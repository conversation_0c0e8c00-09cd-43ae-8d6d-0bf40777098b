<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.titc.pms.member.dal.dao.MemberOrderPayInfoDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo">
        <result column="member_order_no" property="memberOrderNo" />
        <result column="member_order_pay_no" property="memberOrderPayNo" />
        <result column="member_no" property="memberNo" />
        <result column="master_type" property="masterType" />
        <result column="master_code" property="masterCode" />
        <result column="club_code" property="clubCode" />
        <result column="bloc_code" property="blocCode" />
        <result column="hotel_code" property="hotelCode" />
        <result column="member_scene" property="memberScene" />
        <result column="order_pay_state" property="orderPayState" />
        <result column="payee_master_type" property="payeeMasterType" />
        <result column="payee_master_code" property="payeeMasterCode" />
        <result column="payee_master_name" property="payeeMasterName" />
        <result column="pay_channel" property="payChannel" />
        <result column="pay_product" property="payProduct" />
        <result column="pay_vendor" property="payVendor" />
        <result column="pay_type" property="payType" />
        <result column="term_id" property="termId" />
        <result column="card_no" property="cardNo" />
        <result column="fail_reason" property="failReason" />
        <result column="payed_time" property="payedTime" />
        <result column="online_pay_no" property="onlinePayNo" />
        <result column="transaction_id" property="transactionId" />
        <result column="item_code" property="itemCode" />
        <result column="item_name" property="itemName" />
        <result column="account_item_no" property="accountItemNo" />
        <result column="account_no" property="accountNo" />
        <result column="account_Name" property="accountName" />
        <result column="create_user" property="createUser" />
        <result column="modify_user" property="modifyUser" />
        <result column="is_delete" property="isDelete" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        member_order_no, member_no, master_type, master_code, club_code, bloc_code, hotel_code, member_scene, order_pay_state, payee_master_type, payee_master_code, payee_master_name, pay_channel, pay_product, pay_vendor, pay_type, term_id, card_no, fail_reason, payed_time, online_pay_no, transaction_id, create_user, modify_user, is_delete, gmt_create, gmt_modified
    </sql>

</mapper>
