<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.titc.pms.member.dal.dao.MemberOrderRechargeSceneInfoDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.titc.pms.member.dal.entity.po.MemberOrderRechargeSceneInfo">
        <result column="member_order_no" property="memberOrderNo" />
        <result column="member_no" property="memberNo" />
        <result column="master_type" property="masterType" />
        <result column="master_code" property="masterCode" />
        <result column="club_code" property="clubCode" />
        <result column="bloc_code" property="blocCode" />
        <result column="hotel_code" property="hotelCode" />
        <result column="capital_amount" property="capitalAmount" />
        <result column="gift_amount" property="giftAmount" />
        <result column="gift_expire_date" property="giftExpireDate" />
        <result column="create_user" property="createUser" />
        <result column="modify_user" property="modifyUser" />
        <result column="is_delete" property="isDelete" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        member_order_no, member_no, master_type, master_code, club_code, bloc_code, hotel_code, capital_amount, gift_amount, gift_expire_date, create_user, modify_user, is_delete, gmt_create, gmt_modified
    </sql>

</mapper>
