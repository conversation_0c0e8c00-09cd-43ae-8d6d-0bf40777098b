<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.titc.pms.member.dal.dao.MemberProfileTagInfoDao">
    <insert id="batchAdd">
        insert into member_profile_tag_info(
        tag_no,
        member_no,
        tag_type,
        mark_type,
        tag_id,
        tag_name,
        sort,
        create_user,
        modify_user
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.tagNo},
            #{item.memberNo},
            #{item.tagType},
            #{item.markType},
            #{item.tagId},
            #{item.tagName},
            #{item.sort},
            #{item.createUser},
            #{item.modifyUser}
            )
        </foreach>
    </insert>
    <select id="listMemberTagStatistics"
            resultType="com.ly.titc.pms.member.dal.entity.bo.MemberTagStatisticsBo">
        SELECT tag_id as tagId, count(member_no) as `count`  FROM `member_profile_tag_info` where tag_id in
        <foreach collection="list" separator="," item="tagId" open="(" close=")">
            #{tagId}
        </foreach>
        group by tag_id
    </select>
</mapper>