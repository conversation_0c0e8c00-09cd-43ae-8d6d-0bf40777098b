<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.titc.pms.member.dal.dao.MemberCardInfoDao">
    <update id="updateBatch">
        update member_card_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="card_level =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when member_card_no=#{item.memberCardNo} then #{item.cardLevel}
                </foreach>
            </trim>
            <trim prefix="effect_begin_date =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when member_card_no=#{item.memberCardNo} then #{item.effectBeginDate}
                </foreach>
            </trim>
            <trim prefix="effect_end_date =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when member_card_no=#{item.memberCardNo} then #{item.effectEndDate}
                </foreach>
            </trim>
            <trim prefix="is_long_term =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when member_card_no=#{item.memberCardNo} then #{item.isLongTerm}
                </foreach>
            </trim>
            <trim prefix="last_level_change_date =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when member_card_no=#{item.memberCardNo} then #{item.lastLevelChangeDate}
                </foreach>
            </trim>
            <trim prefix="sort =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when member_card_no=#{item.memberCardNo} then #{item.sort}
                </foreach>
            </trim>
            <trim prefix="modify_user =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when member_card_no=#{item.memberCardNo} then #{item.modifyUser}
                </foreach>
            </trim>
        </trim>
        where master_type = #{masterType} and master_code = #{masterCode} and
        member_card_no in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item.memberCardNo}
        </foreach>
    </update>
</mapper>