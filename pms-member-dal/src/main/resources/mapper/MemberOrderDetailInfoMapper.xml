<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.titc.pms.member.dal.dao.MemberOrderDetailInfoDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo">
        <result column="member_order_no" property="memberOrderNo" />
        <result column="member_no" property="memberNo" />
        <result column="master_type" property="masterType" />
        <result column="master_code" property="masterCode" />
        <result column="club_code" property="clubCode" />
        <result column="bloc_code" property="blocCode" />
        <result column="hotel_code" property="hotelCode" />
        <result column="member_scene" property="memberScene" />
        <result column="member_scene_note" property="memberSceneNote" />
        <result column="activity_code" property="activityCode" />
        <result column="activity_name" property="activityName" />
        <result column="activity_cost" property="activityCost" />
        <result column="gift_pack" property="giftPack" />
        <result column="create_user" property="createUser" />
        <result column="modify_user" property="modifyUser" />
        <result column="is_delete" property="isDelete" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        member_order_no, member_no, master_type, master_code, club_code, bloc_code, hotel_code, member_scene, member_scene_note, activity_code, activity_name, activity_cost, gift_pack, create_user, modify_user, is_delete, gmt_create, gmt_modified
    </sql>

</mapper>
