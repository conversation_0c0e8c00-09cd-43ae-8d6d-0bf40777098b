<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.titc.pms.member.dal.dao.MemberInfoDao">
    <select id="pageMember" resultType="java.lang.String">
        SELECT
            mi.member_no
        from
            member_info mi
            join member_card_info mci on mci.master_type = mi.master_type and mci.master_code = mi.master_code and mci.member_no = mi.member_no
            left join member_profile_tag_info mpti on mpti.member_no = mi.member_no and mpti.is_delete = 0
        where
            mi.is_delete = 0
          and mci.is_delete = 0
          and mi.master_type = #{param.masterType}
          and mi.master_code = #{param.masterCode}
        <if test="param.realName != null and param.realName != ''">
            and mi.real_name = #{param.realName}
        </if>
        <if test="param.mobile != null and param.mobile != ''">
            and mi.mobile = #{param.mobile}
        </if>
        <if test="param.idType != null">
            and mi.id_type = #{param.idType}
        </if>
        <if test="param.idNo != null and param.idNo != ''">
            and mi.id_no = #{param.idNo}
        </if>
        <if test="param.source != null and param.source != ''">
            and mi.source = #{param.source}
        </if>
        <if test="param.registerHotelType != null and param.registerHotelType != ''">
            and mi.register_hotel_type = #{param.registerHotelType}
        </if>
        <if test="param.registerHotel != null and param.registerHotel != ''">
            and mi.register_hotel = #{param.registerHotel}
        </if>
        <if test="param.state != null">
            and mi.state = #{param.state}
        </if>
        <if test="param.salesman != null and param.salesman != ''">
            and mi.salesman = #{param.salesman}
        </if>
        <if test="param.memberCardNo != null and param.memberCardNo != ''">
            and mci.member_card_no = #{param.memberCardNo}
        </if>
        <if test="param.memberRegisterBeginDate != null and param.memberRegisterBeginDate != ''">
            and mi.gmt_create >= #{param.memberRegisterBeginDate}
        </if>
        <if test="param.memberRegisterEndDate != null and param.memberRegisterEndDate != ''">
            and mi.gmt_create &lt;= #{param.memberRegisterEndDate}
        </if>
        <if test="param.cardLevel != null">
            and mci.card_level = #{param.cardLevel}
        </if>
        <if test="param.tagIds != null and param.tagIds.size > 0">
            AND mpti.tag_id in
            <foreach collection="param.tagIds" separator="," item="tagId" open="(" close=")">
                #{tagId}
            </foreach>
        </if>
        order by mi.member_no desc
    </select>
    <select id="matchMobileName" resultType="com.ly.titc.pms.member.dal.entity.po.MemberInfo">
        SELECT
            master_type,
            master_code,
           member_no,
           real_name,
           mobile,
           id_type,
           id_no,
           source,
           register_hotel_type,
           register_hotel,
           state,
           gmt_create,
           gmt_modified
        from
            member_info
        where
            is_delete = 0
          and master_type = #{masterType}
          and master_code = #{masterCode}
           and state=1
        <if test="mobile != null and mobile != ''">
          and  mobile = #{mobile}
        </if>
        <if test="realName != null and realName != ''">
        and  real_name like concat('%',#{realName},'%')
        </if>
          limit #{size}
    </select>


</mapper>