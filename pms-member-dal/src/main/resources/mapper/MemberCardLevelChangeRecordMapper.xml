<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.titc.pms.member.dal.dao.MemberCardLevelChangeRecordDao">
    <delete id="deleteByMemberNo">
        delete from member_card_level_change_record
               where member_no = #{memberNo}
        <if test="memberCardNo != null and memberCardNo != ''">
            and member_card_no = #{memberCardNo}
        </if>
    </delete>

    <select id="getLatestLevelChangeRecordsByMemberAndType" resultType="com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord">
        SELECT
        member_no,
        member_card_no,
        gmt_create,
        change_type
        FROM (
        SELECT
        member_no,
        member_card_no,
        gmt_create,
        change_type,
        ROW_NUMBER() OVER (PARTITION BY member_no, adjusted_change_type ORDER BY gmt_create DESC) AS rn
        FROM (
        SELECT
        member_no,
        member_card_no,
        gmt_create,
        change_type,
        CASE
        WHEN change_type IN (2, 4) THEN 2
        ELSE change_type
        END AS adjusted_change_type
        FROM
        member_card_level_change_record
        WHERE
        is_delete = 0
        AND master_type = #{masterType}
        AND master_code = #{masterCode}
        AND change_type IN
        <foreach item="item" index="index" collection="changeTypes" open="(" separator="," close=")">
            #{item}
        </foreach>
        ) subquery
        ) t
        WHERE
        t.rn = 1
        ORDER BY
        gmt_create desc;
    </select>
</mapper>