package com.ly.titc.pms.member.dal.dao;

import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.springboot.dcdb.dal.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会员卡信息表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024/10/28 17:03
 */
public interface MemberCardInfoDao extends BaseMapperX<MemberCardInfo> {

    void updateBatch(@Param("masterType") Integer masterType, @Param("masterCode") String masterCode, @Param("list") List<MemberCardInfo> memberCardInfoList);
}
