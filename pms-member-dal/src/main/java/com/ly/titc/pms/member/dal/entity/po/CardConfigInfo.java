package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员卡模版配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("card_config_info")
public class CardConfigInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 卡模版主键ID
     */
    @PrimaryKey(column = "id", value = 1)
    private Long id;

    /**
     * 集团编码（ELONG为空）
     */
    private String blocCode;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 卡代码
     */
    private String cardCode;

    /**
     * 卡类型 1: 基础卡 2 企业卡
     */
    private Integer cardType;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 适用类型 1 集团  2 品牌 3 门店
     */
    private Integer applicationType;

    /**
     * 适用范围 0 默认 1 全部
     */
    private Integer applicationScope;

    /**
     * 手机号是否必填 0 否 1 是
     */
    private Integer mobileInput;

    /**
     * 姓名是否必填 0 否 1 是
     */
    private Integer nameInput;

    /**
     * 证件是否必填 0 否 1 是
     */
    private Integer certificateInput;

    /**
     * 0 否 1是
     */
    private Integer isDefault;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
