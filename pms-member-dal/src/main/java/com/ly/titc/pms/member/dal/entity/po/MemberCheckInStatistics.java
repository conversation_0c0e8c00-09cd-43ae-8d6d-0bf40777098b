package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员入住统计表
 */
@TableName(value = "member_check_in_statistics")
@Data
public class MemberCheckInStatistics {

    /**
     * 会员编号
     */
    @PrimaryKey(column = "member_no", value = 1)
    private String memberNo;

    /**
     * 酒店编码
     */
    @PrimaryKey(column = "hotel_code", value = 2)
    private String hotelCode;

    /**
     * 总入住次数
     */
    private int checkInCount;

    /**
     * 总入住间夜
     */
    private int checkInNights;

    /**
     * 总消费金额
     */
    private BigDecimal expenseAmount;

    /**
     * 间夜均价
     */
    private BigDecimal averagePrice;

    /**
     * 上一次房价
     */
    private BigDecimal lastPrice;

    /**
     * 上一次离店时间
     */
    private String lastCheckOutDate;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    private int isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
