package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 会员卡信息表
 * @TableName member_card_info
 */
@TableName(value ="member_card_info")
@Data
public class MemberCardInfo implements Serializable {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @PrimaryKey(column = "master_type", value = 1)
    private Integer masterType;

    /**
     * 归属值
     */
    @PrimaryKey(column = "master_code", value = 2)
    private String masterCode;

    /**
     * 会员卡号
     */
    @PrimaryKey(column = "member_card_no", value = 3)
    private String memberCardNo;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡类型
     */
    private Integer cardType;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 会员卡生效时间
     */
    private String effectBeginDate;

    /**
     * 会员卡失效时间
     */
    private String effectEndDate;

    /**
     * 是否长期有效 0 否 1 是
     */
    private Integer isLongTerm;

    /**
     * 最近一次等级变更时间
     */
    private LocalDateTime lastLevelChangeDate;

    /**
     * 发放门店类型 集团:BLOC;门店:HOTEL
     */
    private String issueHotelType;

    /**
     * 发放门店(集团编号; 酒店编号)
     */
    private String issueHotel;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}