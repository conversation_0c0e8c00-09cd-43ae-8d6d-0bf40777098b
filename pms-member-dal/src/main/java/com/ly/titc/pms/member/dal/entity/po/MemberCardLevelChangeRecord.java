package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 会员卡等级变化记录表
 *
 * @TableName member_card_level_change_record
 */
@TableName(value ="member_card_level_change_record")
@Data
@Accessors(chain = true)
public class MemberCardLevelChangeRecord implements Serializable {

    /**
     * 变化记录编号
     */
    @PrimaryKey(column = "record_no", value = 1)
    private Long recordNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    private String masterCode;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 变更前等级
     */
    private Integer preLevel;

    /**
     * 变更前等级名称
     */
    private String preLevelName;

    /**
     * 变更后等级
     */
    private Integer afterLevel;

    /**
     * 变更后等级名称
     */
    private String afterLevelName;

    /**
     * 生效时间
     */
    private String effectBeginDate;

    /**
     * 失效时间
     */
    private String effectEndDate;

    /**
     * 等级变化类型，1注册; 2升级；3保级成功；4保级失败; 5手动处理; 6迁移数据
     */
    private Integer changeType;

    /**
     * 升级原因
     */
    private String reason;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 升降级规则id
     */
    private Long ruleId;

    /**
     * 会员卡id
     */
    private Long cardId;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 业务id
     */
    private String bizNo;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}