package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

/**
 * 会员档案-标签表
 *
 * @TableName member_profile_tag_info
 */
@TableName(value ="member_profile_tag_info")
@Data
public class MemberProfileTagInfo implements Serializable {

    /**
     * 会员标签编号
     */
    @PrimaryKey(column = "tag_no", value = 1)
    private Long tagNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 标签分类
     */
    private Integer tagType;

    /**
     * 打标分类: 1:手动标记 2:系统标记
     */
    private Integer markType;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}