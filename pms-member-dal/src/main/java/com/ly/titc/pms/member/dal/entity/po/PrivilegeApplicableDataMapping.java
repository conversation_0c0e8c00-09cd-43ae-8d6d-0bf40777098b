package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员权益适用范围表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("privilege_applicable_data_mapping")
public class PrivilegeApplicableDataMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @PrimaryKey(column = "id", value = 1)
    private Long id;

    /**
     * 权益ID
     */
    private Long privilegeId;

    /**
     * 权益类型 1.仅作展示 2 价格折扣 3 预定保留 4 延迟退房
     */
    private Integer classification;

    /**
     * 适用类型 1 集团 2 品牌 3 门店
     */
    private Integer applicationType;

    /**
     * 适用范围 0 全部 1 部分
     */
    private Integer scopeType;

    /**
     * 适用范围值
     */
    private String scopeValue;

    /**
     * 权益值
     */
    private String value;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
