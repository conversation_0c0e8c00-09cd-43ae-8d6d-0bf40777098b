package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 会员黑名单mapping
 *
 * @Author：rui
 * @name：MemberBlacklistInfo
 * @Date：2024-12-10 14:35
 * @Filename：CustomerBlacklistInfo
 */
@Data
@TableName(value = "member_blacklist_applicable_data_mapping")
public class MemberBlacklistApplicableDataMapping {

    /**
     * 会员黑名单适用范围ID
     */
    @PrimaryKey(column = "id", value = 1)
    private Long id;

    /**
     * 黑名单编号
     */
    private String blacklistNo;

    /**
     * 适用类型 1 渠道 2 场景
     */
    private Integer applicationType;

    /**
     * 归属值
     */
    private String scopeValue;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}
