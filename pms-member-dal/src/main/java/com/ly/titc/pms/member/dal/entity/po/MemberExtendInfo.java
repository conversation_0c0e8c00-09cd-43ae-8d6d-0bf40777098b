package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

/**
 * 会员拓展信息表
 *
 * @TableName member_extend_info
 */
@TableName(value ="member_extend_info")
@Data
public class MemberExtendInfo implements Serializable {
    /**
     * 会员号
     */
    @PrimaryKey(column = "member_no", value = 1)
    private String memberNo;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 民族
     */
    private String nation;

    /**
     * 车牌号
     */
    private String numberPlate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 语言
     */
    private String language;

    /**
     * 销售员
     */
    private String salesman;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}