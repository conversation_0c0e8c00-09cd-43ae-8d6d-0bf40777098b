package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员权益配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("privilege_config_info")
public class PrivilegeConfigInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @PrimaryKey(column = "id", value = 1)
    private Long id;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 权益分类 1 价格权益 2 积分权益 3 线下权益 4 生态权益
     */
    private Integer type;

    /**
     * 权益类型 1.仅作展示 2 价格折扣 3 预定保留 4 延迟退房
     */
    private Integer classification;

    /**
     * 权益名称
     */
    private String name;

    /**
     * 权益单位
     */
    private String unit;

    /**
     * 权益图标
     */
    private String pic;

    /**
     * 权益说明
     */
    private String instruction;

    /**
     * 权益描述
     */
    private String description;

    /**
     * 权益排序
     */
    private Integer sort;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
