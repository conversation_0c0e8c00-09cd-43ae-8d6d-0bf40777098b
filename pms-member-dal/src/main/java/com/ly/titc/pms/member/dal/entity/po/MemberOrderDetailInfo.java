package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 会员订单明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberOrderDetailInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员订单号
     */
    @PrimaryKey(column = "member_order_no", value = 1)
    private String memberOrderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 集团组code
     */
    private String clubCode;

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 会员业务场景
     */
    private String memberScene;

    /**
     * 业务请求体
     */
    private String memberSceneNote;

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 档位代码
     */
    private String gearCode;

    /**
     * 活动成本
     */
    private String activityCost;

    /**
     * 礼包信息JSON
     */
    private String giftPack;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;


}
