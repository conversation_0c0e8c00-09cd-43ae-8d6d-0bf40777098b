package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员资产使用模式表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("asset_usage_mode_scope_mapping")
public class AssetUsageModeScopeMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @PrimaryKey(column = "id", value = 1)
    private Long id;

    /**
     * 资产类型 store 储值，point 积分
     */
    private String assetType;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 规则使用模式的主体类型  1:集团 2:门店 3:酒馆
     */
    private Integer usageMasterType;

    /**
     * 规则使用模式的主体编码  集团编码 门店编码 ELONG
     */
    private String usageMasterCode;

    /**
     * 酒馆组编码
     */
    private String clubCode;

    /**
     * 集团编码
     */
    private String blocCode;

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 规则模式：SINGLE ：唯一  MULTI：混合（冗余）
     */
    private String ruleMode;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
