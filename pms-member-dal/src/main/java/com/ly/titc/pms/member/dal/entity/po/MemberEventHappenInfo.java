package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：MemberEventHappenInfoDto
 * @Date：2024-12-10 15:56
 * @Filename：MemberEventHappenInfoDto
 */
@Data
@TableName(value = "`member_event_happen_info")
public class MemberEventHappenInfo {

    /**
     * 事件号
     */
    @PrimaryKey(column = "event_no", value = 1)
    private String eventNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 事件
     */
    private String event;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
