package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

/**
 * 会员档案-常用地址表
 *
 * @TableName member_profile_address_info
 */
@TableName(value ="member_profile_address_info")
@Data
public class MemberProfileAddressInfo implements Serializable {
    /**
     * 常用地址编号
     */
    @PrimaryKey(column = "address_no", value = 1)
    private Long addressNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 国家id
     */
    private Long countryId;

    /**
     * 国家
     */
    private String country;

    /**
     * 省id
     */
    private Long provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域id
     */
    private Long districtId;

    /**
     * 区域
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 标签
     */
    private String tag;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}