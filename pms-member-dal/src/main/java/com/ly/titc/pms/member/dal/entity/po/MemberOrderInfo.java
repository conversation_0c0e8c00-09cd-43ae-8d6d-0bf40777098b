package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <p>
 * 会员订单主表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberOrderInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员订单号
     */
    @PrimaryKey(column = "member_order_no", value = 1)
    private String memberOrderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 集团组code
     */
    private String clubCode;

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 会员业务场景
     */
    private String memberScene;

    /**
     * 会员业务场景描述
     */
    private String memberSceneDesc;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 单价(不变) 保留到分
     */
    private BigDecimal price;

    /**
     * 数量（新增）
     */
    private Integer num;

    /**
     * 金额（保留到分）
     */
    private BigDecimal amount;
    /**
     * 售价类型
     */
    private String amountType;

    /**
     * 订单状态 1 待支付 2 支付成功业务处理成功 3 支付失败 4 交易关闭 5.支付成功业务处理失败 6 部分退款 7 全额退款
     */
    private Integer orderState;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;


}
