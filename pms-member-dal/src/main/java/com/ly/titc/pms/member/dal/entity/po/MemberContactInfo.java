package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

/**
 * 会员联系方式信息表
 *
 * @TableName member_contact_info
 */
@TableName(value ="member_contact_info")
@Data
public class MemberContactInfo implements Serializable {
    /**
     * 会员号
     *
     */
    @PrimaryKey(column = "member_no", value = 1)
    private String memberNo;

    /**
     * 邮箱
     */
    private String email;

    /**
     * QQ号
     */
    private String qq;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 国家id
     */
    private Integer countryId;

    /**
     * 国家
     */
    private String country;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域id
     */
    private Integer districtId;

    /**
     * 区域
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}