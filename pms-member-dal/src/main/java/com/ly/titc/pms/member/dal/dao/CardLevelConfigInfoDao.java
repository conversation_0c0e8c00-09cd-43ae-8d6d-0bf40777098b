package com.ly.titc.pms.member.dal.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.pms.member.dal.entity.po.CardLevelConfigInfo;
import com.ly.titc.springboot.dcdb.dal.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 卡等级信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
public interface CardLevelConfigInfoDao extends BaseMapperX<CardLevelConfigInfo> {

    Page<CardLevelConfigInfo> page(Page<CardLevelConfigInfo> page, @Param("cardId") Long cardId, @Param("cardLevel") Integer cardLevel, @Param("state") Integer state);
}
