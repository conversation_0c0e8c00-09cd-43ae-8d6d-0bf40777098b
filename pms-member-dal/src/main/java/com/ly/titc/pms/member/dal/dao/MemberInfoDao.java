package com.ly.titc.pms.member.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.pms.member.dal.entity.bo.PageMemberParamBo;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.springboot.dcdb.dal.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会员信息表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024/10/28 17:03
 */
public interface MemberInfoDao extends BaseMapperX<MemberInfo> {

    /**
     * 分页查询会员
     *
     * @param pageMemberParamBo
     * @param page
     * @return
     */
    IPage<String> pageMember(@Param("param") PageMemberParamBo pageMemberParamBo, Page<String> page);


    List<MemberInfo> matchMobileName(@Param("mobile") String mobile, @Param("realName") String realName,@Param("masterType") Integer masterType,@Param("masterCode") String masterCode, @Param("size")int size);
}
