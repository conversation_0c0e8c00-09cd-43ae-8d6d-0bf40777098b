package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

/**
 * 会员卡号生成记录表
 *
 * @TableName member_card_no_generate_record
 */
@TableName(value ="member_card_no_generate_record")
@Data
public class MemberCardNoGenerateRecord implements Serializable {
    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @PrimaryKey(column = "master_type", value = 1)
    private Integer masterType;

    /**
     * 归属值
     */
    @PrimaryKey(column = "master_code", value = 2)
    private String masterCode;

    /**
     * 会员卡ID
     */
    @PrimaryKey(column = "card_id", value = 3)
    private Long cardId;

    /**
     * 会员卡尾数
     */
    private Long endNum;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}