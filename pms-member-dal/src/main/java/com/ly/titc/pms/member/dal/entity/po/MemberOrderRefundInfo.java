package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 会员订单退款表
 * @TableName member_order_refund_info
 */
@TableName(value ="member_order_refund_info")
@Data
public class MemberOrderRefundInfo implements Serializable {

    /**
     * 集团组编号
     */
    private String clubCode;

    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 酒店编号
     */
    private String hotelCode;

    /**
     * 退款单号
     */
    @PrimaryKey(column = "member_refund_no", value = 1)
    private String memberRefundNo;

    /**
     * 会员订单号
     */
    private String memberOrderNo;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员业务场景
     */
    private String memberScene;

    /**
     * 退款状态 1 退款中 2 退款完成 3 退款失败 4 退款关闭
     */
    private Integer refundState;

    /**
     * 退款金额（到分）
     */
    private BigDecimal refundAmount;
    /**
     * 支付路由 hotel_Cashier 酒店收银台  bloc_Cashier 集团收银台
     */
    private String payRoute;

    /**
     * 支付单号
     */
    private String memberOrderPayNo;

    /**
     * 收银台退款交易号
     */
    private String onlineRefundPayNo;

    /**
     * 渠道退款交易号
     */
    private String transactionId;

    /**
     * 退款原因
     */
    private String reason;

    /**
     * 退款失败原因
     */
    private String failReason;

    /**
     * 终端ID
     */
    private String termId;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}