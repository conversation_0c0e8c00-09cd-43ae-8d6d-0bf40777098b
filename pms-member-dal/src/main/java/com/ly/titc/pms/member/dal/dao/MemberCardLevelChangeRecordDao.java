package com.ly.titc.pms.member.dal.dao;

import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.springboot.dcdb.dal.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会员卡等级变化记录表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024/10/28 17:03
 */
public interface MemberCardLevelChangeRecordDao extends BaseMapperX<MemberCardLevelChangeRecord> {

    /**
     * 删除会员卡等级变更记录
     *
     * @param memberNo
     * @param memberCardNo
     */
    void deleteByMemberNo(@Param("memberNo") String memberNo, @Param("memberCardNo") String memberCardNo);

    List<MemberCardLevelChangeRecord> getLatestLevelChangeRecordsByMemberAndType(@Param("masterType") Integer masterType, @Param("masterCode") String masterCode, @Param("changeTypes") List<Integer> changeTypes);

}
