package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import com.ly.titc.springboot.dcdb.dal.core.handler.EncryptTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会员信息表
 *
 * @TableName member_info
 */
@TableName(value ="member_info", autoResultMap = true)
@Data
public class MemberInfo implements Serializable {

    /**
     * 集团编码
     */
    private String blocCode;

    /**
     * 会员号
     */
    @PrimaryKey(column = "member_no", value = 1)
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 英文名
     */
    private String enName;

    /**
     * 手机号
     */
    @TableField(value = "mobile", typeHandler = EncryptTypeHandler.class)
    private String mobile;

    /**
     * 证件号分类
     */
    private Integer idType;

    /**
     * 证件号
     */
    @TableField(value = "id_no", typeHandler = EncryptTypeHandler.class)
    private String idNo;

    /**
     * 性别 1:男;2:女;3:保密
     */
    private Integer gender;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 自定义会员号
     */
    private String customizeMemberNo;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 来源
     */
    private String source;

    /**
     * 注册门店类型 集团:BLOC;门店:HOTEL
     */
    private String registerHotelType;

    /**
     * 注册门店(集团编号; 酒店编号)
     */
    private String registerHotel;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}