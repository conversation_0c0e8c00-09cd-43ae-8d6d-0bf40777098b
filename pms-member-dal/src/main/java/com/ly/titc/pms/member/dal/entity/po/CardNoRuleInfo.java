package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员卡模版配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("card_no_rule_info")
public class CardNoRuleInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员卡号生成规则ID
     */
    @PrimaryKey(column = "id", value = 1)
    private Long id;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 卡号前缀
     */
    private String cardPrefix;

    /**
     * 卡号长度
     */
    private Integer cardLength;

    /**
     * 排除的数字（分号分隔）
     */
    private String excludeNumber;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
