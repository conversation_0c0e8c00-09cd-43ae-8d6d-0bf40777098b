//package com.ly.titc.pms.member.gateway.controller.member;
//
//import com.ly.titc.common.entity.Response;
//import com.ly.titc.pms.member.dubbo.entity.request.member.IssueMemberCardManualRequest;
//import com.ly.titc.pms.member.gateway.entity.response.member.IssueMemberCardManualResponse;
//import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardRequestDto;
//import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardResultDto;
//import com.ly.titc.pms.member.mediator.service.MemberMedService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import javax.validation.Valid;
//
///**
// * 会员卡手动发放控制器
// *
// * <AUTHOR>
// * @date 2024/12/25 10:00
// */
//@Slf4j
//@RestController
//@RequestMapping("/member/card/manual")
//public class MemberCardManualController {
//
//    @Resource
//    private MemberMedService memberMedService;
//
//    @Resource
//    private MemberCardManualConverter memberCardManualConverter;
//
//    /**
//     * 发放会员卡
//     *
//     * @param request
//     * @return
//     */
//    @PostMapping("/issue")
//    public Response<IssueMemberCardManualResponse> issueMemberCard(@RequestBody @Valid IssueMemberCardManualRequest request) {
//        try {
//            // 转换请求参数
//            IssueMemberCardRequestDto dto = memberCardManualConverter.convertToDto(request);
//            // 调用服务
//            IssueMemberCardResultDto result = memberMedService.issueMemberCard(dto);
//            // 转换响应
//            IssueMemberCardManualResponse response = memberCardManualConverter.convertToResponse(result);
//            return Response.success(response);
//        } catch (Exception e) {
//            log.error("发放会员卡失败", e);
//            IssueMemberCardManualResponse response = new IssueMemberCardManualResponse();
//            response.setSuccess(false);
//            response.setFailureReason("系统异常：" + e.getMessage());
//            return Response.success(response);
//        }
//    }
//}
