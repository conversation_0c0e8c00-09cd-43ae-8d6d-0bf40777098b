package com.ly.titc.pms.member.gateway.entity.response.member;

import lombok.Data;

/**
 * 手动发放会员卡响应
 *
 * <AUTHOR>
 * @date 2024/12/25 10:00
 */
@Data
public class IssueMemberCardManualResponse {

    /**
     * 是否发放成功
     */
    private boolean success;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 发放的会员卡等级
     */
    private Integer cardLevel;

    /**
     * 发放的会员卡等级名称
     */
    private String cardLevelName;

    /**
     * 操作类型（REGISTER: 注册会员, UPGRADE: 会员升级）
     */
    private String operationType;

}
