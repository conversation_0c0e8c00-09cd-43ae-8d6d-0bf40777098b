<?xml version="1.0" encoding="UTF-8"?>
<!-- ====================================================================== -->
<!--                                                                        -->
<!-- Generated by Maven Help Plugin                                         -->
<!-- See: https://maven.apache.org/plugins/maven-help-plugin/               -->
<!--                                                                        -->
<!-- ====================================================================== -->
<!-- ====================================================================== -->
<!--                                                                        -->
<!-- Effective POM for project 'com.ly.titc:pms-member-gateway:jar:1.0.0'   -->
<!--                                                                        -->
<!-- ====================================================================== -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ly.titc</groupId>
    <artifactId>pms-member-parent</artifactId>
    <version>1.0.0</version>
  </parent>
  <groupId>com.ly.titc</groupId>
  <artifactId>pms-member-gateway</artifactId>
  <version>1.0.0</version>
  <properties>
    <cashier-interfaces.version>1.0.3-SNAPSHOT</cashier-interfaces.version>
    <cdm-interfaces.version>1.0.0-SNAPSHOT</cdm-interfaces.version>
    <chm-api.version>1.1.6-SNAPSHOT</chm-api.version>
    <com-component-interfaces.version>1.0.0-SNAPSHOT</com-component-interfaces.version>
    <com-component-sdk.version>1.0.3-SNAPSHOT</com-component-sdk.version>
    <com.common.version>1.0.10-SNAPSHOT</com.common.version>
    <com.ly.dal.version>3.6.6</com.ly.dal.version>
    <ehr-api.version>1.0.3-SNAPSHOT</ehr-api.version>
    <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
    <lombok.version>1.18.20</lombok.version>
    <mapstruct.version>1.4.2.Final</mapstruct.version>
    <mdm-api.version>1.1.3-SNAPSHOT</mdm-api.version>
    <oauth-client.version>1.0.6-SNAPSHOT</oauth-client.version>
    <package.environment>test</package.environment>
    <pms-account-interfaces.version>1.0.5-SNAPSHOT</pms-account-interfaces.version>
    <pms-customer-interfaces.version>1.0.0-SNAPSHOT</pms-customer-interfaces.version>
    <pms-ecrm-interfaces.version>1.0.1-SNAPSHOT</pms-ecrm-interfaces.version>
    <pms-member-asset-interfaces.version>1.0.0-SNAPSHOT</pms-member-asset-interfaces.version>
    <pms-member-interfaces.version>1.0.1-SNAPSHOT</pms-member-interfaces.version>
    <pms-spm-interfaces.version>1.0.4-SNAPSHOT</pms-spm-interfaces.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <spring-boot-dependencies.version>1.0.2-SNAPSHOT</spring-boot-dependencies.version>
    <springboot-mq.version>2.0.3-SNAPSHOT</springboot-mq.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>mdm-api</artifactId>
        <version>1.1.3-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>cdm-interfaces</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-member-interfaces</artifactId>
        <version>1.0.1-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-ecrm-interfaces</artifactId>
        <version>1.0.1-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-member-asset-interfaces</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>com-component-interfaces</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>com-component-sdk</artifactId>
        <version>1.0.3-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>common</artifactId>
        <version>1.0.10-SNAPSHOT</version>
        <exclusions>
          <exclusion>
            <groupId>com.ly.dsf</groupId>
            <artifactId>dsf-spring-boot-starter</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>oauth-client</artifactId>
        <version>1.0.6-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>ehr-api</artifactId>
        <version>1.0.3-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>cashier-interfaces</artifactId>
        <version>1.0.3-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-account-interfaces</artifactId>
        <version>1.0.5-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-spm-interfaces</artifactId>
        <version>1.0.4-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-customer-interfaces</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>springboot-start-dcdb-dal</artifactId>
        <version>1.0.1-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>chm-api</artifactId>
        <version>1.1.6-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-websocket</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-rabbit</artifactId>
        <version>2.4.17</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-redis</artifactId>
        <version>2.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webflux</artifactId>
        <version>5.3.7</version>
      </dependency>
      <dependency>
        <groupId>com.github.lianjiatech</groupId>
        <artifactId>retrofit-spring-boot-starter</artifactId>
        <version>2.2.21</version>
      </dependency>
      <dependency>
        <groupId>com.ly.dsf</groupId>
        <artifactId>dsf-spring-boot-starter</artifactId>
        <version>1.3.7</version>
      </dependency>
      <dependency>
        <groupId>com.ly.dsf</groupId>
        <artifactId>dsf-client</artifactId>
        <version>2.6.7</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-spring-boot-starter</artifactId>
        <version>3.0.11</version>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo</artifactId>
        <version>3.0.11</version>
      </dependency>
      <dependency>
        <groupId>com.ly.dsf</groupId>
        <artifactId>dubbo-dsf-extensions-all</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>caffeine</artifactId>
        <version>2.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>29.0-jre</version>
      </dependency>
      <dependency>
        <groupId>com.google.inject</groupId>
        <artifactId>guice</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>2.0.32</version>
        <exclusions>
          <exclusion>
            <groupId>com.alibaba</groupId>
            <artifactId>jconsole</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.alibaba</groupId>
            <artifactId>tools</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>1.18.20</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjrt</artifactId>
        <version>1.9.8</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjweaver</artifactId>
        <version>1.9.8</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>4.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.7.32</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-to-slf4j</artifactId>
        <version>2.15.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>2.15.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>2.15.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jul</artifactId>
        <version>2.15.0</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
        <version>1.4.2.Final</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok-mapstruct-binding</artifactId>
        <version>0.2.0</version>
      </dependency>
      <dependency>
        <groupId>joda-time</groupId>
        <artifactId>joda-time</artifactId>
        <version>2.9</version>
      </dependency>
      <dependency>
        <groupId>net.coobird</groupId>
        <artifactId>thumbnailator</artifactId>
        <version>0.4.8</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>3.14.9</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>3.9</version>
      </dependency>
      <dependency>
        <groupId>cn.afterturn</groupId>
        <artifactId>easypoi-base</artifactId>
        <version>4.1.0</version>
      </dependency>
      <dependency>
        <groupId>cn.afterturn</groupId>
        <artifactId>easypoi-annotation</artifactId>
        <version>4.1.0</version>
      </dependency>
      <dependency>
        <groupId>commons-httpclient</groupId>
        <artifactId>commons-httpclient</artifactId>
        <version>3.1</version>
      </dependency>
      <dependency>
        <groupId>jaxen</groupId>
        <artifactId>jaxen</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>easyexcel</artifactId>
        <version>3.1.1</version>
      </dependency>
      <dependency>
        <groupId>com.github.shalousun</groupId>
        <artifactId>smart-doc</artifactId>
        <version>2.2.1</version>
      </dependency>
      <dependency>
        <groupId>dom4j</groupId>
        <artifactId>dom4j</artifactId>
        <version>1.6.1</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>2.12.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.12.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>2.12.3</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.nacos</groupId>
        <artifactId>nacos-client</artifactId>
        <version>2.1.2</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>springboot-start-dal</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>springboot-start-dal-plus</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>springboot-start-dynamic-dal</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>springboot-start-mq</artifactId>
        <version>2.0.3-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>springboot-start-elasticsearch</artifactId>
        <version>1.0.1-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>springboot-start-redisson</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>springboot-start-concept-connection</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>elasticsearch-client</artifactId>
        <version>1.0.1-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.zkt</groupId>
        <artifactId>wx-proxy-miniapp</artifactId>
        <version>1.0.23-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.zkt</groupId>
        <artifactId>wx-proxy-pay</artifactId>
        <version>1.0.23-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.zkt</groupId>
        <artifactId>wx-proxy-mp</artifactId>
        <version>1.0.23-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.yeepay.g3.yop</groupId>
        <artifactId>yop-java-sdk</artifactId>
        <version>3.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.ly.tcbase</groupId>
        <artifactId>configcenterclient</artifactId>
        <version>6.2.8</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.ly.tcbase</groupId>
        <artifactId>cache</artifactId>
        <version>3.7.0</version>
      </dependency>
      <dependency>
        <groupId>io.lettuce</groupId>
        <artifactId>lettuce-core</artifactId>
        <version>5.2.1.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.ly.dal</groupId>
        <artifactId>dal-new</artifactId>
        <version>3.6.7</version>
      </dependency>
      <dependency>
        <groupId>com.ly.turbomq</groupId>
        <artifactId>turbomq-client</artifactId>
        <version>4.2.5</version>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-s3</artifactId>
        <version>1.12.139</version>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-core</artifactId>
        <version>1.12.139</version>
      </dependency>
      <dependency>
        <groupId>com.tc</groupId>
        <artifactId>img-uploader</artifactId>
        <version>1.1.6</version>
      </dependency>
      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>5.1.38</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>1.1.23</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis.spring.boot</groupId>
        <artifactId>mybatis-spring-boot-starter</artifactId>
        <version>2.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.5.3.1</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-extension</artifactId>
        <version>3.5.3.1</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis</artifactId>
        <version>3.5.2</version>
      </dependency>
      <dependency>
        <groupId>com.github.pagehelper</groupId>
        <artifactId>pagehelper-spring-boot-starter</artifactId>
        <version>2.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-generator</artifactId>
        <version>3.5.3.1</version>
      </dependency>
      <dependency>
        <groupId>org.powermock</groupId>
        <artifactId>powermock-module-junit4</artifactId>
        <version>2.0.9</version>
      </dependency>
      <dependency>
        <groupId>org.powermock</groupId>
        <artifactId>powermock-api-mockito2</artifactId>
        <version>2.0.9</version>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson</artifactId>
        <version>3.36.0</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>redisson-client</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>4.6.5</version>
      </dependency>
      <dependency>
        <groupId>org.javassist</groupId>
        <artifactId>javassist</artifactId>
        <version>3.28.0-GA</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-amqp</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-blueprint</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-broker</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-camel</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-client</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-console</artifactId>
        <version>5.15.12</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-http</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jaas</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jdbc-store</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jms-pool</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-kahadb-store</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-karaf</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-leveldb-store</artifactId>
        <version>5.15.12</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-log4j-appender</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-mqtt</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-openwire-generator</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-openwire-legacy</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-osgi</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-partition</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-pool</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-ra</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-run</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-runtime-config</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-shiro</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-spring</artifactId>
        <version>5.15.12</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-stomp</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-web</artifactId>
        <version>5.15.12</version>
      </dependency>
      <dependency>
        <groupId>antlr</groupId>
        <artifactId>antlr</artifactId>
        <version>2.7.7</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-api-1.0-sdk</artifactId>
        <version>1.9.80</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-amqp-protocol</artifactId>
        <version>2.12.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-commons</artifactId>
        <version>2.12.0</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-core-client</artifactId>
        <version>2.12.0</version>
        <exclusions>
          <exclusion>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-json_1.0_spec</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jms-client</artifactId>
        <version>2.12.0</version>
        <exclusions>
          <exclusion>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-json_1.0_spec</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jms-server</artifactId>
        <version>2.12.0</version>
        <exclusions>
          <exclusion>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-json_1.0_spec</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-journal</artifactId>
        <version>2.12.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-selector</artifactId>
        <version>2.12.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-server</artifactId>
        <version>2.12.0</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-json_1.0_spec</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-service-extensions</artifactId>
        <version>2.12.0</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjtools</artifactId>
        <version>1.9.5</version>
      </dependency>
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>3.16.1</version>
      </dependency>
      <dependency>
        <groupId>com.atomikos</groupId>
        <artifactId>transactions-jdbc</artifactId>
        <version>4.0.6</version>
      </dependency>
      <dependency>
        <groupId>com.atomikos</groupId>
        <artifactId>transactions-jms</artifactId>
        <version>4.0.6</version>
      </dependency>
      <dependency>
        <groupId>com.atomikos</groupId>
        <artifactId>transactions-jta</artifactId>
        <version>4.0.6</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility</artifactId>
        <version>4.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility-groovy</artifactId>
        <version>4.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility-kotlin</artifactId>
        <version>4.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility-scala</artifactId>
        <version>4.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.btm</groupId>
        <artifactId>btm</artifactId>
        <version>2.1.4</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy</artifactId>
        <version>1.10.10</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy-agent</artifactId>
        <version>1.10.10</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>guava</artifactId>
        <version>2.8.2</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>jcache</artifactId>
        <version>2.8.2</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>simulator</artifactId>
        <version>2.8.2</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-core</artifactId>
        <version>4.6.1</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.fasterxml</groupId>
        <artifactId>classmate</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>1.14</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-dbcp2</artifactId>
        <version>2.7.0</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>commons-pool</groupId>
        <artifactId>commons-pool</artifactId>
        <version>1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
        <version>2.8.0</version>
      </dependency>
      <dependency>
        <groupId>com.couchbase.client</groupId>
        <artifactId>java-client</artifactId>
        <version>3.0.4</version>
      </dependency>
      <dependency>
        <groupId>com.ibm.db2</groupId>
        <artifactId>jcc</artifactId>
        <version>11.5.0.0</version>
      </dependency>
      <dependency>
        <groupId>io.spring.gradle</groupId>
        <artifactId>dependency-management-plugin</artifactId>
        <version>1.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derby</artifactId>
        <version>10.14.2.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbyclient</artifactId>
        <version>10.14.2.0</version>
      </dependency>
      <dependency>
        <groupId>net.sf.ehcache</groupId>
        <artifactId>ehcache</artifactId>
        <version>2.10.6</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache-clustered</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache-transactions</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch</groupId>
        <artifactId>elasticsearch</artifactId>
        <version>7.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>transport</artifactId>
        <version>7.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-client</artifactId>
        <version>7.6.2</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-high-level-client</artifactId>
        <version>7.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.distribution.integ-test-zip</groupId>
        <artifactId>elasticsearch</artifactId>
        <version>7.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.plugin</groupId>
        <artifactId>transport-netty4-client</artifactId>
        <version>7.6.2</version>
      </dependency>
      <dependency>
        <groupId>de.flapdoodle.embed</groupId>
        <artifactId>de.flapdoodle.embed.mongo</artifactId>
        <version>2.2.0</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-core</artifactId>
        <version>6.4.1</version>
      </dependency>
      <dependency>
        <groupId>org.freemarker</groupId>
        <artifactId>freemarker</artifactId>
        <version>2.3.30</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish</groupId>
        <artifactId>jakarta.el</artifactId>
        <version>3.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>codemodel</artifactId>
        <version>2.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>codemodel-annotation-compiler</artifactId>
        <version>2.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-jxc</artifactId>
        <version>2.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-runtime</artifactId>
        <version>2.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-xjc</artifactId>
        <version>2.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>txw2</artifactId>
        <version>2.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>txwc2</artifactId>
        <version>2.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>xsom</artifactId>
        <version>2.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-ant</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-backports-compat23</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-bsf</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-cli-commons</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-cli-picocli</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-console</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-datetime</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-dateutil</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-docgenerator</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-groovydoc</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-groovysh</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-jaxb</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-jmx</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-json</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-json-direct</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-jsr223</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-macro</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-nio</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-servlet</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-sql</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-swing</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-templates</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-test</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-test-junit5</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-testng</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-xml</artifactId>
        <version>2.5.11</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>2.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <version>1.4.200</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-core</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-library</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast</artifactId>
        <version>3.12.7</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast-client</artifactId>
        <version>3.12.7</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast-spring</artifactId>
        <version>3.12.7</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast-hibernate52</artifactId>
        <version>1.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast-hibernate53</artifactId>
        <version>1.3.2</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-c3p0</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-core</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-ehcache</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-entitymanager</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-envers</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-hikaricp</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-java8</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-jcache</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-jpamodelgen</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-proxool</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-spatial</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-testing</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-vibur</artifactId>
        <version>5.4.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator</artifactId>
        <version>6.1.5.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator-annotation-processor</artifactId>
        <version>6.1.5.Final</version>
      </dependency>
      <dependency>
        <groupId>com.zaxxer</groupId>
        <artifactId>HikariCP</artifactId>
        <version>3.4.5</version>
      </dependency>
      <dependency>
        <groupId>org.hsqldb</groupId>
        <artifactId>hsqldb</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>net.sourceforge.htmlunit</groupId>
        <artifactId>htmlunit</artifactId>
        <version>2.40.0</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpasyncclient</artifactId>
        <version>4.1.4</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>fluent-hc</artifactId>
        <version>4.5.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>4.5.12</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient-cache</artifactId>
        <version>4.5.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient-osgi</artifactId>
        <version>4.5.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient-win</artifactId>
        <version>4.5.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpmime</artifactId>
        <version>4.5.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>4.4.13</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore-nio</artifactId>
        <version>4.4.13</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-api</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-jdbc</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-jpa</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-remote</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-rest</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-rocksdb</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cdi-common</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cdi-embedded</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cdi-remote</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-client-hotrod</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-client-rest</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-clustered-counter</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-clustered-lock</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-commons</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-component-annotations</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-core</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-directory-provider</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-hibernate-cache-v53</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-jboss-marshalling</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-jcache</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-jcache-commons</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-jcache-remote</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-key-value-store-client</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-lucene-directory</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-objectfilter</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-osgi</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-persistence-soft-index</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-query</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-query-core</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-query-dsl</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-remote-query-client</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-remote-query-server</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-scripting</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-core</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-hotrod</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-memcached</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-rest</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-router</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring5-common</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring5-embedded</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring5-remote</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-tasks</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-tasks-api</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-tools</artifactId>
        <version>10.1.8.Final</version>
      </dependency>
      <dependency>
        <groupId>org.influxdb</groupId>
        <artifactId>influxdb-java</artifactId>
        <version>2.18</version>
      </dependency>
      <dependency>
        <groupId>com.sun.activation</groupId>
        <artifactId>jakarta.activation</artifactId>
        <version>1.2.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.activation</groupId>
        <artifactId>jakarta.activation-api</artifactId>
        <version>1.2.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.annotation</groupId>
        <artifactId>jakarta.annotation-api</artifactId>
        <version>1.3.5</version>
      </dependency>
      <dependency>
        <groupId>jakarta.jms</groupId>
        <artifactId>jakarta.jms-api</artifactId>
        <version>2.0.3</version>
      </dependency>
      <dependency>
        <groupId>jakarta.json</groupId>
        <artifactId>jakarta.json-api</artifactId>
        <version>1.1.6</version>
      </dependency>
      <dependency>
        <groupId>jakarta.json.bind</groupId>
        <artifactId>jakarta.json.bind-api</artifactId>
        <version>1.0.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.mail</groupId>
        <artifactId>jakarta.mail-api</artifactId>
        <version>1.6.5</version>
      </dependency>
      <dependency>
        <groupId>jakarta.persistence</groupId>
        <artifactId>jakarta.persistence-api</artifactId>
        <version>2.2.3</version>
      </dependency>
      <dependency>
        <groupId>jakarta.servlet</groupId>
        <artifactId>jakarta.servlet-api</artifactId>
        <version>4.0.3</version>
      </dependency>
      <dependency>
        <groupId>jakarta.servlet.jsp.jstl</groupId>
        <artifactId>jakarta.servlet.jsp.jstl-api</artifactId>
        <version>1.2.7</version>
      </dependency>
      <dependency>
        <groupId>jakarta.transaction</groupId>
        <artifactId>jakarta.transaction-api</artifactId>
        <version>1.3.3</version>
      </dependency>
      <dependency>
        <groupId>jakarta.validation</groupId>
        <artifactId>jakarta.validation-api</artifactId>
        <version>2.0.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.websocket</groupId>
        <artifactId>jakarta.websocket-api</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.ws.rs</groupId>
        <artifactId>jakarta.ws.rs-api</artifactId>
        <version>2.1.6</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.bind</groupId>
        <artifactId>jakarta.xml.bind-api</artifactId>
        <version>2.3.3</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.soap</groupId>
        <artifactId>jakarta.xml.soap-api</artifactId>
        <version>1.4.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.ws</groupId>
        <artifactId>jakarta.xml.ws-api</artifactId>
        <version>2.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>commons-compiler</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>commons-compiler-jdk</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>janino</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>javax.activation</groupId>
        <artifactId>javax.activation-api</artifactId>
        <version>1.2.0</version>
      </dependency>
      <dependency>
        <groupId>javax.annotation</groupId>
        <artifactId>javax.annotation-api</artifactId>
        <version>1.3.2</version>
      </dependency>
      <dependency>
        <groupId>javax.cache</groupId>
        <artifactId>cache-api</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>javax.xml.bind</groupId>
        <artifactId>jaxb-api</artifactId>
        <version>2.3.1</version>
      </dependency>
      <dependency>
        <groupId>javax.xml.ws</groupId>
        <artifactId>jaxws-api</artifactId>
        <version>2.3.1</version>
      </dependency>
      <dependency>
        <groupId>javax.jms</groupId>
        <artifactId>javax.jms-api</artifactId>
        <version>2.0.1</version>
      </dependency>
      <dependency>
        <groupId>javax.json</groupId>
        <artifactId>javax.json-api</artifactId>
        <version>1.1.4</version>
      </dependency>
      <dependency>
        <groupId>javax.json.bind</groupId>
        <artifactId>javax.json.bind-api</artifactId>
        <version>1.0</version>
      </dependency>
      <dependency>
        <groupId>javax.mail</groupId>
        <artifactId>javax.mail-api</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>javax.money</groupId>
        <artifactId>money-api</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>javax.persistence</groupId>
        <artifactId>javax.persistence-api</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>javax.transaction</groupId>
        <artifactId>javax.transaction-api</artifactId>
        <version>1.3</version>
      </dependency>
      <dependency>
        <groupId>javax.validation</groupId>
        <artifactId>validation-api</artifactId>
        <version>2.0.1.Final</version>
      </dependency>
      <dependency>
        <groupId>javax.websocket</groupId>
        <artifactId>javax.websocket-api</artifactId>
        <version>1.1</version>
      </dependency>
      <dependency>
        <groupId>org.firebirdsql.jdbc</groupId>
        <artifactId>jaybird-jdk17</artifactId>
        <version>3.0.8</version>
      </dependency>
      <dependency>
        <groupId>org.firebirdsql.jdbc</groupId>
        <artifactId>jaybird-jdk18</artifactId>
        <version>3.0.8</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.logging</groupId>
        <artifactId>jboss-logging</artifactId>
        <version>3.4.1.Final</version>
      </dependency>
      <dependency>
        <groupId>org.jboss</groupId>
        <artifactId>jboss-transaction-spi</artifactId>
        <version>7.6.0.Final</version>
      </dependency>
      <dependency>
        <groupId>org.jdom</groupId>
        <artifactId>jdom2</artifactId>
        <version>2.0.6</version>
      </dependency>
      <dependency>
        <groupId>redis.clients</groupId>
        <artifactId>jedis</artifactId>
        <version>3.3.0</version>
      </dependency>
      <dependency>
        <groupId>org.mortbay.jasper</groupId>
        <artifactId>apache-el</artifactId>
        <version>8.5.54</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.orbit</groupId>
        <artifactId>javax.servlet.jsp</artifactId>
        <version>2.2.0.v201112011158</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-reactive-httpclient</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <groupId>com.samskivert</groupId>
        <artifactId>jmustache</artifactId>
        <version>1.15</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-core</artifactId>
        <version>1.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-jaxrs</artifactId>
        <version>1.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-jsonb</artifactId>
        <version>1.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-jsonb-extras</artifactId>
        <version>1.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-jsonschema</artifactId>
        <version>1.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-mapper</artifactId>
        <version>1.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-websocket</artifactId>
        <version>1.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.jolokia</groupId>
        <artifactId>jolokia-core</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq</artifactId>
        <version>3.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-meta</artifactId>
        <version>3.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-codegen</artifactId>
        <version>3.13.2</version>
      </dependency>
      <dependency>
        <groupId>com.jayway.jsonpath</groupId>
        <artifactId>json-path</artifactId>
        <version>2.4.0</version>
      </dependency>
      <dependency>
        <groupId>com.jayway.jsonpath</groupId>
        <artifactId>json-path-assert</artifactId>
        <version>2.4.0</version>
      </dependency>
      <dependency>
        <groupId>org.skyscreamer</groupId>
        <artifactId>jsonassert</artifactId>
        <version>1.5.0</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>jstl</artifactId>
        <version>1.2</version>
      </dependency>
      <dependency>
        <groupId>net.sourceforge.jtds</groupId>
        <artifactId>jtds</artifactId>
        <version>1.3.1</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-api</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-basic-auth-extension</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-file</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-json</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-runtime</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-transforms</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-clients</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-log4j-appender</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-scala_2.11</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-scala_2.12</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-scala_2.13</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-test-utils</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-tools</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.11</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.12</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.13</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.liquibase</groupId>
        <artifactId>liquibase-core</artifactId>
        <version>3.8.9</version>
        <exclusions>
          <exclusion>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-access</artifactId>
        <version>1.2.3</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>1.2.3</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>1.2.3</version>
      </dependency>
      <dependency>
        <groupId>org.mariadb.jdbc</groupId>
        <artifactId>mariadb-java-client</artifactId>
        <version>2.6.0</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-stackdriver</artifactId>
        <version>1.5.1</version>
        <exclusions>
          <exclusion>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.jvnet.mimepull</groupId>
        <artifactId>mimepull</artifactId>
        <version>1.9.13</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>3.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <version>3.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-junit-jupiter</artifactId>
        <version>3.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>bson</artifactId>
        <version>4.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-core</artifactId>
        <version>4.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-legacy</artifactId>
        <version>4.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-reactivestreams</artifactId>
        <version>4.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-sync</artifactId>
        <version>4.0.3</version>
      </dependency>
      <dependency>
        <groupId>com.microsoft.sqlserver</groupId>
        <artifactId>mssql-jdbc</artifactId>
        <version>7.4.1.jre8</version>
      </dependency>
      <dependency>
        <groupId>net.sourceforge.nekohtml</groupId>
        <artifactId>nekohtml</artifactId>
        <version>1.9.22</version>
      </dependency>
      <dependency>
        <groupId>org.neo4j</groupId>
        <artifactId>neo4j-ogm-api</artifactId>
        <version>3.2.11</version>
      </dependency>
      <dependency>
        <groupId>org.neo4j</groupId>
        <artifactId>neo4j-ogm-bolt-driver</artifactId>
        <version>3.2.11</version>
      </dependency>
      <dependency>
        <groupId>org.neo4j</groupId>
        <artifactId>neo4j-ogm-bolt-native-types</artifactId>
        <version>3.2.11</version>
      </dependency>
      <dependency>
        <groupId>org.neo4j</groupId>
        <artifactId>neo4j-ogm-core</artifactId>
        <version>3.2.11</version>
      </dependency>
      <dependency>
        <groupId>org.neo4j</groupId>
        <artifactId>neo4j-ogm-embedded-driver</artifactId>
        <version>3.2.11</version>
      </dependency>
      <dependency>
        <groupId>org.neo4j</groupId>
        <artifactId>neo4j-ogm-embedded-native-types</artifactId>
        <version>3.2.11</version>
      </dependency>
      <dependency>
        <groupId>org.neo4j</groupId>
        <artifactId>neo4j-ogm-http-driver</artifactId>
        <version>3.2.11</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.30.Final</version>
      </dependency>
      <dependency>
        <groupId>org.synchronoss.cloud</groupId>
        <artifactId>nio-multipart-parser</artifactId>
        <version>1.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.nimbusds</groupId>
        <artifactId>oauth2-oidc-sdk</artifactId>
        <version>7.1.1</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>dms</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>ojdbc10</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>ojdbc10_g</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>ojdbc10dms</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>ojdbc10dms_g</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>ojdbc8</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>ojdbc8_g</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>ojdbc8dms</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>ojdbc8dms_g</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>ons</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>oraclepki</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>orai18n</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>osdt_cert</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>osdt_core</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>simplefan</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>ucp</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>xdb</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>xmlparserv2</artifactId>
        <version>********</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>logging-interceptor</artifactId>
        <version>3.14.8</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>mockwebserver</artifactId>
        <version>3.14.8</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okcurl</artifactId>
        <version>3.14.8</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-dnsoverhttps</artifactId>
        <version>3.14.8</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-sse</artifactId>
        <version>3.14.8</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-testing-support</artifactId>
        <version>3.14.8</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-tls</artifactId>
        <version>3.14.8</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-urlconnection</artifactId>
        <version>3.14.8</version>
      </dependency>
      <dependency>
        <groupId>org.messaginghub</groupId>
        <artifactId>pooled-jms</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>42.2.12</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_pushgateway</artifactId>
        <version>0.9.0</version>
      </dependency>
      <dependency>
        <groupId>org.quartz-scheduler</groupId>
        <artifactId>quartz</artifactId>
        <version>2.3.2</version>
        <exclusions>
          <exclusion>
            <groupId>com.mchange</groupId>
            <artifactId>c3p0</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.zaxxer</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.quartz-scheduler</groupId>
        <artifactId>quartz-jobs</artifactId>
        <version>2.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-apt</artifactId>
        <version>4.3.1</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-collections</artifactId>
        <version>4.3.1</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-core</artifactId>
        <version>4.3.1</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-jpa</artifactId>
        <version>4.3.1</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-mongodb</artifactId>
        <version>4.3.1</version>
        <exclusions>
          <exclusion>
            <groupId>org.mongodb</groupId>
            <artifactId>mongo-java-driver</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.rabbitmq</groupId>
        <artifactId>amqp-client</artifactId>
        <version>5.9.0</version>
      </dependency>
      <dependency>
        <groupId>org.reactivestreams</groupId>
        <artifactId>reactive-streams</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>json-path</artifactId>
        <version>3.3.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>json-schema-validator</artifactId>
        <version>3.3.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>rest-assured</artifactId>
        <version>3.3.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>scala-support</artifactId>
        <version>3.3.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>spring-mock-mvc</artifactId>
        <version>3.3.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>spring-web-test-client</artifactId>
        <version>3.3.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>xml-path</artifactId>
        <version>3.3.0</version>
      </dependency>
      <dependency>
        <groupId>io.reactivex</groupId>
        <artifactId>rxjava</artifactId>
        <version>1.3.8</version>
      </dependency>
      <dependency>
        <groupId>io.reactivex</groupId>
        <artifactId>rxjava-reactive-streams</artifactId>
        <version>1.2.1</version>
      </dependency>
      <dependency>
        <groupId>io.reactivex.rxjava2</groupId>
        <artifactId>rxjava</artifactId>
        <version>2.2.19</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-test</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-test-autoconfigure</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-actuator</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-actuator-autoconfigure</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure-processor</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-buildpack-platform</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-metadata</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-processor</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-devtools</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-jarmode-layertools</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-loader</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-loader-tools</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-properties-migrator</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-activemq</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-amqp</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-aop</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-artemis</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-batch</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-cache</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-cassandra</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-cassandra-reactive</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-couchbase</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-couchbase-reactive</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jdbc</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-ldap</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-mongodb</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-mongodb-reactive</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-r2dbc</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-neo4j</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-rest</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-solr</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-freemarker</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-groovy-templates</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-hateoas</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-integration</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jdbc</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jersey</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jetty</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jooq</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-json</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jta-atomikos</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jta-bitronix</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-log4j2</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-logging</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-mail</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-mustache</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-oauth2-client</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-quartz</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-reactor-netty</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-rsocket</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-security</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-thymeleaf</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-tomcat</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-undertow</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-webflux</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web-services</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.messaging.saaj</groupId>
        <artifactId>saaj-impl</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-api</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-chrome-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-edge-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-firefox-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-ie-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-java</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-opera-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-remote-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-safari-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-support</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>htmlunit-driver</artifactId>
        <version>2.40.0</version>
      </dependency>
      <dependency>
        <groupId>com.sendgrid</groupId>
        <artifactId>sendgrid-java</artifactId>
        <version>4.4.8</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jcl-over-slf4j</artifactId>
        <version>1.7.30</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jul-to-slf4j</artifactId>
        <version>1.7.30</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>log4j-over-slf4j</artifactId>
        <version>1.7.30</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-ext</artifactId>
        <version>1.7.30</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-jcl</artifactId>
        <version>1.7.30</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-jdk14</artifactId>
        <version>1.7.30</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-log4j12</artifactId>
        <version>1.7.30</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-nop</artifactId>
        <version>1.7.30</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>1.7.30</version>
      </dependency>
      <dependency>
        <groupId>org.yaml</groupId>
        <artifactId>snakeyaml</artifactId>
        <version>1.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-analysis-extras</artifactId>
        <version>8.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-analytics</artifactId>
        <version>8.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-cell</artifactId>
        <version>8.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-clustering</artifactId>
        <version>8.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-core</artifactId>
        <version>8.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-dataimporthandler</artifactId>
        <version>8.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-dataimporthandler-extras</artifactId>
        <version>8.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-langid</artifactId>
        <version>8.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-ltr</artifactId>
        <version>8.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-solrj</artifactId>
        <version>8.5.1</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-test-framework</artifactId>
        <version>8.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-velocity</artifactId>
        <version>8.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-amqp</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-rabbit-junit</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-rabbit-test</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-core</artifactId>
        <version>4.2.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-infrastructure</artifactId>
        <version>4.2.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-integration</artifactId>
        <version>4.2.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-test</artifactId>
        <version>4.2.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.hateoas</groupId>
        <artifactId>spring-hateoas</artifactId>
        <version>1.1.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka</artifactId>
        <version>2.5.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka-test</artifactId>
        <version>2.5.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-core</artifactId>
        <version>2.3.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-core-tiger</artifactId>
        <version>2.3.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-ldif-batch</artifactId>
        <version>2.3.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-ldif-core</artifactId>
        <version>2.3.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-odm</artifactId>
        <version>2.3.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-test</artifactId>
        <version>2.3.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-asciidoctor</artifactId>
        <version>2.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-core</artifactId>
        <version>2.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-mockmvc</artifactId>
        <version>2.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-restassured</artifactId>
        <version>2.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-webtestclient</artifactId>
        <version>2.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.retry</groupId>
        <artifactId>spring-retry</artifactId>
        <version>1.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-core</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-security</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-support</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-test</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-xml</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.xerial</groupId>
        <artifactId>sqlite-jdbc</artifactId>
        <version>3.31.1</version>
      </dependency>
      <dependency>
        <groupId>com.sun.mail</groupId>
        <artifactId>jakarta.mail</artifactId>
        <version>1.6.5</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf</groupId>
        <artifactId>thymeleaf</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf</groupId>
        <artifactId>thymeleaf-spring5</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.github.mxab.thymeleaf.extras</groupId>
        <artifactId>thymeleaf-extras-data-attribute</artifactId>
        <version>2.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf.extras</groupId>
        <artifactId>thymeleaf-extras-java8time</artifactId>
        <version>3.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf.extras</groupId>
        <artifactId>thymeleaf-extras-springsecurity5</artifactId>
        <version>3.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>nz.net.ultraq.thymeleaf</groupId>
        <artifactId>thymeleaf-layout-dialect</artifactId>
        <version>2.4.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-annotations-api</artifactId>
        <version>9.0.35</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-jdbc</artifactId>
        <version>9.0.35</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-jsp-api</artifactId>
        <version>9.0.35</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-core</artifactId>
        <version>9.0.35</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-el</artifactId>
        <version>9.0.35</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-jasper</artifactId>
        <version>9.0.35</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-websocket</artifactId>
        <version>9.0.35</version>
      </dependency>
      <dependency>
        <groupId>com.unboundid</groupId>
        <artifactId>unboundid-ldapsdk</artifactId>
        <version>4.0.14</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-core</artifactId>
        <version>2.1.0.Final</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-servlet</artifactId>
        <version>2.1.0.Final</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-websockets-jsr</artifactId>
        <version>2.1.0.Final</version>
      </dependency>
      <dependency>
        <groupId>org.webjars</groupId>
        <artifactId>hal-browser</artifactId>
        <version>3325375</version>
      </dependency>
      <dependency>
        <groupId>org.webjars</groupId>
        <artifactId>webjars-locator-core</artifactId>
        <version>0.45</version>
      </dependency>
      <dependency>
        <groupId>wsdl4j</groupId>
        <artifactId>wsdl4j</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-assertj</artifactId>
        <version>2.7.0</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-core</artifactId>
        <version>2.7.0</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-legacy</artifactId>
        <version>2.7.0</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-matchers</artifactId>
        <version>2.7.0</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-placeholders</artifactId>
        <version>2.7.0</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-core-shaded</artifactId>
        <version>4.6.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-mapper-processor</artifactId>
        <version>4.6.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-mapper-runtime</artifactId>
        <version>4.6.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-query-builder</artifactId>
        <version>4.6.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-test-infra</artifactId>
        <version>4.6.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>native-protocol</artifactId>
        <version>1.4.10</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-shaded-guava</artifactId>
        <version>25.1-jre</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-annotation</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-core</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-collectd</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-ehcache</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-graphite</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-healthchecks</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-httpclient</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-httpasyncclient</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jcache</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jdbi</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jdbi3</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jersey2</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jetty9</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jmx</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-json</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jvm</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-log4j2</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-logback</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-servlet</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-servlets</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-avro</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-cbor</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-csv</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-ion</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-properties</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-protobuf</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-smile</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-xml</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-yaml</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-guava</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hibernate3</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hibernate4</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hibernate5</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hppc</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jaxrs</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-joda</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jdk8</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-json-org</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jsr310</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jsr353</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-pcollections</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-base</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-cbor-provider</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-json-provider</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-smile-provider</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-xml-provider</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-yaml-provider</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-all</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-objects</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-retrofit2</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-stree</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-afterburner</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-guice</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-jaxb-annotations</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-jsonSchema</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-kotlin</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-mrbean</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-osgi</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-parameter-names</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-paranamer</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_2.10</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_2.11</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_2.12</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_2.13</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.core</groupId>
        <artifactId>jersey-common</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.core</groupId>
        <artifactId>jersey-client</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.core</groupId>
        <artifactId>jersey-server</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.bundles</groupId>
        <artifactId>jaxrs-ri</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-apache-connector</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-grizzly-connector</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-jetty-connector</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-jdk-connector</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-netty-connector</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-jetty-http</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-grizzly2-http</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-grizzly2-servlet</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-jetty-servlet</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-jdk-http</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-netty-http</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-servlet</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-servlet-core</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-simple-http</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers.glassfish</groupId>
        <artifactId>jersey-gf-ejb</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-bean-validation</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-entity-filtering</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-metainf-services</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.microprofile</groupId>
        <artifactId>jersey-mp-config</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc-bean-validation</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc-freemarker</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc-jsp</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc-mustache</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-proxy-client</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-servlet-portability</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-spring4</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-spring5</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-declarative-linking</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-wadl-doclet</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-weld2-se</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x-transaction</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x-validation</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x-servlet</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x-ban-custom-hk2-binding</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.rx</groupId>
        <artifactId>jersey-rx-client-guava</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.rx</groupId>
        <artifactId>jersey-rx-client-rxjava</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.rx</groupId>
        <artifactId>jersey-rx-client-rxjava2</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.microprofile</groupId>
        <artifactId>jersey-mp-rest-client</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-jaxb</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-jackson</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-jackson1</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-jettison</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-processing</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-binding</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-kryo</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-moxy</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-multipart</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-sse</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.security</groupId>
        <artifactId>oauth1-client</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.security</groupId>
        <artifactId>oauth1-server</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.security</groupId>
        <artifactId>oauth1-signature</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.security</groupId>
        <artifactId>oauth2-client</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.inject</groupId>
        <artifactId>jersey-hk2</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.inject</groupId>
        <artifactId>jersey-cdi2-se</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework</groupId>
        <artifactId>jersey-test-framework-core</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-bundle</artifactId>
        <version>2.30.1</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-external</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-grizzly2</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-inmemory</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-jdk-http</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-simple</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-jetty</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework</groupId>
        <artifactId>jersey-test-framework-util</artifactId>
        <version>2.30.1</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>apache-jsp</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>apache-jstl</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-client</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-java-client</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-java-server</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-openjdk8-client</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-openjdk8-server</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-conscrypt-client</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-conscrypt-server</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-server</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-annotations</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-ant</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-client</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-continuation</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-deploy</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-distribution</artifactId>
        <version>9.4.28.v20200408</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-distribution</artifactId>
        <version>9.4.28.v20200408</version>
        <type>tar.gz</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.fcgi</groupId>
        <artifactId>fcgi-client</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.fcgi</groupId>
        <artifactId>fcgi-server</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.gcloud</groupId>
        <artifactId>jetty-gcloud-session-manager</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-home</artifactId>
        <version>9.4.28.v20200408</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-home</artifactId>
        <version>9.4.28.v20200408</version>
        <type>tar.gz</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-http</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-client</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-common</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-hpack</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-http-client-transport</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-server</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-http-spi</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>infinispan-common</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>infinispan-remote-query</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>infinispan-embedded-query</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-hazelcast</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-io</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jaas</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jaspi</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jmx</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jndi</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.memcached</groupId>
        <artifactId>jetty-memcached-sessions</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-nosql</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-osgi-boot</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-osgi-boot-jsp</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-osgi-boot-warurl</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-httpservice</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-plus</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-proxy</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-quickstart</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-rewrite</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-security</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-openid</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlet</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlets</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-spring</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-unixsocket</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util-ajax</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-webapp</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>javax-websocket-client-impl</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>javax-websocket-server-impl</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-api</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-client</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-common</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-server</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-servlet</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-xml</artifactId>
        <version>9.4.28.v20200408</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>5.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-api</artifactId>
        <version>5.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-engine</artifactId>
        <version>5.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-migrationsupport</artifactId>
        <version>5.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-params</artifactId>
        <version>5.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-commons</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-console</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-engine</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-launcher</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-reporting</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-runner</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite-api</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-testkit</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.vintage</groupId>
        <artifactId>junit-vintage-engine</artifactId>
        <version>5.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-jdk7</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-jdk8</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-js</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-common</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-reflect</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-osgi-bundle</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-junit</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-junit5</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-testng</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-js</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-common</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-annotations-common</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-main-kts</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-script-runtime</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-script-util</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-scripting-common</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-scripting-jvm</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-scripting-jvm-host</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-compiler</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-compiler-embeddable</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-daemon-client</artifactId>
        <version>1.3.72</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-android</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-core-js</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-core</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-core-native</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-core-common</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-debug</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-guava</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-javafx</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-jdk8</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-jdk9</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-play-services</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-reactive</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-reactor</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-rx2</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-rx3</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-slf4j</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-swing</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-test</artifactId>
        <version>1.3.6</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-1.2-api</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jcl</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-flume-ng</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-taglib</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jmx-gui</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j-impl</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j18-impl</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-appserver</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-web</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-couchdb</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-mongodb2</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-mongodb3</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-cassandra</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jpa</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-iostreams</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jpl</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-liquibase</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-docker</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-kubernetes</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-spring-cloud-config-client</artifactId>
        <version>2.13.2</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-core</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-jersey2</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-appoptics</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-atlas</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-azure-monitor</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-cloudwatch</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-cloudwatch2</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-datadog</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-dynatrace</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-elastic</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-ganglia</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-graphite</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-humio</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-influx</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-jmx</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-kairos</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-new-relic</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-opentsdb</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-prometheus</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-signalfx</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-statsd</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-wavefront</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-test</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-buffer</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-dns</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-haproxy</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-http</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-http2</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-memcache</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-mqtt</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-redis</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-smtp</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-socks</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-stomp</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-xml</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-common</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-dev-tools</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-handler</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-handler-proxy</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver-dns</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-rxtx</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-sctp</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-udt</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-example</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-all</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.49.Final</version>
        <classifier>linux-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.49.Final</version>
        <classifier>osx-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-epoll</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-epoll</artifactId>
        <version>4.1.49.Final</version>
        <classifier>linux-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-kqueue</artifactId>
        <version>4.1.49.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-kqueue</artifactId>
        <version>4.1.49.Final</version>
        <classifier>osx-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>cloud-spanner-r2dbc</artifactId>
        <version>0.2.0</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-h2</artifactId>
        <version>0.8.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-mssql</artifactId>
        <version>0.8.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>dev.miku</groupId>
        <artifactId>r2dbc-mysql</artifactId>
        <version>0.8.1.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-postgresql</artifactId>
        <version>0.8.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-pool</artifactId>
        <version>0.8.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-proxy</artifactId>
        <version>0.8.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-spi</artifactId>
        <version>0.8.1.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-core</artifactId>
        <version>3.3.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-test</artifactId>
        <version>3.3.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-tools</artifactId>
        <version>3.3.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-extra</artifactId>
        <version>3.3.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-adapter</artifactId>
        <version>3.3.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.netty</groupId>
        <artifactId>reactor-netty</artifactId>
        <version>0.9.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-pool</artifactId>
        <version>0.1.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.kafka</groupId>
        <artifactId>reactor-kafka</artifactId>
        <version>1.2.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.rabbitmq</groupId>
        <artifactId>reactor-rabbitmq</artifactId>
        <version>1.4.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.kotlin</groupId>
        <artifactId>reactor-kotlin-extensions</artifactId>
        <version>1.0.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-core</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-load-balancer</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-micrometer</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-test</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-transport-local</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-transport-netty</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-cassandra</artifactId>
        <version>3.0.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-commons</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-couchbase</artifactId>
        <version>4.0.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-elasticsearch</artifactId>
        <version>4.0.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-gemfire</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-geode</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-jdbc</artifactId>
        <version>2.0.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-relational</artifactId>
        <version>2.0.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-jpa</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-mongodb</artifactId>
        <version>3.0.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-neo4j</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-r2dbc</artifactId>
        <version>1.1.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-rest-webmvc</artifactId>
        <version>3.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-rest-core</artifactId>
        <version>3.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-rest-hal-browser</artifactId>
        <version>3.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-rest-hal-explorer</artifactId>
        <version>3.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-solr</artifactId>
        <version>4.2.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-keyvalue</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-envers</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-ldap</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-aop</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-aspects</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-beans</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context-indexer</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context-support</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-core</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-expression</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-instrument</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jcl</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jdbc</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jms</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-messaging</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-orm</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-oxm</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-test</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-tx</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-web</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webmvc</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-websocket</artifactId>
        <version>5.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-amqp</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-core</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-event</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-feed</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-file</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-ftp</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-gemfire</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-groovy</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-http</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-ip</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-jdbc</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-jms</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-jmx</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-jpa</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-mail</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-mongodb</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-mqtt</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-redis</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-rmi</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-rsocket</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-scripting</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-security</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-sftp</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-stomp</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-stream</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-syslog</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-test</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-test-support</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-webflux</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-websocket</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-ws</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-xml</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-xmpp</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-zookeeper</artifactId>
        <version>5.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-acl</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-aspects</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-cas</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-config</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-core</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-crypto</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-data</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-ldap</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-messaging</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-client</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-core</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-jose</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-resource-server</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-openid</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-remoting</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-rsocket</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-saml2-service-provider</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-taglibs</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-test</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-web</artifactId>
        <version>5.3.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-jdbc</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-data-geode</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-hazelcast</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-data-mongodb</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-core</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-data-redis</artifactId>
        <version>2.3.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-rsa</artifactId>
        <version>1.0.9.RELEASE</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-commons</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-context</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-loadbalancer</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-eureka-client</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-archaius</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-archaius</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-eureka-server</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-hystrix-dashboard</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-turbine</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-turbine-stream</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-zuul</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-core</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-hystrix</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-eureka-server</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-hystrix-dashboard</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-hystrix-stream</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-sidecar</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-turbine</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-turbine-stream</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-zuul</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-ribbon</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.netflix.netflix-commons</groupId>
        <artifactId>netflix-commons-util</artifactId>
        <version>0.3.0</version>
      </dependency>
      <dependency>
        <groupId>com.netflix.archaius</groupId>
        <artifactId>archaius-core</artifactId>
        <version>0.7.6</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.concurrency-limits</groupId>
        <artifactId>concurrency-limits-core</artifactId>
        <version>0.1.12</version>
      </dependency>
      <dependency>
        <groupId>com.netflix.concurrency-limits</groupId>
        <artifactId>concurrency-limits-servlet</artifactId>
        <version>0.1.12</version>
      </dependency>
      <dependency>
        <groupId>com.netflix.servo</groupId>
        <artifactId>servo-core</artifactId>
        <version>0.12.21</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.eureka</groupId>
        <artifactId>eureka-client</artifactId>
        <version>1.10.7</version>
        <exclusions>
          <exclusion>
            <groupId>com.github.vlsi.compactmap</groupId>
            <artifactId>compactmap</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.eureka</groupId>
        <artifactId>eureka-core</artifactId>
        <version>1.10.7</version>
        <exclusions>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>blitz4j</groupId>
            <artifactId>com.netflix.blitz4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.amazonaws</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.hystrix</groupId>
        <artifactId>hystrix-core</artifactId>
        <version>1.5.18</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.hystrix</groupId>
        <artifactId>hystrix-serialization</artifactId>
        <version>1.5.18</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.hystrix</groupId>
        <artifactId>hystrix-metrics-event-stream</artifactId>
        <version>1.5.18</version>
        <exclusions>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.hystrix</groupId>
        <artifactId>hystrix-javanica</artifactId>
        <version>1.5.18</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.ribbon</groupId>
        <artifactId>ribbon</artifactId>
        <version>2.3.0</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.ribbon</groupId>
        <artifactId>ribbon-core</artifactId>
        <version>2.3.0</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.ribbon</groupId>
        <artifactId>ribbon-httpclient</artifactId>
        <version>2.3.0</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.ribbon</groupId>
        <artifactId>ribbon-eureka</artifactId>
        <version>2.3.0</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.ribbon</groupId>
        <artifactId>ribbon-loadbalancer</artifactId>
        <version>2.3.0</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.zuul</groupId>
        <artifactId>zuul-core</artifactId>
        <version>1.3.1</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.netflix-commons</groupId>
        <artifactId>netflix-eventbus</artifactId>
        <version>0.3.0</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream</artifactId>
        <version>3.0.9.RELEASE</version>
        <type>test-jar</type>
        <classifier>test-binder</classifier>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-test</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-schema-registry-core</artifactId>
        <version>1.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-schema-registry-server</artifactId>
        <version>1.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-schema-registry-client</artifactId>
        <version>1.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-test-support</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-kafka</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-kafka-streams</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-kafka-core</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-rabbit</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-rabbit-core</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-stream-rabbit</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-rabbit-test-support</artifactId>
        <version>3.0.9.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-task</artifactId>
        <version>2.2.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-task-core</artifactId>
        <version>2.2.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-task-batch</artifactId>
        <version>2.2.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-task-stream</artifactId>
        <version>2.2.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.pivotal.cfenv</groupId>
        <artifactId>java-cfenv-test-support</artifactId>
        <version>2.1.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-circuitbreaker</artifactId>
        <version>1.3.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-timelimiter</artifactId>
        <version>1.3.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-reactor</artifactId>
        <version>1.3.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-micrometer</artifactId>
        <version>1.3.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-circuitbreaker-resilience4j</artifactId>
        <version>1.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-circuitbreaker-spring-retry</artifactId>
        <version>1.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
        <version>1.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-circuitbreaker-reactor-resilience4j</artifactId>
        <version>1.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-config</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-config-client</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-config-server</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-config-monitor</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.vault</groupId>
        <artifactId>spring-vault-core</artifactId>
        <version>2.2.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.credhub</groupId>
        <artifactId>spring-credhub-core</artifactId>
        <version>2.0.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jgit</groupId>
        <artifactId>org.eclipse.jgit</artifactId>
        <version>5.1.3.201810200350-r</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jgit</groupId>
        <artifactId>org.eclipse.jgit.junit.http</artifactId>
        <version>5.1.3.201810200350-r</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jgit</groupId>
        <artifactId>org.eclipse.jgit.http.apache</artifactId>
        <version>5.1.3.201810200350-r</version>
      </dependency>
      <dependency>
        <groupId>com.jcraft</groupId>
        <artifactId>jsch</artifactId>
        <version>0.1.54</version>
      </dependency>
      <dependency>
        <groupId>org.tmatesoft.svnkit</groupId>
        <artifactId>svnkit</artifactId>
        <version>1.8.12</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-context</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-core</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-compiler</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-task</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-web</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-function-web</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-function-webflux</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-deployer</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-adapter-aws</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-adapter-azure</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-adapter-gcp</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-adapter-openwhisk</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-kotlin</artifactId>
        <version>3.0.11.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gateway-webflux</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gateway-mvc</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gateway-core</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gateway-server</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-gateway</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-consul-core</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-consul-binder</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-consul-config</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-consul-discovery</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-consul</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-consul-bus</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-consul-config</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-consul-discovery</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-consul-all</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.ecwid.consul</groupId>
        <artifactId>consul-api</artifactId>
        <version>1.4.5</version>
        <exclusions>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-sleuth-core</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-sleuth-zipkin</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-zipkin</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-sleuth</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing.brave</groupId>
        <artifactId>brave-opentracing</artifactId>
        <version>0.37.2</version>
        <exclusions>
          <exclusion>
            <groupId>io.zipkin.brave</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.github.lognet</groupId>
        <artifactId>grpc-spring-boot-starter</artifactId>
        <version>3.4.1</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.zipkin2</groupId>
        <artifactId>zipkin</artifactId>
        <version>2.21.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.zipkin2</groupId>
        <artifactId>zipkin-tests</artifactId>
        <version>2.21.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.zipkin2</groupId>
        <artifactId>zipkin-junit</artifactId>
        <version>2.21.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-tests</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-jfr</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-log4j2</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-log4j12</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-slf4j</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-rxjava2</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-dubbo</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-dubbo-rpc</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-grpc</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-http</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-http-tests</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-httpasyncclient</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-httpclient</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-jaxrs2</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-jersey-server</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-jms</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-kafka-clients</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-kafka-streams</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-messaging</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-mongodb</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-mysql</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-mysql6</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-mysql8</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-netty-codec-http</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-okhttp3</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-p6spy</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-rpc</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-servlet</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-sparkjava</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-spring-rabbit</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-spring-web</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-spring-webmvc</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-vertx-web</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-spring-beans</artifactId>
        <version>5.12.7</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter</artifactId>
        <version>2.15.2</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-okhttp3</artifactId>
        <version>2.15.2</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-libthrift</artifactId>
        <version>2.15.2</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-urlconnection</artifactId>
        <version>2.15.2</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-kafka08</artifactId>
        <version>2.15.2</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-kafka</artifactId>
        <version>2.15.2</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-amqp-client</artifactId>
        <version>2.15.2</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-activemq-client</artifactId>
        <version>2.15.2</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter-spring-beans</artifactId>
        <version>2.15.2</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter-brave</artifactId>
        <version>2.15.2</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter-metrics-micrometer</artifactId>
        <version>2.15.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-vault-config</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-vault-config-aws</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-vault-config-consul</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-vault-config-databases</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-vault-config-rabbitmq</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-vault-config</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-zookeeper-core</artifactId>
        <version>2.2.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-zookeeper-config</artifactId>
        <version>2.2.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-zookeeper-discovery</artifactId>
        <version>2.2.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-zookeeper</artifactId>
        <version>2.2.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-zookeeper-all</artifactId>
        <version>2.2.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-zookeeper-config</artifactId>
        <version>2.2.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-zookeeper-discovery</artifactId>
        <version>2.2.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-framework</artifactId>
        <version>4.0.1</version>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-recipes</artifactId>
        <version>4.0.1</version>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-x-discovery</artifactId>
        <version>4.0.1</version>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-test</artifactId>
        <version>4.0.1</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.springframework.security.oauth.boot</groupId>
        <artifactId>spring-security-oauth2-autoconfigure</artifactId>
        <version>2.1.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-security</artifactId>
        <version>2.2.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-oauth2</artifactId>
        <version>2.2.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-security</artifactId>
        <version>2.2.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-cloudfoundry-discovery</artifactId>
        <version>2.2.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-cloudfoundry-commons</artifactId>
        <version>2.2.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-cloudfoundry-web</artifactId>
        <version>2.2.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-cloudfoundry</artifactId>
        <version>2.2.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-bus-amqp</artifactId>
        <version>2.2.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-bus-kafka</artifactId>
        <version>2.2.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-bus</artifactId>
        <version>2.2.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-wiremock</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-spec</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-spec-java</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-spec-groovy</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-spec-kotlin</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-verifier</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-converters</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-pact</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-stub-runner</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-stub-runner-boot</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-shade</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-contract-verifier</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-contract-stub-runner</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-contract-stub-runner-jetty</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-maven-plugin</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-gradle-plugin</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.github.tomakehurst</groupId>
        <artifactId>wiremock-jre8-standalone</artifactId>
        <version>2.26.2</version>
      </dependency>
      <dependency>
        <groupId>com.toomuchcoding.jsonassert</groupId>
        <artifactId>jsonassert</artifactId>
        <version>0.4.14</version>
      </dependency>
      <dependency>
        <groupId>com.thimbleware.jmemcached</groupId>
        <artifactId>jmemcached-core</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>elasticache-java-cluster-client</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-aws</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-aws-jdbc</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-aws-messaging</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-aws-parameter-store-config</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-aws-secrets-manager-config</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-aws-context</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-aws-messaging</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-aws-jdbc</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-aws-core</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-aws-autoconfigure</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-aws-actuator</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-aws-parameter-store-config</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-aws-secrets-manager-config</artifactId>
        <version>2.2.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-macie2</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-iotsitewise</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-synthetics</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-codestarconnections</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-detective</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-kinesisvideosignalingchannels</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-ebs</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-augmentedairuntime</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-outposts</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-codeguruprofiler</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-codegurureviewer</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-frauddetector</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-kendra</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-networkmanager</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-computeoptimizer</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-accessanalyzer</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-schemas</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-imagebuilder</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-elasticinference</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-iotsecuretunneling</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-appconfig</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-wafv2</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-connectparticipant</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-migrationhubconfig</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-dataexchange</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-sesv2</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-marketplacecatalog</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-ssooidc</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-sso</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-savingsplans</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-codestarnotifications</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-workmailmessageflow</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-qldbsession</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-qldb</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-forecastquery</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-forecast</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-lakeformation</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-eventbridge</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-ec2instanceconnect</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-applicationinsights</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-servicequotas</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-personalizeevents</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-personalize</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-personalizeruntime</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-ioteventsdata</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-iotevents</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-iotthingsgraph</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-groundstation</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-mediapackagevod</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-managedblockchain</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-textract</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-worklink</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-backup</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-docdb</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-apigatewayv2</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-apigatewaymanagementapi</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-kafka</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-appmesh</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-licensemanager</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-securityhub</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-fsx</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-mediaconnect</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-kinesisanalyticsv2</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-comprehendmedical</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-globalaccelerator</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-transfer</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-datasync</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-robomaker</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-amplify</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-quicksight</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-rdsdata</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-route53resolver</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-ram</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-s3control</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-pinpointsmsvoice</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-pinpointemail</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-chime</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-signer</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-dlm</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-macie</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-eks</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-mediatailor</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-neptune</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-pi</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-iot1clickprojects</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-iot1clickdevices</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-iotanalytics</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-acmpca</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-secretsmanager</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-fms</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-connect</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-transcribe</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-autoscalingplans</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-workmail</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-servicediscovery</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-cloud9</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-serverlessapplicationrepository</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-alexaforbusiness</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-resourcegroups</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-comprehend</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-translate</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-sagemaker</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-iotjobsdataplane</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-sagemakerruntime</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-kinesisvideo</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-appsync</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-guardduty</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-mq</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-mediaconvert</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-mediastore</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-mediastoredata</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-medialive</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-mediapackage</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-costexplorer</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-pricing</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-mobile</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-cloudhsmv2</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-glue</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-migrationhub</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-dax</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-greengrass</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-athena</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-marketplaceentitlement</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-codestar</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-lexmodelbuilding</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-resourcegroupstaggingapi</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-pinpoint</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-xray</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-opsworkscm</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-support</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-simpledb</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-servicecatalog</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-servermigration</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-simpleworkflow</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-storagegateway</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-route53</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-importexport</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-sts</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-sqs</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-rds</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-redshift</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-elasticbeanstalk</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-glacier</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-iam</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-datapipeline</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-elasticloadbalancing</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-elasticloadbalancingv2</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-emr</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-elasticache</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-elastictranscoder</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-ec2</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-dynamodb</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-sns</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-budgets</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-cloudtrail</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-cloudwatch</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-logs</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-events</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-cognitoidentity</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-cognitosync</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-directconnect</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-cloudformation</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-cloudfront</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-clouddirectory</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-kinesis</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-opsworks</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-ses</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-autoscaling</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-cloudsearch</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-cloudwatchmetrics</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-codedeploy</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-codepipeline</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-kms</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-config</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-lambda</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-ecs</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-ecr</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-cloudhsm</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-ssm</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-workspaces</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-machinelearning</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-directory</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-efs</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-codecommit</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-devicefarm</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-elasticsearch</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-waf</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-marketplacecommerceanalytics</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-inspector</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-iot</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-api-gateway</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-acm</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-gamelift</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-dms</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-marketplacemeteringservice</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-cognitoidp</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-discovery</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-applicationautoscaling</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-snowball</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-rekognition</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-polly</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-lightsail</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-stepfunctions</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-health</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-costandusagereport</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-codebuild</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-appstream</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-shield</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-batch</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-lex</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-mechanicalturkrequester</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-organizations</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-workdocs</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-models</artifactId>
        <version>1.11.792</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-swf-libraries</artifactId>
        <version>1.11.22</version>
        <optional>false</optional>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>jmespath-java</artifactId>
        <version>1.11.792</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-openfeign-core</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-openfeign</artifactId>
        <version>2.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-core</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign.form</groupId>
        <artifactId>feign-form-spring</artifactId>
        <version>3.8.0</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-slf4j</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-httpclient</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-hystrix</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-okhttp</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-gson</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jackson-jaxb</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jackson</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jaxb</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jaxrs</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-ribbon</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-sax</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-mock</artifactId>
        <version>10.10.1</version>
      </dependency>
      <dependency>
        <groupId>me.snowdrop</groupId>
        <artifactId>istio-client</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-core</artifactId>
        <version>1.1.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-config</artifactId>
        <version>1.1.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-discovery</artifactId>
        <version>1.1.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-istio</artifactId>
        <version>1.1.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-ribbon</artifactId>
        <version>1.1.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-leader</artifactId>
        <version>1.1.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-loadbalancer</artifactId>
        <version>1.1.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes</artifactId>
        <version>1.1.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-config</artifactId>
        <version>1.1.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-ribbon</artifactId>
        <version>1.1.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-loadbalancer</artifactId>
        <version>1.1.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-all</artifactId>
        <version>1.1.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.junit</groupId>
        <artifactId>arquillian-junit-standalone</artifactId>
        <version>1.4.0.Final</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.arquillian.cube</groupId>
        <artifactId>arquillian-cube-requirement</artifactId>
        <version>1.15.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.arquillian.cube</groupId>
        <artifactId>arquillian-cube-kubernetes</artifactId>
        <version>1.15.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.arquillian.cube</groupId>
        <artifactId>arquillian-cube-openshift</artifactId>
        <version>1.15.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>io.specto</groupId>
        <artifactId>hoverfly-java-junit5</artifactId>
        <version>0.13.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>io.specto</groupId>
        <artifactId>hoverfly-java</artifactId>
        <version>0.13.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>mockwebserver</artifactId>
        <version>0.1.2</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.sun</groupId>
            <artifactId>tools</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-client</artifactId>
        <version>4.10.3</version>
        <type>test-jar</type>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-common</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>model-annotator</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-annotator</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-core</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-rbac</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-admissionregistration</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-apps</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-autoscaling</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-apiextensions</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-batch</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-certificates</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-coordination</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-discovery</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-events</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-extensions</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-networking</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-metrics</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-policy</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-scheduling</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-settings</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-storageclass</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-client</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-server-mock</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-client</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>knative-model</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>knative-client</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>knative-mock</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>knative-examples</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>knative-tests</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-model-v1alpha1</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-model-v1beta1</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-client</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-mock</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-examples</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-tests</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>servicecatalog-model</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>servicecatalog-client</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>servicecatalog-server-mock</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>service-catalog-examples</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>servicecatalog-tests</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-server-mock</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-examples</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8.kubernetes</groupId>
        <artifactId>kubernetes-karaf</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8.kubernetes</groupId>
        <artifactId>kubernetes-karaf-itests</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-test</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-openshift-uberjar</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-core</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-autoconfigure</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-cloudfoundry</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-pubsub</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-data-firestore</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-data-datastore</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-data-spanner</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-storage</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-logging</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-pubsub-stream-binder</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-security-iap</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-vision</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-bigquery</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-security-firebase</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-secretmanager</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-bus-pubsub</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-cloudfoundry</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-pubsub</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-storage</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-data-spanner</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-data-datastore</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-data-firestore</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-firestore</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-sql-mysql</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-sql-postgresql</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-trace</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-logging</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-metrics</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-security-iap</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-security-firebase</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-vision</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-bigquery</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gcp-starter-secretmanager</artifactId>
        <version>1.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud.sql</groupId>
        <artifactId>mysql-socket-factory</artifactId>
        <version>1.0.16</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud.sql</groupId>
        <artifactId>postgres-socket-factory</artifactId>
        <version>1.0.16</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client</artifactId>
        <version>1.30.7</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava-gwt</artifactId>
        <version>29.0-jre</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava-testlib</artifactId>
        <version>29.0-jre</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client</artifactId>
        <version>1.37.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-android</artifactId>
        <version>1.37.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-apache-v2</artifactId>
        <version>1.37.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-appengine</artifactId>
        <version>1.37.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-gson</artifactId>
        <version>1.37.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-jackson2</artifactId>
        <version>1.37.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-protobuf</artifactId>
        <version>1.37.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-test</artifactId>
        <version>1.37.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-xml</artifactId>
        <version>1.37.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-alts</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-api</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-auth</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-context</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-core</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-grpclb</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-netty</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-netty-shaded</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-okhttp</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-protobuf</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-services</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-stub</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-testing</artifactId>
        <version>1.33.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>api-common</artifactId>
        <version>1.10.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax</artifactId>
        <version>1.60.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax-grpc</artifactId>
        <version>1.60.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax-httpjson</artifactId>
        <version>0.77.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-common-protos</artifactId>
        <version>1.18.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-common-protos</artifactId>
        <version>1.18.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-iam-v1</artifactId>
        <version>1.0.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-iam-v1</artifactId>
        <version>1.0.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-java</artifactId>
        <version>3.13.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-java-util</artifactId>
        <version>3.13.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquery</artifactId>
        <version>1.123.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-compute</artifactId>
        <version>0.118.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dns</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-logging-logback</artifactId>
        <version>0.118.3-alpha</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-nio</artifactId>
        <version>0.121.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-notification</artifactId>
        <version>0.121.0-beta</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-resourcemanager</artifactId>
        <version>0.118.2-alpha</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-spanner-jdbc</artifactId>
        <version>1.17.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-storage</artifactId>
        <version>1.113.1</version>
      </dependency>
      <dependency>
        <groupId>io.grafeas</groupId>
        <artifactId>grafeas</artifactId>
        <version>1.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-accessapproval-v1</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-accessapproval-v1</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-accessapproval</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.auto.value</groupId>
        <artifactId>auto-value-annotations</artifactId>
        <version>1.7.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-asset-v1</artifactId>
        <version>2.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-asset-v1</artifactId>
        <version>2.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-asset-v1beta1</artifactId>
        <version>0.93.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-asset-v1p1beta1</artifactId>
        <version>0.93.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-asset-v1p2beta1</artifactId>
        <version>0.93.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-asset-v1p4beta1</artifactId>
        <version>0.93.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-asset-v1p5beta1</artifactId>
        <version>0.93.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-asset-v1p1beta1</artifactId>
        <version>0.93.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-asset-v1p2beta1</artifactId>
        <version>0.93.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-asset-v1beta1</artifactId>
        <version>0.93.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-asset-v1p4beta1</artifactId>
        <version>0.93.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-asset-v1p5beta1</artifactId>
        <version>0.93.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-asset</artifactId>
        <version>2.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-automl-v1beta1</artifactId>
        <version>0.83.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-automl-v1</artifactId>
        <version>1.2.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-automl-v1beta1</artifactId>
        <version>0.83.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-automl-v1</artifactId>
        <version>1.2.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-automl</artifactId>
        <version>1.2.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquerydatatransfer</artifactId>
        <version>1.0.14</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-bigquerydatatransfer-v1</artifactId>
        <version>1.0.14</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-bigquerydatatransfer-v1</artifactId>
        <version>1.0.14</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-bigquerystorage-v1alpha2</artifactId>
        <version>0.105.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-bigquerystorage-v1beta1</artifactId>
        <version>0.105.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-bigquerystorage-v1beta2</artifactId>
        <version>0.105.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-bigquerystorage-v1</artifactId>
        <version>1.5.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-bigquerystorage-v1alpha2</artifactId>
        <version>0.105.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-bigquerystorage-v1beta1</artifactId>
        <version>0.105.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-bigquerystorage-v1beta2</artifactId>
        <version>0.105.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-bigquerystorage-v1</artifactId>
        <version>1.5.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquerystorage</artifactId>
        <version>1.5.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigtable</artifactId>
        <version>1.16.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigtable-emulator</artifactId>
        <version>0.125.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-bigtable-admin-v2</artifactId>
        <version>1.16.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-bigtable-v2</artifactId>
        <version>1.16.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-bigtable-admin-v2</artifactId>
        <version>1.16.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-bigtable-v2</artifactId>
        <version>1.16.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-billing-v1</artifactId>
        <version>1.1.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-billing-v1</artifactId>
        <version>1.1.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-billing</artifactId>
        <version>1.1.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-billingbudgets-v1beta1</artifactId>
        <version>0.4.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-billingbudgets-v1beta1</artifactId>
        <version>0.4.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-billingbudgets</artifactId>
        <version>0.4.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-build-v1</artifactId>
        <version>2.1.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-build-v1</artifactId>
        <version>2.1.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-build</artifactId>
        <version>2.1.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-container-v1</artifactId>
        <version>1.0.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-container-v1</artifactId>
        <version>1.0.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-container</artifactId>
        <version>1.0.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-containeranalysis-v1</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-containeranalysis-v1beta1</artifactId>
        <version>0.88.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-containeranalysis</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-containeranalysis-v1</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-containeranalysis-v1beta1</artifactId>
        <version>0.88.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-datacatalog-v1</artifactId>
        <version>1.0.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-datacatalog-v1beta1</artifactId>
        <version>0.37.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-datacatalog</artifactId>
        <version>1.0.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-datacatalog-v1</artifactId>
        <version>1.0.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-datacatalog-v1beta1</artifactId>
        <version>0.37.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-datalabeling-v1beta1</artifactId>
        <version>0.84.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-datalabeling-v1beta1</artifactId>
        <version>0.84.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-datalabeling</artifactId>
        <version>0.119.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dataproc</artifactId>
        <version>1.1.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-dataproc-v1beta2</artifactId>
        <version>0.89.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-dataproc-v1</artifactId>
        <version>1.1.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-dataproc-v1</artifactId>
        <version>1.1.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-dataproc-v1beta2</artifactId>
        <version>0.89.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-datastore-v1</artifactId>
        <version>0.88.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-datastore</artifactId>
        <version>1.105.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-dlp-v2</artifactId>
        <version>2.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dlp</artifactId>
        <version>2.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-dlp-v2</artifactId>
        <version>2.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-dialogflow-v2</artifactId>
        <version>2.3.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-dialogflow-v2beta1</artifactId>
        <version>0.90.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-dialogflow-v2</artifactId>
        <version>2.3.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dialogflow</artifactId>
        <version>2.3.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-dialogflow-v2beta1</artifactId>
        <version>0.90.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-document-ai-v1beta1</artifactId>
        <version>0.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-document-ai-v1beta2</artifactId>
        <version>0.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-document-ai-v1beta3</artifactId>
        <version>0.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-document-ai-v1beta1</artifactId>
        <version>0.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-document-ai-v1beta2</artifactId>
        <version>0.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-document-ai-v1beta3</artifactId>
        <version>0.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-document-ai</artifactId>
        <version>0.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-errorreporting</artifactId>
        <version>0.120.5-beta</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-error-reporting-v1beta1</artifactId>
        <version>0.86.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-error-reporting-v1beta1</artifactId>
        <version>0.86.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-firestore</artifactId>
        <version>2.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-firestore-admin-v1</artifactId>
        <version>2.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-firestore-v1</artifactId>
        <version>2.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-firestore-admin-v1</artifactId>
        <version>2.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-firestore-v1</artifactId>
        <version>2.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-firestore-admin</artifactId>
        <version>2.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-iamcredentials</artifactId>
        <version>1.1.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-iamcredentials-v1</artifactId>
        <version>1.1.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-iamcredentials-v1</artifactId>
        <version>1.1.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-iot</artifactId>
        <version>1.1.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-iot-v1</artifactId>
        <version>1.1.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-iot-v1</artifactId>
        <version>1.1.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-kms-v1</artifactId>
        <version>0.87.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-kms-v1</artifactId>
        <version>0.87.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-kms</artifactId>
        <version>1.40.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-language-v1</artifactId>
        <version>1.83.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-language-v1beta2</artifactId>
        <version>0.84.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-language-v1</artifactId>
        <version>1.83.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-language</artifactId>
        <version>1.101.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-language-v1beta2</artifactId>
        <version>0.84.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-logging-v2</artifactId>
        <version>0.85.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-logging-v2</artifactId>
        <version>0.85.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-logging</artifactId>
        <version>1.102.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-mediatranslation</artifactId>
        <version>0.2.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-mediatranslation-v1beta1</artifactId>
        <version>0.2.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-mediatranslation-v1beta1</artifactId>
        <version>0.2.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-memcache-v1beta2</artifactId>
        <version>0.2.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-memcache-v1beta2</artifactId>
        <version>0.2.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-memcache</artifactId>
        <version>0.2.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-monitoring</artifactId>
        <version>2.0.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-monitoring-v3</artifactId>
        <version>2.0.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-monitoring-v3</artifactId>
        <version>2.0.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-monitoring-dashboard-v1</artifactId>
        <version>1.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-monitoring-dashboard-v1</artifactId>
        <version>1.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-monitoring-dashboard</artifactId>
        <version>1.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-os-login-v1</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-os-login-v1</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-os-login</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-phishingprotection</artifactId>
        <version>0.29.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-phishingprotection-v1beta1</artifactId>
        <version>0.29.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-phishingprotection-v1beta1</artifactId>
        <version>0.29.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-pubsub-v1</artifactId>
        <version>1.90.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-pubsub-v1</artifactId>
        <version>1.90.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-pubsub</artifactId>
        <version>1.108.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-recaptchaenterprise</artifactId>
        <version>1.0.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-recaptchaenterprise-v1</artifactId>
        <version>1.0.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-recaptchaenterprise-v1beta1</artifactId>
        <version>0.32.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-recaptchaenterprise-v1</artifactId>
        <version>1.0.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-recaptchaenterprise-v1beta1</artifactId>
        <version>0.32.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-recommender</artifactId>
        <version>1.2.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-recommender-v1</artifactId>
        <version>1.2.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-recommender-v1beta1</artifactId>
        <version>0.7.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-recommender-v1</artifactId>
        <version>1.2.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-recommender-v1beta1</artifactId>
        <version>0.7.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-redis-v1beta1</artifactId>
        <version>0.86.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-redis-v1</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-redis-v1</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-redis-v1beta1</artifactId>
        <version>0.86.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-redis</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-scheduler</artifactId>
        <version>1.23.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-scheduler-v1beta1</artifactId>
        <version>0.83.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-scheduler-v1beta1</artifactId>
        <version>0.83.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-scheduler-v1</artifactId>
        <version>1.23.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-scheduler-v1</artifactId>
        <version>1.23.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-secretmanager-v1</artifactId>
        <version>1.2.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-secretmanager-v1beta1</artifactId>
        <version>1.2.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-secretmanager-v1</artifactId>
        <version>1.2.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-secretmanager-v1beta1</artifactId>
        <version>1.2.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-secretmanager</artifactId>
        <version>1.2.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-securitycenter-v1</artifactId>
        <version>1.2.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-securitycenter-v1</artifactId>
        <version>1.2.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-securitycenter</artifactId>
        <version>1.2.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-securitycenter-v1beta1</artifactId>
        <version>0.90.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-securitycenter-v1beta1</artifactId>
        <version>0.90.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-securitycenter-v1p1beta1</artifactId>
        <version>0.90.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-securitycenter-v1p1beta1</artifactId>
        <version>0.90.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-servicedirectory</artifactId>
        <version>0.2.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-servicedirectory-v1beta1</artifactId>
        <version>0.2.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-servicedirectory-v1beta1</artifactId>
        <version>0.2.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-spanner-admin-instance-v1</artifactId>
        <version>2.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-spanner-v1</artifactId>
        <version>2.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-spanner-v1</artifactId>
        <version>2.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-spanner-admin-database-v1</artifactId>
        <version>2.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-spanner</artifactId>
        <version>2.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-spanner</artifactId>
        <version>2.0.2</version>
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-spanner-admin-instance-v1</artifactId>
        <version>2.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-spanner-admin-database-v1</artifactId>
        <version>2.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-speech</artifactId>
        <version>1.24.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-speech-v1</artifactId>
        <version>1.24.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-speech-v1beta1</artifactId>
        <version>0.77.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-speech-v1p1beta1</artifactId>
        <version>0.77.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-speech-v1</artifactId>
        <version>1.24.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-speech-v1beta1</artifactId>
        <version>0.77.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-speech-v1p1beta1</artifactId>
        <version>0.77.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-talent-v4</artifactId>
        <version>0.39.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-talent-v4beta1</artifactId>
        <version>0.39.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-talent</artifactId>
        <version>0.39.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-talent-v4</artifactId>
        <version>0.39.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-talent-v4beta1</artifactId>
        <version>0.39.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-tasks-v2beta3</artifactId>
        <version>0.86.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-tasks-v2beta2</artifactId>
        <version>0.86.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-tasks-v2beta3</artifactId>
        <version>0.86.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-tasks-v2</artifactId>
        <version>1.30.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-tasks-v2beta2</artifactId>
        <version>0.86.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-tasks-v2</artifactId>
        <version>1.30.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-tasks</artifactId>
        <version>1.30.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-texttospeech</artifactId>
        <version>1.2.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-texttospeech-v1beta1</artifactId>
        <version>0.85.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-texttospeech-v1</artifactId>
        <version>1.2.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-texttospeech-v1</artifactId>
        <version>1.2.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-texttospeech-v1beta1</artifactId>
        <version>0.85.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-trace-v1</artifactId>
        <version>1.2.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-trace-v1</artifactId>
        <version>1.2.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-trace-v2</artifactId>
        <version>1.2.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-trace-v2</artifactId>
        <version>1.2.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-trace</artifactId>
        <version>1.2.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-translate-v3beta1</artifactId>
        <version>0.78.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-translate-v3</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-translate-v3beta1</artifactId>
        <version>0.78.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-translate-v3</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-translate</artifactId>
        <version>1.95.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-video-intelligence-v1p3beta1</artifactId>
        <version>0.87.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-video-intelligence-v1p1beta1</artifactId>
        <version>0.87.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-video-intelligence</artifactId>
        <version>1.5.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-video-intelligence-v1beta2</artifactId>
        <version>0.87.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-video-intelligence-v1beta2</artifactId>
        <version>0.87.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-video-intelligence-v1p1beta1</artifactId>
        <version>0.87.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-video-intelligence-v1</artifactId>
        <version>1.5.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-video-intelligence-v1</artifactId>
        <version>1.5.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-video-intelligence-v1p2beta1</artifactId>
        <version>0.87.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-video-intelligence-v1p3beta1</artifactId>
        <version>0.87.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-video-intelligence-v1p2beta1</artifactId>
        <version>0.87.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1p4beta1</artifactId>
        <version>0.83.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-vision</artifactId>
        <version>1.100.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1p3beta1</artifactId>
        <version>0.83.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1</artifactId>
        <version>1.100.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1p1beta1</artifactId>
        <version>0.83.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1p1beta1</artifactId>
        <version>0.83.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1p3beta1</artifactId>
        <version>0.83.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1p4beta1</artifactId>
        <version>0.83.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1p2beta1</artifactId>
        <version>1.100.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1</artifactId>
        <version>1.100.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1p2beta1</artifactId>
        <version>1.100.4</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-websecurityscanner-v1alpha</artifactId>
        <version>0.84.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-websecurityscanner-v1beta</artifactId>
        <version>0.84.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-websecurityscanner-v1</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-websecurityscanner-v1alpha</artifactId>
        <version>0.84.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-websecurityscanner-v1beta</artifactId>
        <version>0.84.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-websecurityscanner-v1</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-websecurityscanner</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-webrisk</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-webrisk-v1</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-webrisk-v1beta1</artifactId>
        <version>0.35.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-webrisk-v1</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-webrisk-v1beta1</artifactId>
        <version>0.35.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-credentials</artifactId>
        <version>0.22.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-oauth2-http</artifactId>
        <version>0.22.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-appengine</artifactId>
        <version>0.22.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-core</artifactId>
        <version>1.93.9</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-core-grpc</artifactId>
        <version>1.93.9</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-core-http</artifactId>
        <version>1.93.9</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-biz</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-facade</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <version>2.3.0.RELEASE</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.github.lianjiatech</groupId>
      <artifactId>retrofit-spring-boot-starter</artifactId>
      <version>2.2.21</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.ly.turbomq</groupId>
      <artifactId>turbomq-client</artifactId>
      <version>4.2.5</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.ly.tcbase</groupId>
      <artifactId>cache</artifactId>
      <version>3.7.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok-mapstruct-binding</artifactId>
      <version>0.2.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <version>2.3.0.RELEASE</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-s3</artifactId>
      <version>1.12.139</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-mediator</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>oauth-client</artifactId>
      <version>1.0.6-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
      <id>lyRepository</id>
      <name>lyRepository</name>
      <url>https://nexus.17usoft.com/repository/mvn-all/</url>
    </repository>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <name>Central Repository</name>
      <url>https://repo.maven.apache.org/maven2</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <name>Central Repository</name>
      <url>https://repo.maven.apache.org/maven2</url>
    </pluginRepository>
  </pluginRepositories>
  <build>
    <sourceDirectory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/src/main/java</sourceDirectory>
    <scriptSourceDirectory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/src/main/scripts</scriptSourceDirectory>
    <testSourceDirectory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/src/test/java</testSourceDirectory>
    <outputDirectory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/target/classes</outputDirectory>
    <testOutputDirectory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/target/test-classes</testOutputDirectory>
    <resources>
      <resource>
        <directory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/src/main/resources</directory>
        <excludes>
          <exclude>test/*</exclude>
          <exclude>qa/*</exclude>
          <exclude>uat/*</exclude>
          <exclude>stage/*</exclude>
          <exclude>product/*</exclude>
        </excludes>
      </resource>
      <resource>
        <filtering>true</filtering>
        <directory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/src/main/resources/test</directory>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/src/test/resources</directory>
      </testResource>
    </testResources>
    <directory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/target</directory>
    <finalName>titc.java.pms.member.workbench.gateway</finalName>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>3.7.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.7.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>3.0.1</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.3.0.RELEASE</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
            <configuration>
              <mainClass>com.ly.titc.pms.member.gateway.GatewayApplication</mainClass>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <mainClass>com.ly.titc.pms.member.gateway.GatewayApplication</mainClass>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <executions>
          <execution>
            <id>default-compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <source>1.8</source>
              <target>1.8</target>
              <showWarnings>true</showWarnings>
              <encoding>UTF-8</encoding>
              <annotationProcessorPaths>
                <path>
                  <groupId>org.projectlombok</groupId>
                  <artifactId>lombok</artifactId>
                  <version>1.18.20</version>
                </path>
                <path>
                  <groupId>org.mapstruct</groupId>
                  <artifactId>mapstruct-processor</artifactId>
                  <version>1.4.2.Final</version>
                </path>
                <path>
                  <groupId>org.projectlombok</groupId>
                  <artifactId>lombok-mapstruct-binding</artifactId>
                  <version>0.2.0</version>
                </path>
              </annotationProcessorPaths>
            </configuration>
          </execution>
          <execution>
            <id>default-testCompile</id>
            <phase>test-compile</phase>
            <goals>
              <goal>testCompile</goal>
            </goals>
            <configuration>
              <source>1.8</source>
              <target>1.8</target>
              <showWarnings>true</showWarnings>
              <encoding>UTF-8</encoding>
              <annotationProcessorPaths>
                <path>
                  <groupId>org.projectlombok</groupId>
                  <artifactId>lombok</artifactId>
                  <version>1.18.20</version>
                </path>
                <path>
                  <groupId>org.mapstruct</groupId>
                  <artifactId>mapstruct-processor</artifactId>
                  <version>1.4.2.Final</version>
                </path>
                <path>
                  <groupId>org.projectlombok</groupId>
                  <artifactId>lombok-mapstruct-binding</artifactId>
                  <version>0.2.0</version>
                </path>
              </annotationProcessorPaths>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <showWarnings>true</showWarnings>
          <encoding>UTF-8</encoding>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>1.18.20</version>
            </path>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>1.4.2.Final</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>0.2.0</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.shalousun</groupId>
        <artifactId>smart-doc-maven-plugin</artifactId>
        <version>2.2.1</version>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>html</goal>
            </goals>
            <configuration>
              <configFile>./src/main/resources/test/smart-doc.json</configFile>
              <projectName>PMS在线文档api-doc</projectName>
              <excludes>
                <exclude>com.alibaba:fastjson</exclude>
              </excludes>
              <includes>
                <include>com.alibaba:fastjson</include>
              </includes>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <configFile>./src/main/resources/test/smart-doc.json</configFile>
          <projectName>PMS在线文档api-doc</projectName>
          <excludes>
            <exclude>com.alibaba:fastjson</exclude>
          </excludes>
          <includes>
            <include>com.alibaba:fastjson</include>
          </includes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <id>default-clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.3.1</version>
        <executions>
          <execution>
            <id>default-testResources</id>
            <phase>process-test-resources</phase>
            <goals>
              <goal>testResources</goal>
            </goals>
          </execution>
          <execution>
            <id>default-resources</id>
            <phase>process-resources</phase>
            <goals>
              <goal>resources</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.4.1</version>
        <executions>
          <execution>
            <id>default-jar</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.2.5</version>
        <executions>
          <execution>
            <id>default-test</id>
            <phase>test</phase>
            <goals>
              <goal>test</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-install-plugin</artifactId>
        <version>3.1.2</version>
        <executions>
          <execution>
            <id>default-install</id>
            <phase>install</phase>
            <goals>
              <goal>install</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>3.1.2</version>
        <executions>
          <execution>
            <id>default-deploy</id>
            <phase>deploy</phase>
            <goals>
              <goal>deploy</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
        <version>3.12.1</version>
        <executions>
          <execution>
            <id>default-site</id>
            <phase>site</phase>
            <goals>
              <goal>site</goal>
            </goals>
            <configuration>
              <outputDirectory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/target/site</outputDirectory>
              <reportPlugins>
                <reportPlugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-project-info-reports-plugin</artifactId>
                </reportPlugin>
              </reportPlugins>
            </configuration>
          </execution>
          <execution>
            <id>default-deploy</id>
            <phase>site-deploy</phase>
            <goals>
              <goal>deploy</goal>
            </goals>
            <configuration>
              <outputDirectory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/target/site</outputDirectory>
              <reportPlugins>
                <reportPlugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-project-info-reports-plugin</artifactId>
                </reportPlugin>
              </reportPlugins>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <outputDirectory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/target/site</outputDirectory>
          <reportPlugins>
            <reportPlugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-project-info-reports-plugin</artifactId>
            </reportPlugin>
          </reportPlugins>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <reporting>
    <outputDirectory>/Users/<USER>/workspace/tcworkspace3/pms-member/pms-member-gateway/target/site</outputDirectory>
  </reporting>
</project>
