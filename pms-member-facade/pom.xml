<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>pms-member-parent</artifactId>
    <groupId>com.ly.titc</groupId>
    <version>1.0.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>pms-member-facade</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-com</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.ly.tcbase</groupId>
      <artifactId>configcenterclient</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.lianjiatech</groupId>
      <artifactId>retrofit-spring-boot-starter</artifactId>
      <scope>provided</scope>
    </dependency>
    <!--spring-->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
      <scope>provided</scope>
    </dependency>
    <!-- aspectj -->
    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjweaver</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>com-component-sdk</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok-mapstruct-binding</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.dsf</groupId>
      <artifactId>dsf-spring-boot-starter</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.lianjiatech</groupId>
      <artifactId>retrofit-spring-boot-starter</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>jaxen</groupId>
      <artifactId>jaxen</artifactId>
    </dependency>
  </dependencies>
  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <excludes>
          <exclude>test/*</exclude>
          <exclude>qa/*</exclude>
          <exclude>stage/*</exclude>
          <exclude>product/*</exclude>
        </excludes>
      </resource>
      <resource>
        <directory>src/main/resources/${package.environment}</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <showWarnings>true</showWarnings>
          <encoding>UTF-8</encoding>
          <annotationProcessorPaths>
            <!-- 引入lombok-->
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <!-- 引入mapstruct-processor-->
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${mapstruct.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>${lombok-mapstruct-binding.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>