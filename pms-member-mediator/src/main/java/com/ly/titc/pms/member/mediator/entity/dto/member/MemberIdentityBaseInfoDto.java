package com.ly.titc.pms.member.mediator.entity.dto.member;

import com.ly.titc.springboot.elasticsearch.entity.DocId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 会员详细信息DTO
 *
 * <AUTHOR>
 * @classname MemberDto
 * @descrition 会员信息DTO
 * @since 2023/8/8 14:44
 */
@Data
@Accessors(chain = true)
public class MemberIdentityBaseInfoDto {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    private String masterCode;

    /**
     * 归属名称
     */
    private String masterName;

    /**
     * 自定义会员号
     */
    private String customizeMemberNo;

    /**
     * 会员手机号
     */
    private String mobile;

    /**
     * 证件类型
     */
    private Integer idType;

    /**
     * 证件类型
     */
    private String idTypeStr;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 会员昵称
     */
    private String nickName;

    /**
     * 会员真实姓名
     */
    private String realName;

    /**
     * 会员英文名
     */
    private String enName;

    /**
     * 性别1：男；2：女
     */
    private Integer gender;

    /**
     * 性别1：男；2：女
     */
    private String genderStr;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 民族
     */
    private String nation;

    /**
     * 语言
     */
    private String language;

    /**
     * 车牌号
     */
    private String numberPlate;

    /**
     * 会员来源
     */
    private String source;

    /**
     * 注册门店类型 集团:BLOC;门店:HOTEL
     */
    private String registerHotelType;

    /**
     * 注册门店(集团编号; 酒店编号)
     */
    private String registerHotel;

    /**
     * 注册门店名称
     */
    private String registerHotelName;


    /**
     * 会员状态 1 在用  0 注销
     */
    private Integer state;

    /**
     * 是否是黑名单用户 0 未拉黑 1 已拉黑
     */
    private Integer blackFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 会员卡信息(默认卡)
     */
    private MemberCardInfoDto memberCardInfo;



}
