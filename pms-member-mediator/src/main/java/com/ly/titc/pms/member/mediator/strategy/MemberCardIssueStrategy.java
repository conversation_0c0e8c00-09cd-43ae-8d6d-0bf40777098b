package com.ly.titc.pms.member.mediator.strategy;

import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardRequestDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardResultDto;

/**
 * 会员卡发放策略接口
 *
 * <AUTHOR>
 * @date 2025年06月26日
 */
public interface MemberCardIssueStrategy {

    /**
     * 判断是否支持该请求
     *
     * @param dto 发卡请求
     * @return true-支持，false-不支持
     */
    boolean supports(IssueMemberCardRequestDto dto);

    /**
     * 执行发卡逻辑
     *
     * @param dto 发卡请求
     * @return 发卡结果
     */
    IssueMemberCardResultDto execute(IssueMemberCardRequestDto dto);
}
