package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.member.dal.entity.po.PointConfigInfo;
import com.ly.titc.pms.member.mediator.entity.dto.point.PointConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.point.SavePointConfigDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @date 2025/6/25 19:34
 */
@Mapper(componentModel = "spring")
public interface PointConfigMedConverter {

    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator"),
    })
    PointConfigInfo convertDtoToPo(SavePointConfigDto dto);

    PointConfigDto convertPoToDto(PointConfigInfo configInfo);
}
