package com.ly.titc.pms.member.mediator.entity.dto.order;

import com.ly.titc.pms.spm.dubbo.entity.dto.activity.DiscountBenefitDto;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberOrderDetailDto
 * @Date：2024-12-13 16:28
 * @Filename：MemberOrderDetailDto
 */
@Data
public class MemberRechargeOrderDetailDto {

    /**
     * 会员订单号
     */
    private String memberOrderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 平台渠道描述
     */
    private String platformChannelDesc;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     *
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;
    /**
     * 归属名称
     */
    private String masterName;
    /**
     * 集团组code
     */
    private String clubCode;

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;


    /**
     * 订单状态  1 待支付，2  支付成功 3，支付失败 4.交易关闭  5.已退款
     */
    private Integer orderState;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 数量
     * （充值的时候是充值明细-数量）
     */
    private Integer num;

    /**
     * 单价
     * 充值详情时是指 面额售价
     */
    private BigDecimal price;

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 活动名称
     */
    private String activityName;


    /**
     * 储值金额相关信息
     */
    private MemberOrderRechargeDto orderRechargeDto;

    /**
     * 活动礼包相关信息
     */
    private List<DiscountBenefitDto> benefitDtoList;

    /**
     * 支付信息
     */
    private MemberOrderPayDto orderPayDto;
}
