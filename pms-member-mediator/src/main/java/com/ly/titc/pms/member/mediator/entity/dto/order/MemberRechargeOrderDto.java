package com.ly.titc.pms.member.mediator.entity.dto.order;

import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @Author：rui
 * @name：MemberOrderDto
 * @Date：2024-12-5 16:55
 * @Filename：MemberOrderDto
 */
@Data
public class MemberRechargeOrderDto {

    /**
     * 会员订单号
     */
    private String memberOrderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 归属名称
     */
    private String masterName;

    /**
     * 订单状态  1 待支付，2  支付成功 3，支付失败 4.交易关闭  5.已退款
     */
    private Integer orderState;

    /**
     * 备注
     */
    private String remark;

    /**
     * 已消费储值金额
     */
    private BigDecimal consumeAmount;

    /**
     * 已消费礼金金额
     */
    private BigDecimal consumeGiftAmount;

    /**
     * 储值本金金额
     */
    private BigDecimal capitalAmount;

    /**
     * 储值礼金
     */
    private BigDecimal giftAmount;

    /**
     * 赠送礼金过期时间
     */
    private String giftExpireDate;


    /**
     * 是否是长期有效
     */
    private boolean isPerpetualEffect;


    /**
     * 充值时间
     */
    private Timestamp gmtCreate;


}
