package com.ly.titc.pms.member.mediator.entity.message;

import com.ly.titc.cashier.dubbo.enums.CashierRefundStateEnum;
import com.ly.titc.common.mq.msg.MsgExt;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-8-29 20:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class RefundSuccessInfoMsgExt extends MsgExt {
    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 收银台交易号
     */
    private String onlinePayNo;

    /**
     * 收银台退款交易号
     */
    private String refundPayNo;

    /**
     * 支付中台退款流水号/会员卡退款流水号
     */
    private String refundTradeNo;

    /**
     * 业务支付申请号
     */
    private String bizRefundNo;

    /**
     * 系统来源
     */
    private String sourceSystem;

    /**
     * 渠道交易号
     */
    private String transactionId;

    /**
     * 渠道交易号
     */
    private String refundTransactionId;

    /**
     * 收款账户号
     */
    private String accountNo;

    /**
     * 收款账户名
     */
    private String accountName;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 支付渠道：微信，支付宝，银行，POS机
     */
    private String payChannel;

    /**
     * 支付产品
     */
    private String payProduct;

    /**
     * POS机终端ID
     */
    private String termId;

    /**
     * 当面付(付款条码),授权码
     */
    private String authCode;

    /**
     * 银行卡号
     */
    private String cardNo;

    /**
     * 退款状态 1 退款中、2 退款成功、3 退款失败
     * @see CashierRefundStateEnum
     */
    private Integer refundState;



    /**
     * 商品描述
     */
    private String goodsDes;

    /**
     * 失败原因
     */
    private String failReason;


    /**
     * 业务线唯一号
     */
    private String businessNo;

    /**
     *  WXBOOKINGPAY(微订房支付) ,PMSPAY(PMS支付)
     */
    private String businessType;

    /**
     * 业务请求体
     */
    private String businessNote;

    /**
     * 操作人
     */
    private String operator;
}
