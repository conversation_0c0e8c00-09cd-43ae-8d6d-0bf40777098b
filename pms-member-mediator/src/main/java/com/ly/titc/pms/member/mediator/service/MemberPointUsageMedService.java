package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.*;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 11:48
 */
public interface MemberPointUsageMedService {
    /**
     * 根据搜索条件分页查询配置
     * CRM调用
     */
    Pageable<MemberPointUsageRuleDto> page(QueryMemberMasterUsageDto dto);
    /**
     * 保存积分
     */
    List<MemberUsageRuleSaveResultDto> save(MemberPointUsageRuleConfigSaveDto dto);
    /**
     * 更新状态
     */
    Boolean updateState(UpdateMemberUsageStateDto dto);

    /**
     * 删除
     */
    Boolean delete(DeleteMemberUsageDto dto);

    /**
     * 未设置提醒
     */
    List<MemberUsageRuleSaveResultDto> remind(BaseDto dto);
}
