package com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-13 10:40
 */
@Data
public class ApplicableHotelsDto {

    /**
     * 是否全部门店
     */
    @NotNull
    private Boolean allHotels;

    /**
     * 适用门店列表
     */
    private List<String> hotelCodes;

    /**
     * 所有品牌标签
     */
    private List<CommonDto> brandLabels;

    /**
     * 所有区域标签
     */
    private List<CommonDto> areaLabels;

    /**
     * 所有地域(省市区)标签
     */
    private List<CommonDto> regionLabels;
}
