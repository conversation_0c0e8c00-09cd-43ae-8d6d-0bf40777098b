package com.ly.titc.pms.member.mediator.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.entity.CodeObject;
import com.ly.titc.mdm.entity.dto.selector.SelectorDimensionDto;
import com.ly.titc.mdm.entity.request.selector.ListSelectorComponentInfoReq;
import com.ly.titc.mdm.entity.request.selector.SaveSelectorComponentReq;
import com.ly.titc.mdm.entity.response.selector.SelectorComponentInfoResp;
import com.ly.titc.mdm.enums.BusinessUKEnum;
import com.ly.titc.mdm.enums.ComponetTypeEnum;
import com.ly.titc.mdm.enums.SelectorDimensionEnum;
import com.ly.titc.pms.member.mediator.entity.dto.usage.ScopeHotelsDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.UsageScopeHotelDto;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-1-8 19:40
 */
@Mapper(componentModel = "spring")
public interface HotelSelectorMedConverter {

   default SaveSelectorComponentReq convert(ScopeHotelsDto scopeHotelDto,String dbPrimaryKey,String dbUk) {
       SaveSelectorComponentReq req = new SaveSelectorComponentReq();
       req.setDbPrimaryKey(dbPrimaryKey);
       req.setDbUk(dbUk);
       req.setComponentType(ComponetTypeEnum.HOTEL.getCode());
       req.setBusinessUk(BusinessUKEnum.MEMBER.getCode());
       req.setDbPrimaryKey(dbPrimaryKey);
       List<SelectorDimensionDto> dimensions = new ArrayList<>();
       //酒店维度
       List<String> hotelCodes = scopeHotelDto.getHotelCodes();
       if (CollectionUtil.isNotEmpty(hotelCodes)) {
           SelectorDimensionDto dimension = new SelectorDimensionDto();
           dimension.setDimension(SelectorDimensionEnum.HOTEL.getDimension());
           List<JSONObject> dimensionValues = new ArrayList<>();
           hotelCodes.forEach(s -> {
               if(StringUtils.isEmpty(s)){
                   return;
               }
               JSONObject jsonObject = new JSONObject();
               jsonObject.put("hotelCode", s);
               dimensionValues.add(jsonObject);
           });
           if(CollectionUtil.isNotEmpty(dimensionValues)) {
               dimension.setDimensionValue(JSON.toJSONString(dimensionValues));
               dimensions.add(dimension);
           }
       }
       //酒馆维度
       List<CodeObject> clubLabels = scopeHotelDto.getClubLabels();
       if (CollectionUtil.isNotEmpty(clubLabels)) {
           SelectorDimensionDto dimension = new SelectorDimensionDto();
           dimension.setDimension(SelectorDimensionEnum.BLOC.getDimension());
           List<JSONObject> dimensionValues = new ArrayList<>();
           clubLabels.forEach(s -> {
               if(StringUtils.isEmpty(s.getCode())){
                   return;
               }
               JSONObject jsonObject = new JSONObject();
               jsonObject.put("blocCode", s.getCode());
               dimensionValues.add(jsonObject);
           });
           if(CollectionUtil.isNotEmpty(dimensionValues)) {
               dimension.setDimensionValue(JSON.toJSONString(dimensionValues));
               dimensions.add(dimension);
           }
       }
       //品牌维度
       List<CodeObject> brandLabels = scopeHotelDto.getBrandLabels();
       if (CollectionUtil.isNotEmpty(brandLabels)) {
           SelectorDimensionDto dimension = new SelectorDimensionDto();
           dimension.setDimension(SelectorDimensionEnum.BRAND.getDimension());
           List<JSONObject> dimensionValues = new ArrayList<>();
           brandLabels.forEach(s -> {
               if(StringUtils.isEmpty(s.getCode())){
                   return;
               }
               JSONObject jsonObject = new JSONObject();
               jsonObject.put("brandCode", s.getCode());
               dimensionValues.add(jsonObject);
           });
           if(CollectionUtil.isNotEmpty(dimensionValues)) {
               dimension.setDimensionValue(JSON.toJSONString(dimensionValues));
               dimensions.add(dimension);
           }
       }
         //区域维度
         List<CodeObject> areaLabels = scopeHotelDto.getAreaLabels();
            if (CollectionUtil.isNotEmpty(areaLabels)) {
                SelectorDimensionDto dimension = new SelectorDimensionDto();
                dimension.setDimension(SelectorDimensionEnum.AREA.getDimension());
                List<JSONObject> dimensionValues = new ArrayList<>();
                areaLabels.forEach(s -> {
                    if(StringUtils.isEmpty(s.getCode())){
                        return;
                    }
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("areaCode", s.getCode());
                    dimensionValues.add(jsonObject);
                });
                if(CollectionUtil.isNotEmpty(dimensionValues)) {
                    dimension.setDimensionValue(JSON.toJSONString(dimensionValues));
                    dimensions.add(dimension);
                }
            }
       //省市维度
         List<CodeObject> regionLabels = scopeHotelDto.getRegionLabels();
            if (CollectionUtil.isNotEmpty(regionLabels)) {
                SelectorDimensionDto dimension = new SelectorDimensionDto();
                dimension.setDimension(SelectorDimensionEnum.REGION.getDimension());
                List<JSONObject> dimensionValues = new ArrayList<>();
                regionLabels.forEach(s -> {
                    if(StringUtils.isEmpty(s.getCode())){
                        return;
                    }
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("regionId", s.getCode());
                    dimensionValues.add(jsonObject);
                });
                if(CollectionUtil.isNotEmpty(dimensionValues)) {
                    dimension.setDimensionValue(JSON.toJSONString(dimensionValues));
                    dimensions.add(dimension);
                }
            }

       req.setDimensions(dimensions);
       return req;
   }

    default Map<String,ScopeHotelsDto> convert(List<SelectorComponentInfoResp> componentInfoResps){
        Map<String,ScopeHotelsDto> scopeHotelDtoMap = new HashMap<>();
        if(CollectionUtil.isEmpty(componentInfoResps)){
            return scopeHotelDtoMap;
        }
       componentInfoResps.forEach(resp -> {
           String dbPrimaryKey = resp.getDbPrimaryKey();
           List<SelectorDimensionDto> dimensions =resp.getDimensions();
           ScopeHotelsDto scopeHotelsDto  = new ScopeHotelsDto();
              dimensions.forEach(selectorDimensionDto -> {
                    String dimension = selectorDimensionDto.getDimension();
                    String dimensionValue = selectorDimensionDto.getDimensionValue();
                    List<JSONObject> dimensionValues = JSON.parseArray(dimensionValue, JSONObject.class);
                  ;
                    switch (dimension){
                        case "HOTEL":
                            List<String> hotelCodes = dimensionValues.stream().map(jsonObject -> jsonObject.getString("hotelCode")).collect(Collectors.toList());
                            scopeHotelsDto.setHotelCodes(hotelCodes);
                            break;
                        case "BLOC":
                            List<CodeObject> clubLabels = dimensionValues.stream().map(jsonObject -> {
                                CodeObject codeObject = new CodeObject();
                                codeObject.setCode(jsonObject.getString("blocCode"));
                                return codeObject;
                            }).collect(Collectors.toList());
                            scopeHotelsDto.setClubLabels(clubLabels);
                            break;
                        case "BRAND":
                            List<CodeObject> brandLabels = dimensionValues.stream().map(jsonObject -> {
                                CodeObject codeObject = new CodeObject();
                                codeObject.setCode(jsonObject.getString("brandCode"));
                                return codeObject;
                            }).collect(Collectors.toList());
                            scopeHotelsDto.setBrandLabels(brandLabels);
                            break;
                        case "AREA":
                            List<CodeObject> areaLabels = dimensionValues.stream().map(jsonObject -> {
                                CodeObject codeObject = new CodeObject();
                                codeObject.setCode(jsonObject.getString("areaCode"));
                                return codeObject;
                            }).collect(Collectors.toList());
                            scopeHotelsDto.setAreaLabels(areaLabels);
                            break;
                        case "REGION":
                            List<CodeObject> regionLabels = dimensionValues.stream().map(jsonObject -> {
                                CodeObject codeObject = new CodeObject();
                                codeObject.setCode(jsonObject.getString("regionId"));
                                return codeObject;
                            }).collect(Collectors.toList());
                            scopeHotelsDto.setRegionLabels(regionLabels);
                            break;
                    }
                });
           scopeHotelDtoMap.put(dbPrimaryKey,scopeHotelsDto);
              });
        return scopeHotelDtoMap;

    }

    default ListSelectorComponentInfoReq convert(List<Long> ruleIds,String dbUk){
        ListSelectorComponentInfoReq req = new ListSelectorComponentInfoReq();
        req.setDbPrimaryKeys(ruleIds.stream().map(String::valueOf).collect(Collectors.toList()));
        req.setDbUk(dbUk);
        req.setComponentType(ComponetTypeEnum.HOTEL.getCode());
        req.setBusinessUk(BusinessUKEnum.MEMBER.getCode());
        return req;
    }
}
