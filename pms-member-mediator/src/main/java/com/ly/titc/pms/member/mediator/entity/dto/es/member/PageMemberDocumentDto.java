package com.ly.titc.pms.member.mediator.entity.dto.es.member;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * ES分页查询会员入参
 *
 * <AUTHOR>
 * @date 2024/11/13 15:46
 */
@Data
@Accessors(chain = true)
public class PageMemberDocumentDto {

    /**
     * 查询语句
     */
    private List<JSONObject> clauses;

    /**
     * 会员标签的查询语句
     */
    private List<JSONObject> tagClauses;

    /**
     * 会员卡的查询语句
     */
    private List<JSONObject> cardClauses;

    /**
     * 开始
     */
    private Integer from;

    /**
     * size
     */
    private Integer size;
}
