package com.ly.titc.pms.member.mediator.entity.dto.member.config;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：MemberRelatedConfigInfoDto
 * @Date：2024-11-25 11:08
 * @Filename：MemberRelatedConfigInfoDto
 */
@Data
@Accessors(chain = true)
public class SaveMemberRelatedConfigDto {

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 配置json
     */
    private String content;

    /**
     * 0 注册 1 手机验证设置 2 列表显示 3 列表快捷操作
     */
    private Integer type;

    /**
     * 操作人
     */
    private String operator;
}
