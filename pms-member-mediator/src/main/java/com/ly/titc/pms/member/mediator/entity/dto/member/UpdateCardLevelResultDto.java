package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 更新会员卡等级结果
 *
 * <AUTHOR>
 * @date 2024/11/25 17:04
 */
@Data
@Accessors(chain = true)
public class UpdateCardLevelResultDto {

    /**
     * 会员
     */
    private String memberNo;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 失败原因
     */
    private String reason;
}
