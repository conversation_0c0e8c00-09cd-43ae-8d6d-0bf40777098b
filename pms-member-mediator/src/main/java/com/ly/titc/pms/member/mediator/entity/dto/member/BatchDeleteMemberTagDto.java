package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;

import java.util.List;

/**
 * 批量删除会员标签
 *
 * <AUTHOR>
 * @date 2024/11/25 15:47
 */
@Data
public class BatchDeleteMemberTagDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    private String masterCode;

    /**
     * 会员编号
     */
    private List<String> memberNos;

    /**
     * 标签ID
     */
    private List<Long> tagIds;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 是否所有会员
     */
    private boolean isAll = false;
}
