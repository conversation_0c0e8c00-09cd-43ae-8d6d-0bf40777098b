package com.ly.titc.pms.member.mediator.rpc.dsf.oauth;

import com.ly.titc.common.entity.Response;
import com.ly.titc.oauth.api.MenuService;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.oauth.entity.request.menu.ListMenusByUserIdReq;
import com.ly.titc.oauth.entity.response.menu.MenuResp;
import com.ly.titc.pms.member.mediator.rpc.dsf.AbstractDsfServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * @Description: MenuDecorator
 * @Author: lixu
 * @Date: 2024/3/5
 */
@Slf4j
@Component
public class MenuDecorator extends AbstractDsfServiceProxy {

    private static final String S_NAME = "menu";



    public MenuResp listTrees(String tenantCode, String blocCode, Long userId, String projectCode, Long resourceId) {

        MenuService service = getProxy(MenuService.class, OAUTH_DSF_GS_NAME,S_NAME,oauthVersion);
        ListMenusByUserIdReq request = new ListMenusByUserIdReq();
        request
                .setBlocCode(blocCode)
                .setUserId(userId)
                .setProjectCode(projectCode)
                .setResourceId(resourceId)
                .setTenantCode(tenantCode)
                .setTrackingId(UserThreadHolder.getTrackingId());
        Response<MenuResp>  response =  service.listMenusByUserId(request);
        return Response.getValidateData(response);

    }

}
