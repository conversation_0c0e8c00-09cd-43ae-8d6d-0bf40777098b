package com.ly.titc.pms.member.mediator.entity.dto.order;

import com.ly.titc.cashier.dubbo.enums.CashierRefundStateEnum;
import com.ly.titc.cashier.dubbo.enums.PayProductEnum;
import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

/**
 * 退款结果返回
 *
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 11:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class RefundResultDto {

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 退款单号
     */
    private String memberRefundNo;

    /**
     * 收银台退款交易号
     */
    private String refundPayNo;

    /**
     * 支付产品
     * @see PayProductEnum
     */
    private String payProduct;

    /**
     * 渠道交易号
     */
    private String refundTransactionId;

    /**
     * 退款状态 1 退款中、2 退款成功、3 退款失败
     * @see CashierRefundStateEnum
     */
    private Integer refundState;

    /**
     * 备注
     */
    private String failReason;

}
