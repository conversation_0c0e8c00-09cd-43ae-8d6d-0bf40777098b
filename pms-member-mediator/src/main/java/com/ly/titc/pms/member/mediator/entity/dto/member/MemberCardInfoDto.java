package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：MemberCardInfoDto
 * @Date：2024-11-18 17:58
 * @Filename：MemberCardInfoDto
 */
@Data
@Accessors(chain = true)
public class MemberCardInfoDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡类型
     */
    private Integer cardType;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 会员卡等级名称
     */
    private String cardLevelName;

    /**
     * 会员卡生效时间
     */
    private String effectBeginDate;

    /**
     * 会员卡失效时间
     */
    private String effectEndDate;

    /**
     * 等级有效期（天）
     */
    private Integer validPeriod;

    /**
     * 是否长期有效 0 否 1 是
     */
    private Integer isLongTerm;

    /**
     * 最近一次等级变更时间
     */
    private LocalDateTime lastLevelChangeDate;

    /**
     * 发放门店类型 集团:BLOC;门店:HOTEL
     */
    private String issueHotelType;

    /**
     * 发放门店(集团编号; 酒店编号)
     */
    private String issueHotel;

    /**
     * 发卡门店名称
     */
    private String issueHotelName;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
