package com.ly.titc.pms.member.mediator.entity.dto.order;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-1-8 14:14
 */
@Data
@Accessors(chain = true)
public class MemberOrderPayDto {

    /**
     * 支付渠道：微信，支付宝，刷卡
     */
    private String payChannel;

    /**
     * 支付渠道描述
     */
    private String payChannelDesc;

    /**
     * 支付产品（microPay 条码支付 slotCardPay刷卡支付）
     */
    private String payProduct;

    /**
     * 支付厂商： FUIOU 富友 WX 微信 ZFB 支付宝 TALN 通联 UNP 银联
     */
    private String payVendor;

    /**
     * 支付方式： wxPay 微信支付； aliPay 支付宝支付； unionpay 银联支付； posSlotCard 国内卡刷卡； posWildcard 境外卡刷卡； posWxPay pos微信支付； posAliPay pos支付宝支付； posUnionPay pos银联
     */
    private String payType;

    /**
     * 支付方式： wxPay 微信支付； aliPay 支付宝支付； unionpay 银联支付； posSlotCard 国内卡刷卡； posWildcard 境外卡刷卡； posWxPay pos微信支付； posAliPay pos支付宝支付； posUnionPay pos银联
     */
    private String payTypeName;

    /**
     * POS机终端ID
     */
    private String termId;

    /**
     * 支付完成时间
     */
    private String payedTime;
    /**
     * 订单支付状态  1 待支付，2  支付成功 3，支付失败 4.交易关闭,5 退款
     */
    private Integer payState;

    /**
     * 退款时间
     */
    private String refundTime;

    /**
     * 渠道交易号（支付流水号）
     */
    private String transactionId;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

}
