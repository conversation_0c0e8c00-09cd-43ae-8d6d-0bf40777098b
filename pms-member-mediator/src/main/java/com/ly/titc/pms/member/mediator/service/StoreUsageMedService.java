package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.mediator.entity.dto.point.BlocScopeUsageDto;
import com.ly.titc.pms.member.mediator.entity.dto.point.ListBlocScopeUsageDto;
import com.ly.titc.pms.member.mediator.entity.dto.store.*;
import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleResultDto;

import java.util.List;

/**
 * 储值规则
 *
 * <AUTHOR>
 * @date 2025/6/26 10:47
 */
public interface StoreUsageMedService {

    /**
     * 查询储值规则
     *
     * @param dto
     * @return
     */
    List<StoreUsageRuleDto> listStoreRule(ListStoreRuleDto dto);

    /**
     * 分页查询储值使用规则
     *
     * @param dto
     * @return
     */
    Pageable<StoreUsageRuleDetailDto> pageStoreUsageRule(PageStoreUsageDto dto);

    /**
     * 保存储值使用规则
     *
     * @param dto
     * @return
     */
    SaveStoreUsageRuleResultDto saveStoreUsageRule(SaveStoreUsageRuleDto dto);

    /**
     * 更新储值使用规则状态
     *
     * @param dto
     * @return
     */
    Boolean updateState(UpdateStoreUsageStateDto dto);

    /**
     * 删除规则
     *
     * @param dto
     */
    void deleteStoreUsageRule(DeleteStoreUsageDto dto);

    /**
     * 查询集团适用规则
     *
     * @param dto
     * @return
     */
    List<BlocScopeUsageDto> listBlocScopeUsageRule(ListBlocScopeUsageDto dto);

}
