package com.ly.titc.pms.member.mediator.entity.dto.member.config;

import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：PageMemberTagConfigInfoReq
 * @Date：2024-11-8 11:37
 * @Filename：PageMemberTagConfigInfoReq
 */
@Data
public class PageMemberTagConfigDto {

    private Integer masterType;

    private String masterCode;

    private Integer type;

    private List<Integer> typeList;

    private String name;

    /**
     * 页码
     */
    private Integer pageIndex = 1;

    /**
     * pageSize
     */
    private Integer pageSize = 20;

}
