package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PageOrderQueryDto;

/**
 * @Author：rui
 * @name：MemberAssetMedService
 * @Date：2024-12-5 15:08
 * @Filename：MemberAssetMedService
 */
public interface MemberAssetMedService {

    Pageable<MemberRechargeOrderDto> pageMemberStoredRechargeRecord(PageOrderQueryDto dto);

    MemberRechargeOrderDetailDto getMemberStoredRechargeRecord(String memberOrderNo);

}
