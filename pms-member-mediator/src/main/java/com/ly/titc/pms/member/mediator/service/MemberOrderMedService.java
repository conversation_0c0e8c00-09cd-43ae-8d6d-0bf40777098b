package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.pms.member.mediator.entity.dto.order.*;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-26 11:49
 */
public interface MemberOrderMedService{


    /**
     * 创建订单
     */
    <T> CreateOrderResultDto createOrder(CreateOrderDto<T> dto);

    /**
     * 发起支付
     */
    MemberPayUnifiedOrderDto payUnifiedOrder(MemberPayOrderDto dto);

    /**
     * 查询支付结果
     */

    PayOrderResultDto getPayState(GetPayStateDto dto);

    /**
     * 申请退款
     */
    RefundResultDto refundOrder(RefundOrderDto dto);

    /**
     * 查询退款结果
     */
    RefundResultDto getRefundState(GetRefundStateDto dto);

}
