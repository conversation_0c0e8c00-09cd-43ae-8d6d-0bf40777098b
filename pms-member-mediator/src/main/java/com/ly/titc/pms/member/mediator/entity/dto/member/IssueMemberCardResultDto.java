package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 发放会员卡结果DTO
 *
 * <AUTHOR>
 * @date 2024/12/25 10:00
 */
@Data
@Accessors(chain = true)
public class IssueMemberCardResultDto {

    /**
     * 是否发放成功
     */
    private boolean success;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 发放的会员卡等级
     */
    private Integer cardLevel;

    /**
     * 发放的会员卡等级名称
     */
    private String cardLevelName;

    /**
     * 操作类型（REGISTER: 注册会员, UPGRADE: 会员升级）
     */
    private String operationType;

    public static IssueMemberCardResultDto success(String memberNo, String memberCardNo, Integer cardLevel, String cardLevelName, String operationType) {
        return new IssueMemberCardResultDto()
                .setSuccess(true)
                .setMemberNo(memberNo)
                .setMemberCardNo(memberCardNo)
                .setCardLevel(cardLevel)
                .setCardLevelName(cardLevelName)
                .setOperationType(operationType);
    }

    public static IssueMemberCardResultDto failure(String failureReason) {
        return new IssueMemberCardResultDto()
                .setSuccess(false)
                .setFailureReason(failureReason);
    }

}
