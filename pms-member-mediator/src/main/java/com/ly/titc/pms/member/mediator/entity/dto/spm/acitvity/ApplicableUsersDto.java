package com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-13 11:04
 */
@Data
public class ApplicableUsersDto {

    /**
     * 是否全部用户
     */
    @NotNull
    private Boolean allUsers;

    /**
     * 是否包含散客
     */
    private Boolean includeGuest;

    /**
     * 会员标签
     */
    private ApplicableMembersDto applicableMembersDto;

}
