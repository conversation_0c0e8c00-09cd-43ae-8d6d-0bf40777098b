package com.ly.titc.pms.member.mediator.manager;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.mediator.handler.order.AbstractOrderSceneHandler;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 10:37
 */
public class OrderSceneManager {
    private static  final Map<String, AbstractOrderSceneHandler> instance = new HashMap<>();

    /**
     * 处理类
     * @param handler
     */
    public static void putInstance(AbstractOrderSceneHandler handler){
        String key = handler.getScene();
        instance.put(key,handler);
    }

    /**
     * 获取场景处理类
     */
    public static  AbstractOrderSceneHandler getInstance(String scene){
        AbstractOrderSceneHandler<?> handler = instance.get(scene);
        if(handler ==null){
            throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00001);
        }
        return handler;
    }
}
