package com.ly.titc.pms.member.mediator.entity.dto.point;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-24 14:25
 */
@Data
@Accessors(chain = true)
public class PointConfigDto {

    /**
     * 主键ID 不为空表示编辑
     */
    private Long id;

    /**
     * 归属主体类型
     */
    private Integer masterType;

    /**
     * 归属主体code
     */
    private String masterCode;
    /**
     * 酒馆组
     */
    private String clubCode;

    /**
     * 集团code
     */
    private String blocCode;
    /**
     * 门店code
     */
    private String hotelCode;

    /**
     * 积分有效期 0 1长期有效
     */
    private Integer pointLimit;

    /**
     * 积分有效期单位 年 YEAR 月:MONTH 日; DAY
     */
    private String pointLimitUnit;

    /**
     * 长期有效 0 否 1 是
     */
    private Integer pointLimitLong;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;


}
