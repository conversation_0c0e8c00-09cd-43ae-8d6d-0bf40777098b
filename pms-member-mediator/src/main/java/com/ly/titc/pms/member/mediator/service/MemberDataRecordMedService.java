package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.dubbo.entity.request.data.GetMemberPayRegisterStatisticsReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPayRegisterStatisticsResp;
import com.ly.titc.pms.member.mediator.entity.dto.data.*;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberDataRecordMedServiceImpl
 * @Date：2024-12-11 21:42
 * @Filename：MemberDataRecordMedServiceImpl
 */
public interface MemberDataRecordMedService {

    /**
     * 新增入住记录
     *
     * @param dto
     */
    String addRecord(AddMemberCheckInRecordDto dto);

    /**
     * 入住记录汇总
     *
     * @param memberNo
     */
    void summaryCheckInRecord(String memberNo);

    /**
     * 分页查询入住记录
     *
     * @param dto
     * @return
     */
    Pageable<CheckInRecordDto> pageCheckinRecord(PageCheckinRecordDto dto);

    /**
     * 查询入住统计
     *
     * @param memberNo
     * @return
     */
    CheckInStatisticsDto getCheckInStatistics(String memberNo);

    /**
     * 查询门店入住统计
     *
     * @param memberNo
     * @return
     */
    CheckInStatisticsDto getHotelCheckInStatistics(String memberNo, String hotelCode);

    /**
     * 根据会员查询入住统计
     *
     * @param memberNos
     * @return
     */
    List<CheckInStatisticsDto> listByMemberNos(List<String> memberNos);

    /**
     * 分页查询购卡记录
     *
     * @param dto
     * @return
     */
    Pageable<PurchaseCardRecordDto> pagePurchaseCardRecord(PagePurchaseCardRecordDto dto);

    /**
     * 分页查询升降级记录
     *
     * @param dto
     * @return
     */
    Pageable<MemberLevelChangeRecordDto> pageMemberLevelChangeRecord(PageLevelChangeRecordDto dto);


    /**
     * 查询时间范围内，会员充值支付金额汇总 +  会员注册数量
     */
    MemberPayRegisterStatisticsResp getMemberPayStatistics(GetMemberPayRegisterStatisticsReq req);

}
