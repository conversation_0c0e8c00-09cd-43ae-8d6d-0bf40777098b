package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.*;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 11:49
 */
public interface MemberStoreUsageMedService {
    /**
     * 根据搜索条件分页查询配置
     * CRM调用
     */
    Pageable<MemberStoreUsageRuleDto>  page (QueryMemberMasterUsageDto dto);
    /**
     * 新增储值设置
     */
    List<MemberUsageRuleSaveResultDto> save(MemberStoreUsageRuleConfigSaveDto dto);
    /**
     * 更新状态
     */
    Boolean updateState(UpdateMemberUsageStateDto dto);

    /**
     * 删除
     */
    Boolean delete(DeleteMemberUsageDto dto);

    /**
     * 提醒
     */

    List<MemberUsageRuleSaveResultDto> remind(BaseDto dto);
}
