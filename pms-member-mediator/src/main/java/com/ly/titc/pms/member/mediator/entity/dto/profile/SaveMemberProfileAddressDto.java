package com.ly.titc.pms.member.mediator.entity.dto.profile;

import lombok.Data;

/**
 * 常用地址
 *
 * <AUTHOR>
 * @date 2024/11/19 17:54
 */
@Data
public class SaveMemberProfileAddressDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 常用地址编号
     */
    private Long addressNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 国家id
     */
    private Long countryId;

    /**
     * 国家
     */
    private String country;

    /**
     * 省id
     */
    private Long provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域id
     */
    private Long districtId;

    /**
     * 区域
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 标签
     */
    private String tag;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

}
