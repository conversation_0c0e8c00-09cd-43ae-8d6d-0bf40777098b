package com.ly.titc.pms.member.mediator.manager;

import com.ly.titc.pms.member.mediator.handler.check.AbstractRegisterCheckHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author：rui
 * @name：ScheduleHandlerFactory
 * @Date：2024-11-21 14:49
 * @Filename：ScheduleHandlerFactory
 */
@Slf4j
public class RegisterCheckHandlerManager {

    /**
     * MAP
     */
    private static Map<Integer, AbstractRegisterCheckHandler> MAP = new HashMap<>();

    public static AbstractRegisterCheckHandler getHandler(Integer action) {
        return MAP.get(action);
    }

    /**
     * 添加处理类
     * @param handler 处理方法
     */
    public static void putHandler(Integer action, AbstractRegisterCheckHandler handler) {
        MAP.put(action, handler);
    }
}
