package com.ly.titc.pms.member.mediator.handler.schedule;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author：rui
 * @name：ScheduleHandlerFactory
 * @Date：2024-11-21 14:49
 * @Filename：ScheduleHandlerFactory
 */
@Slf4j
public class ScheduleHandlerFactory {

    /**
     * MAP
     */
    private static Map<Integer, AbstractScheduleHandler> MAP = new HashMap<>();

    public static AbstractScheduleHandler getHandler(Integer action) {
        return MAP.get(action);
    }

    /**
     * 添加处理类
     * @param handler 处理方法
     */
    public static void putHandler(Integer action, AbstractScheduleHandler handler) {
        MAP.put(action, handler);
    }
}
