package com.ly.titc.pms.member.mediator.rpc.dsf.mdm;

import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.api.BlocDictDsfService;
import com.ly.titc.mdm.api.BrandService;
import com.ly.titc.mdm.entity.dto.dict.DictItemTreeDto;
import com.ly.titc.mdm.entity.request.dict.BlocDictItemQueryReq;
import com.ly.titc.mdm.entity.response.dict.BlocDictItemResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 字典
 * @Author：rui
 * @name：DictDecorator
 * @Date：2024-12-4 16:29
 * @Filename：DictDecorator
 */
@Slf4j
@Component
public class DictDecorator {

    @DubboReference(protocol = "dsf", providedBy = "${mdm-dsf-group}", version = "${mdm-dsf-service-version}")
    private BlocDictDsfService blocDsfService;


    public List<DictItemTreeDto> listDictItemTree(String dictCode, String trackingId) {
        BlocDictItemQueryReq req = new BlocDictItemQueryReq();
        req.setDictCode(dictCode);
        req.setTrackingId(trackingId);
        return Response.getValidateData(blocDsfService.listDictItemTree(req));
    }
}
