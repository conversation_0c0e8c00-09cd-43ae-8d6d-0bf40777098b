package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.PayCashierConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.PayCashierConfigQueryDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.TermListDto;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-3 19:22
 */
public interface MemberCashierMedService {


    /**
     * 根据集团编号或会员场景查询收银设置
     */
    PayCashierConfigDto getCashierConfig(PayCashierConfigQueryDto dto);

    /**
     * 获取支付终端
     */
    List<TermListDto> getTermList(BaseDto dto);
}
