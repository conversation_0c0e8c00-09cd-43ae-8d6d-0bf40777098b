package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelUpgradeRuleResp;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.entity.bo.ListCardLevelConfigBo;
import com.ly.titc.pms.member.entity.bo.PageCardLevelConfigBo;
import com.ly.titc.pms.member.entity.bo.PageCardLevelRelegationRuleBo;
import com.ly.titc.pms.member.entity.bo.PageCardLevelUpgradeRuleBo;
import com.ly.titc.pms.member.mediator.entity.dto.card.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/25 14:21
 */
@Mapper(componentModel = "spring")
public interface CardConfigMedConverter {

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardConfigInfo convertDtoToPo(SaveCardConfigDto dto);

    @Mappings({
            @Mapping(target = "cardId", source = "cardId"),
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardNoRuleInfo convertDtoToPo(SaveCardNoRuleDto dto, Long cardId, String operator);

    @Mappings({
            @Mapping(target = "cardId", source = "cardId"),
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardApplicableDataMapping convertDtoToPo(SaveCardApplicableDataMappingDto mapping, Long cardId, String operator);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardLevelUpgradeRuleInfo convertDtoToPo(SaveCardLevelUpgradeRuleDto dto);

    @Mappings({
            @Mapping(target = "cardId", source = "cardId"),
            @Mapping(target = "upgradeRuleId", source = "ruleId"),
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardLevelUpgradeRuleDetailInfo convertDtoToPo(SaveCardLevelUpgradeRuleDetailDto ruleDetail, Long cardId, Long ruleId, String operator);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardLevelRelegationRuleInfo convertDtoToPo(SaveCardLevelRelegationRuleDto dto);

    @Mappings({
            @Mapping(target = "cardId", source = "cardId"),
            @Mapping(target = "relegationRuleId", source = "ruleId"),
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardLevelRelegationRuleDetailInfo convertDtoToPo(SaveCardLevelRelegationRuleDetailDto detail, Long cardId, Long ruleId, String operator);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardLevelConfigInfo convertDtoToPo(SaveCardLevelConfigDto dto);

    @Mappings({
            @Mapping(target = "cardId", source = "cardId"),
            @Mapping(target = "cardLevel", source = "cardLevel"),
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    CardLevelPrivilegeConfigInfo convertDtoToPo(SaveCardLevelPrivilegeConfigDto config, Long cardId, Integer cardLevel, String operator);

    ListCardLevelConfigBo convertDtoToBo(ListCardLevelConfigDto dto);

    CardLevelConfigDto convertPoToDto(CardLevelConfigInfo cardLevelConfigInfo);

    CardConfigDto convertPoToDto(CardConfigInfo defaultCard);

    PageCardLevelConfigBo convertDtoToBo(PageCardLevelConfigDto dto);

    CardNoRuleDto convertPoToDto(CardNoRuleInfo cardNoRuleInfo);

    CardLevelUpgradeRuleDto convertPoToDto(CardLevelUpgradeRuleInfo cardLevelUpgradeRuleInfo);

    CardLevelUpgradeRuleDetailDto convertPoToDto(CardLevelUpgradeRuleDetailInfo cardLevelUpgradeRuleDetailInfo);

    PageCardLevelUpgradeRuleBo convertDtoToBo(PageCardLevelUpgradeRuleDto dto);

    default List<CardLevelUpgradeRuleDto> convertUpgradeRuleResp(List<CardLevelUpgradeRuleInfo> ruleInfos,
                                                                 List<CardConfigInfo> cardConfigInfos,
                                                                 Map<String, String> cardLevelMap) {
        List<CardLevelUpgradeRuleDto> respList = ruleInfos.stream().map(this::convertPoToDto).collect(Collectors.toList());
        Map<Long, CardConfigInfo> cardConfigMap = cardConfigInfos.stream().collect(Collectors.toMap(CardConfigInfo::getId, Function.identity()));
        respList.forEach(item -> {
            CardConfigInfo cardConfigInfoDto = cardConfigMap.get(item.getCardId());
            if (cardConfigInfoDto != null) {
                item.setCardName(cardConfigInfoDto.getCardName());
            }
            item.setSourceLevelName(cardLevelMap.get(String.format("%s_%s", item.getCardId(), item.getSourceLevel())));
            item.setTargetLevelName(cardLevelMap.get(String.format("%s_%s", item.getCardId(), item.getTargetLevel())));
        });
        return respList;
    }

    CardLevelRelegationRuleDto convertPoToDto(CardLevelRelegationRuleInfo cardLevelRelegationRuleInfo);

    CardLevelRelegationRuleDetailDto convertPoToDto(CardLevelRelegationRuleDetailInfo cardLevelRelegationRuleDetailInfo);

    PageCardLevelRelegationRuleBo convertDtoToBo(PageCardLevelRelegationRuleDto dto);

    default List<CardLevelRelegationRuleDto> convertRelegationRuleResp(List<CardLevelRelegationRuleInfo> ruleInfos,
                                                                       List<CardConfigInfo> cardConfigInfos,
                                                                       Map<String, String> cardLevelMap) {
        List<CardLevelRelegationRuleDto> respList = ruleInfos.stream().map(this::convertPoToDto).collect(Collectors.toList());
        Map<Long, CardConfigInfo> cardConfigMap = cardConfigInfos.stream().collect(Collectors.toMap(CardConfigInfo::getId, Function.identity()));
        respList.forEach(item -> {
            CardConfigInfo cardConfigInfoDto = cardConfigMap.get(item.getCardId());
            if (cardConfigInfoDto != null) {
                item.setCardName(cardConfigInfoDto.getCardName());
            }
            item.setSourceLevelName(cardLevelMap.get(String.format("%s_%s", item.getCardId(), item.getSourceLevel())));
            item.setTargetLevelName(cardLevelMap.get(String.format("%s_%s", item.getCardId(), item.getTargetLevel())));
        });
        return respList;
    }
}
