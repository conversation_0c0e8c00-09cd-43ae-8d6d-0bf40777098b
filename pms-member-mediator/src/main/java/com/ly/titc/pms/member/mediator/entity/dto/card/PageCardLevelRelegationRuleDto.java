package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * @Author：rui
 * @name：PageRelegationRuleReq
 * @Date：2024-11-7 20:25
 * @Filename：PageRelegationRuleReq
 */
@Data
@Accessors(chain = true)
public class PageCardLevelRelegationRuleDto {

    private Integer masterType;

    private String masterCode;

    private String name;

    private Integer sourceLevel;

    private Integer state;

    /**
     * 页码
     */
    private Integer pageIndex = 1;

    /**
     * pageSize
     */
    private Integer pageSize = 20;
}
