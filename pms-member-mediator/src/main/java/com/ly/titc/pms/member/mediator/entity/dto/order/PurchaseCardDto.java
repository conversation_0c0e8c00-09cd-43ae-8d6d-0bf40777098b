package com.ly.titc.pms.member.mediator.entity.dto.order;

import lombok.Data;

/**
 * 购卡DTO
 *
 * <AUTHOR>
 * @date 2024/12/10 14:14
 */
@Data
public class PurchaseCardDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    private String masterCode;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 会员卡类型
     */
    private Integer cardType;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 发放门店类型 集团:BLOC;门店:HOTEL
     */
    private String issueHotelType;

    /**
     * 发放门店(集团编号; 酒店编号)
     */
    private String issueHotel;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

}
