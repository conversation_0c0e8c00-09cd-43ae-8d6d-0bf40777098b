package com.ly.titc.pms.member.mediator.rpc.dubbo.asset;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberRecordOPResultResp;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberPointOpDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-28 16:25
 */
@Slf4j
@Service
public class MemberPointOpDecorator {

    @DubboReference(group = "${asset-dsf-dubbo-group}")
    private MemberPointOpDubboService opDubboService;


    /**
     * 获得积分 （调整加积分）
     * @param req
     */
    public MemberRecordOPResultResp receive(ReceiveMemberPointReq req) {
        req.setTrackingId(TraceNoUtil.getTraceNo());
        Response<MemberRecordOPResultResp> response = opDubboService.receive(req);
       return Response.getValidateData(response);
    }

    /**
     * 获得后撤回积分
     */
    public MemberRecordOPResultResp receiveRollback(ReceiveRollBackMemberPointReq req) {
        req.setTrackingId(TraceNoUtil.getTraceNo());
        Response<MemberRecordOPResultResp> response = opDubboService.receiveRollback(req);
        return Response.getValidateData(response);
    }

    /**
     * 消费积分
     */
    public MemberRecordOPResultResp consume(ConsumeMemberPointReq req) {
        req.setTrackingId(TraceNoUtil.getTraceNo());
        Response<MemberRecordOPResultResp> response = opDubboService.consume(req);
        return Response.getValidateData(response);
    }


    /**
     *消费撤回(退款)
     */
    public MemberRecordOPResultResp consumeRollback(ConsumeRollBackMemberPointReq req) {
        req.setTrackingId(TraceNoUtil.getTraceNo());
        Response<MemberRecordOPResultResp> response = opDubboService.consumeRollback(req);
        return Response.getValidateData(response);
    }


    public List<MemberPointsFlowInfoResp> listConsumeRecords(ListMemberPointConsumeForBusinessReq req) {
        req.setTrackingId(TraceNoUtil.getTraceNo());
        Response<List<MemberPointsFlowInfoResp>> response = opDubboService.listConsumeRecords(req);
        return Response.getValidateData(response);
    }




}
