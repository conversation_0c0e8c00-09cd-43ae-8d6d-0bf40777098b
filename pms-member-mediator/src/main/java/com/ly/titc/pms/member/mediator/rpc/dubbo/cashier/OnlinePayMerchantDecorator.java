package com.ly.titc.pms.member.mediator.rpc.dubbo.cashier;

import com.alibaba.fastjson2.JSON;
import com.ly.titc.cashier.dubbo.entity.request.merchant.CashierHotelPaySettingReq;
import com.ly.titc.cashier.dubbo.entity.response.merchant.CashierPayOpenStateResp;
import com.ly.titc.cashier.dubbo.interfaces.merchant.OnlinePayMerchantDubboService;
import com.ly.titc.common.entity.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-9-24 17:22
 */
@Slf4j
@Service
public class OnlinePayMerchantDecorator {
    @DubboReference(group = "${cashier-dsf-dubbo-group}",timeout = 30000)
    private OnlinePayMerchantDubboService merchantDubboService;


    /**
     * 获取线上支付状态
     * @param req
     * @return
     */
    public CashierPayOpenStateResp hotelPaySetting(CashierHotelPaySettingReq req){
        log.info("获取线上支付开通状态请求参数：{}", JSON.toJSONString(req));
        Response<CashierPayOpenStateResp> response = merchantDubboService.hotelPaySetting(req);
        log.info("获取线上支付开通状态响应参数：{}",JSON.toJSONString(response));
        return Response.getValidateData(response);
    }
}
