package com.ly.titc.pms.member.mediator.entity.dto.store;

import lombok.Data;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:10
 */
@Data
@Accessors(chain = true)
public class UpdateStoreUsageStateDto  {

    /**
     * 归属主体类型
     */
    private Integer masterType;

    /**
     * 归属主体值
     */
    private String masterCode;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 操作人
     */
    private String operator;

}
