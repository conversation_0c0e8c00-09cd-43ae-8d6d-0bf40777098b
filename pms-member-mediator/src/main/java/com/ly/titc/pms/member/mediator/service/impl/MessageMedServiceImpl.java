package com.ly.titc.pms.member.mediator.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.titc.cc.dubbo.enums.oa.SendTypeEnum;
import com.ly.titc.common.config.ConfigCenter;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.SystemConstant;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.dubbo.entity.message.MemberEventMsg;
import com.ly.titc.pms.member.mediator.rpc.dubbo.common.OaMessageDubboInterface;
import com.ly.titc.pms.member.mediator.rpc.dubbo.message.MessageDecorator;
import com.ly.titc.pms.member.mediator.service.MessageMedService;
import com.ly.titc.springboot.mq.producer.TurboMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_EVENT;

/**
 * <AUTHOR>
 * @title: OaMessageMedService
 * @projectName pms-member
 * @description: 消息通知
 * @date 2023/10/24 10:28
 */
@Slf4j
@Service
public class MessageMedServiceImpl implements MessageMedService {

    @Resource(type = OaMessageDubboInterface.class)
    private OaMessageDubboInterface oaMessageDecorator;

    @Resource
    private MessageDecorator messageDecorator;

    @Resource(type = TurboMQProducer.class)
    private TurboMQProducer producer;

    @Resource
    private RedisFactory redisFactory;

    @Override
    public String sendSceneSms(Integer masterType, String masterCode, String mobile, String scene) {
        String key = CommonUtil.concat(CommonConstant.REGISTER_SMS_PREFIX, scene, mobile);
        String traceNo = TraceNoUtil.getTraceNo();
        Boolean result = redisFactory.setNx(key, 1, mobile);
        if (!result) {
            log.warn("this mobile register send sms is processing...scene:{};mobile:{}", scene, mobile);
            throw new ServiceException(RespCodeEnum.MEMBER_10027);
        }
        String verifyKey = CommonUtil.concat(CommonConstant.REGISTER_VERIFY_CODE_PREFIX, scene, mobile);
        try {
            // 先从redis种获取验证码，如果存在，重新生成，并覆盖
            String verifyCode = redisFactory.getString(verifyKey);
            if (StringUtils.isNotEmpty(verifyCode)) {
                // 先删除
                redisFactory.del(verifyKey);
            }
            // 重新生成
            verifyCode = RandomStringUtils.randomNumeric(4);
            log.info("verifyCode:{}", verifyCode);
            // 5分钟过期
            redisFactory.setString(verifyKey, 300, verifyCode);
            // 发送短信
            messageDecorator.sendText(masterType, masterCode, mobile, verifyCode);
            return verifyCode;
        } catch (Exception e) {
            log.error("send register sms error, traceNo:{}, mobile:{}", traceNo, mobile, e);
            redisFactory.del(verifyKey);
            throw new ServiceException("会员发送短信验证码失败", RespCodeEnum.CODE_500.getCode());
        } finally {
            redisFactory.del(key);
        }
    }

    @Override
    public void verifyRegisterSms(String mobile, String code, String scene) {
        String key = CommonUtil.concat(CommonConstant.REGISTER_VERIFY_CODE_PREFIX, scene, mobile);
        String verifyCode = redisFactory.getString(key);
        if (StringUtils.isEmpty(verifyCode)) {
            throw new ServiceException(RespCodeEnum.MEMBER_10030);
        }
        if (!redisFactory.getString(key).equals(code)) {
            throw new ServiceException(RespCodeEnum.MEMBER_10028);
        }
        redisFactory.del(key);
    }

    @Override
    public void sendOaAlertMsg(String trackingId, String title, String context) {
        List<String> list = alterMsgReceive();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        oaMessageDecorator.sendText(trackingId, SendTypeEnum.QYWX.getType(), title, null, context, list);
    }

    @Override
    public void sendMemberEventMsg(String trackingId, MemberEventMsg memberEventMsg) {
        String msg = JSONObject.toJSONString(memberEventMsg);
        try {
            log.info("发送会员事件消息, msg:{}", msg);
            SendResult sendResult = producer.sendMsgWithTag(TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_EVENT, msg);
            log.info("发送会员事件消息, sendResult:{}", sendResult);
        } catch (Exception e) {
            log.error("发送会员事件消息失败, topic:{}, tag:{}, msg:{}", TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_EVENT, msg);
            sendMqFailOaContext(trackingId, TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_EVENT, msg, e.getMessage());
        }
    }

    /**
     * 消息投递MQ失败，人工提醒
     *
     * @param trackingId
     * @param msg
     */
    private void sendMqFailOaContext(String trackingId, String topic, String tag, String msg, String message) {
        try {
            String title = "【投递MQ消息失败】";
            String content = title + Constant.HTML_RETURN_NEWLINE +
                    "trackingId:" + trackingId + Constant.HTML_RETURN_NEWLINE +
                    "topic:" + topic + Constant.HTML_RETURN_NEWLINE +
                    "tag:" + tag + Constant.HTML_RETURN_NEWLINE +
                    "MSG消息:" + msg + Constant.HTML_RETURN_NEWLINE +
                    "失败原因" + message + Constant.HTML_RETURN_NEWLINE;
            sendOaAlertMsg(trackingId, title, content);
        } catch (Exception e) {
            log.error("告警消息发送失败：{}", trackingId, e);
        }
    }


    /**
     * 告警接收人
     *
     * @return
     */
    private List<String> alterMsgReceive() {
        try {
            String value = ConfigCenter.get(SystemConstant.SEND_MSG_TO_WX);
            if (ObjectUtils.isEmpty(value)) {
                log.warn("job numbers is empty!!");
                return Collections.emptyList();
            }
            return Arrays.asList(value.split(Constant.STRING_SEMICOLON));
        } catch (Exception e) {
            log.warn("从配置中心获取报警接收人失败。", e);
        }
        return Collections.emptyList();
    }

}
