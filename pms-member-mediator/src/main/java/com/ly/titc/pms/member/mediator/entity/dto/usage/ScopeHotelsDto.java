package com.ly.titc.pms.member.mediator.entity.dto.usage;

import com.ly.titc.common.entity.CodeObject;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-29 10:01
 */
@Data
public class ScopeHotelsDto {

    /**
     * 适用门店列表
     */
    private List<String> hotelCodes;

    /**
     * 所有酒管标签
     */
    private List<CodeObject> clubLabels;

    /**
     * 所有品牌标签
     */
    private List<CodeObject> brandLabels;

    /**
     * 所有区域标签
     */
    private List<CodeObject> areaLabels;

    /**
     * 所有地域(省市区)标签
     */
    private List<CodeObject> regionLabels;
}
