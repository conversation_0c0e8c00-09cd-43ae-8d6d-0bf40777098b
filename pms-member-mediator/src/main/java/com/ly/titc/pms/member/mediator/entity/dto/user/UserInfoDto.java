package com.ly.titc.pms.member.mediator.entity.dto.user;


import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Description:
 * @Author: lixu
 * @Date: 2022/11/27
 */
@Data
@Accessors(chain = true)
public class UserInfoDto {
    private String blocCode;

    private Long userId;

    /**
     * 是否是超管
     */
    private Boolean isAdmin;

    /**
     * 门店codes
     */
    private List<String> hotelCodes;



    //------sso 2.0-----//

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 微信openId
     */
    private String weChatId;

    /**
     * 微信昵称
     */
    private String weChatNickName;

    private String jobNo;

    /**
     * 租户Code
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户类型 类型 1-内部 2-外部 3-酒管组
     */
    private int tenantType;

    /**
     * 集团名称
     */
    private String blocName;


    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户邮箱地址
     */
    private String email;

    /**
     * 微信头像
     */
    private String headImgUrl;

}
