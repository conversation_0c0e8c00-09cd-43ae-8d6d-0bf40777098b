package com.ly.titc.pms.member.mediator.rpc.dubbo.cashier;

import com.alibaba.fastjson2.JSON;
import com.ly.titc.cashier.dubbo.entity.request.*;
import com.ly.titc.cashier.dubbo.entity.request.onlinePay.CashierPayReq;
import com.ly.titc.cashier.dubbo.entity.response.*;
import com.ly.titc.cashier.dubbo.entity.response.onlinePay.CashierPayResp;
import com.ly.titc.cashier.dubbo.interfaces.pay.CashierPayDubboService;
import com.ly.titc.common.entity.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-8-21 13:54
 */
@Slf4j
@Service
public class CashierPayDecorator {

    @DubboReference(group = "${cashier-dsf-dubbo-group}",timeout = 30000)
    private CashierPayDubboService cashierPayDubboService;

    /**
     * 获取支付终端
     */
    public List<CashierTermListResp> termList(CashierTermListReq req) {
        return Response.getValidateData(cashierPayDubboService.termList(req));
    }


    /**
     * 线上支付
     */
    public CashierPayResp onlinePay(CashierPayReq req) {
        log.info("调用收银台线上支付请求参数：{}", JSON.toJSONString(req));
        Response<CashierPayResp> response = cashierPayDubboService.cashierPay(req);
        log.info("调用收银台线上支付响应参数：{}",JSON.toJSONString(response));
      return   Response.getValidateData(response);
    }

    /**
     * 查询支付订单详情
     * @param req
     * @return
     */
    public CashierPayDetailInfoResp getPayInfo(CashierPayInfoGetReq req) {
        log.info("调用收银台查询支付订单详情请求参数：{}", JSON.toJSONString(req));
        Response<CashierPayDetailInfoResp> response = cashierPayDubboService.getPayInfo(req);
        log.info("调用收银台查询支付订单详情响应参数：{}",JSON.toJSONString(response));
        return Response.getValidateData(response);
    }

    /**
     * 退款
     */
    public CashierRefundResp refund(CashierRefundApplyReq req){
        log.info("调用收银台退款请求参数：{}", JSON.toJSONString(req));
        Response<CashierRefundResp> response = cashierPayDubboService.refund(req);
        log.info("调用收银台退款响应参数：{}",JSON.toJSONString(response));
        return Response.getValidateData(response);
    }

    /**
     * 获取退款信息
     */
    public CashierRefundDetailResp getRefundInfo(CashierRefundInfoGetReq req){
        log.info("调用收银查询台退款请求参数：{}", JSON.toJSONString(req));
       Response<CashierRefundDetailResp>  response = cashierPayDubboService.getRefundInfo(req);
        log.info("调用收银查询台退款响应参数：{}",JSON.toJSONString(response));
        return Response.getValidateData(response);
    }


    /**
     * 退款补偿获取静态二维码
     */
    public CashierStaticQrCodeResp getStaticQrCode(CashierStaticQrCodeReq req) {
        return Response.getValidateData(cashierPayDubboService.getStaticQrCode(req));
    }



}
