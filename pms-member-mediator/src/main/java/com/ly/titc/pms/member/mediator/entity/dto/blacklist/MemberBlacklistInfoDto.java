package com.ly.titc.pms.member.mediator.entity.dto.blacklist;

import lombok.Data;
import java.util.List;

/**
 * @Author：rui
 * @name：BlacklistInfoDto
 * @Date：2024-12-10 15:55
 * @Filename：BlacklistInfoDto
 */
@Data
public class MemberBlacklistInfoDto {

    /**
     * 黑名单编号
     */
    private String blacklistNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 场景适用范围 0 部分 1 全部
     */
    private Integer sceneScope;

    /**
     * 原因
     */
    private String reason;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 适用列表
     */
    private List<MemberBlacklistApplicableDataMappingDto> mappingList;

}
