package com.ly.titc.pms.member.mediator.entity.dto.member;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import com.ly.titc.springboot.elasticsearch.entity.DocId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员详细信息DTO
 *
 * <AUTHOR>
 * @classname MemberDto
 * @descrition 会员信息DTO
 * @since 2023/8/8 14:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class MemberInfoDto extends DocId {

    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 英文名
     */
    private String enName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 证件号分类
     */
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 性别 1:男;2:女;3:保密
     */
    private Integer gender;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 自定义会员号
     */
    private String customizeMemberNo;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
