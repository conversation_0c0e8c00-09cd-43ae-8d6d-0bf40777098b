package com.ly.titc.pms.member.mediator.rpc.dubbo.coupon;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.config.GetBlocCouponConfigReq;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.config.SaveBlocCouponConfigReq;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.config.BlocCouponConfigResp;
import com.ly.titc.pms.spm.dubbo.interfaces.CouponConfigDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Slf4j
@Component
public class CouponConfigDecorator {
    @DubboReference(group = "${spm-dsf-dubbo-group}")
    private CouponConfigDubboService couponConfigDubboService;


    /**
     * 保存集团优惠券相关配置
     */
    public void saveBlocConfig(SaveBlocCouponConfigReq req){
        Response<String> response = couponConfigDubboService.saveBlocConfig(req);
        Response.getValidateData(response);
    }

    /**
     * 查询集团优惠券相关配置
     */
    public BlocCouponConfigResp getBlocConfig(GetBlocCouponConfigReq req){
        Response<BlocCouponConfigResp> response = couponConfigDubboService.getBlocConfig(req);
        return Response.getValidateData(response);
    }
}
