package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.pms.member.mediator.entity.dto.privilege.SavePrivilegeConfigDto;

/**
 * 权益配置
 *
 * <AUTHOR>
 * @date 2025/6/25 17:54
 */
public interface PrivilegeConfigMedService {

    /**
     * 保存权益配置
     *
     * @param dto
     * @return
     */
    Long savePrivilegeConfig(SavePrivilegeConfigDto dto);

    /**
     * 删除权益配置
     *
     * @param privilegeId
     * @param operator
     */
    void deletePrivilegeConfig(Long privilegeId, String operator);

    /**
     * 启用/禁用权益配置
     *
     * @param privilegeId
     * @param state
     * @param operator
     * @return
     */
    Long actionPrivilegeConfig(Long privilegeId, Integer state, String operator);
}
