package com.ly.titc.pms.member.mediator.rpc.dubbo.giftpack;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.spm.dubbo.entity.request.giftpack.*;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.GiftPackInfoResp;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.PageGiftPackInfoResp;
import com.ly.titc.pms.spm.dubbo.interfaces.GiftPackDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-28
 */
@Slf4j
@Component
public class GiftPackDecorator {

    @DubboReference(group = "${spm-dsf-dubbo-group}",check = false)
    private GiftPackDubboService giftPackDubboService;

    /**
     * 保存礼包信息
     */
    public String save(SaveGiftPackInfoReq req){
        Response<String> response = giftPackDubboService.save(req);
        return Response.getValidateData(response);
    }

    /**
     * 礼包列表
     */
    public Pageable<PageGiftPackInfoResp> pageInfos(PageGiftPackInfoReq req){
        Response<Pageable<PageGiftPackInfoResp>> response = giftPackDubboService.pageInfos(req);
        return Response.getValidateData(response);
    }

    /**
     * 启用/停用
     */
    public void updateState(UpdateGiftPackStateReq req){
        Response<String> response = giftPackDubboService.updateState(req);
        Response.getValidateData(response);
    }

    /**
     * 礼包详情
     */
    public GiftPackInfoResp getGiftPackInfo(GetGiftPackInfoReq req){
        Response<GiftPackInfoResp> response = giftPackDubboService.getInfo(req);
        return Response.getValidateData(response);
    }

    /**
     * 礼包删除
     */
    public void delete(DeleteGiftPackReq req){
        Response<String> response = giftPackDubboService.delete(req);
        Response.getValidateData(response);
    }
}
