package com.ly.titc.pms.member.mediator.handler.check;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.GetByCustomerNoReq;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerInfoResp;
import com.ly.titc.pms.member.com.enums.MemberRegisterCheckEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.mediator.converter.MemberMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.CustomerInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.customer.CustomerDecorator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * 客户校验
 *
 * <AUTHOR>
 * @date 2024/12/13 17:47
 */
@Component
@Slf4j
public class CustomerCheckHandler extends AbstractRegisterCheckHandler {

    @Resource
    private CustomerDecorator customerDecorator;

    @Resource
    private MemberMedConverter memberMedConverter;

    @Override
    public Integer getAction() {
        return MemberRegisterCheckEnum.CUSTOMER.getAction();
    }

    @Override
    public void check(RegisterMemberDto dto) {
        CustomerInfoDto customerInfo = dto.getCustomerInfo();
        if (customerInfo != null) {
            // 校验客户是否绑定过会员
            GetByCustomerNoReq req = memberMedConverter.convertDtoToReq(customerInfo);
            req.setTrackingId(UUID.randomUUID().toString());
            CustomerInfoResp customerInfoResp = customerDecorator.getByCustomerNo(req);
            if (customerInfoResp == null) {
                throw new ServiceException(RespCodeEnum.MEMBER_10046);
            }
            if (StringUtils.isNotBlank(customerInfoResp.getMemberNo())) {
                throw new ServiceException(RespCodeEnum.MEMBER_10047);
            }
        }

    }
}
