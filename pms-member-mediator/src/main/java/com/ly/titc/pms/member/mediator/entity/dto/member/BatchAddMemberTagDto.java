package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;

import java.util.List;

/**
 * 批量保存会员标签
 *
 * <AUTHOR>
 * @date 2024/11/25 15:47
 */
@Data
public class BatchAddMemberTagDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    private String masterCode;

    /**
     * 会员编号
     */
    private List<String> memberNos;

    /**
     * 标签ID
     */
    private List<Long> tagIds;

    /**
     * 打标分类: 1:手动标记 2:系统标记
     */
    private Integer markType;

    /**
     * 操作人
     */
    private String operator;
}
