package com.ly.titc.pms.member.mediator.entity.dto.order;

import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 11:57
 */
@Data
public class RefundOrderDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    private String masterCode;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员订单号
     */
    private String memberOrderNo;

    /**
     * 退款金额（到分）
     */
    private BigDecimal refundAmount;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 退款原因
     */
    private String reason;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 终端ID
     */
    private String termId;

}
