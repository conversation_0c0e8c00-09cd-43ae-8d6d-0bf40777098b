package com.ly.titc.pms.member.mediator.entity.dto.blacklist;

import lombok.Data;

import java.util.List;

/**
 * 拉黑会员
 *
 * <AUTHOR>
 * @date 2024/12/19 11:35
 */
@Data
public class BlacklistMemberDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 场景适用范围 0 部分 1 全部
     */
    private Integer sceneScope;

    /**
     * 适用场景列表
     */
    private List<String> scenes;

    /**
     * 渠道适用范围 0 部分 1 全部
     */
    private Integer platformChannelScope;

    /**
     * 适用渠道列表
     */
    private List<String> platformChannels;

    /**
     * 原因
     */
    private String reason;

    /**
     * 来源类型
     */
    private Integer sourceType;

    /**
     * 来源
     */
    private String source;

    /**
     * 操作人
     */
    private String operator;


}
