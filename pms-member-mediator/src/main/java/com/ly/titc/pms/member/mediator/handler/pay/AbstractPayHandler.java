package com.ly.titc.pms.member.mediator.handler.pay;

import com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.converter.MemberOrderMedConverter;
import com.ly.titc.pms.member.mediator.converter.MemberRefundOrderMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.order.*;
import com.ly.titc.pms.member.mediator.manager.OrderPayManager;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cashier.CashierSettlePaymentTradeDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cashier.CashierPayDecorator;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-28 15:40
 */
public abstract class AbstractPayHandler {

    @Resource
    protected CashierPayDecorator cashierPayDecorator;

    @Resource
    protected MemberOrderMedConverter orderMedConverter;

    @Resource
    protected CashierSettlePaymentTradeDecorator paymentTradeDecorator;

    @Resource
    protected MemberRefundOrderMedConverter refundOrderMedConverter;

    public AbstractPayHandler() {
        OrderPayManager.putInstance(getRoute(), this);
    }

    /**
     * 查询支付结果
     */
    public abstract PayOrderResultDto getPayState(GetPayStateDto dto);


    /**
     * 退款
     */
    public abstract OrderRefundResultDto refund(MemberOrderRefundInfo refundInfo,MemberOrderPayInfo memberOrderPayInfo);

    /**
     * 查询退款结果
     */
    public abstract OrderRefundResultDto getRefundState(MemberOrderRefundInfo memberOrderRefundInfo,String operator);

    /**
     * 获取支付路由
     */
    public abstract String getRoute();
}
