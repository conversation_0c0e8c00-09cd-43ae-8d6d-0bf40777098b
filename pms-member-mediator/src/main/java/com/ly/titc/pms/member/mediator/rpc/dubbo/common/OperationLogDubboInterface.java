package com.ly.titc.pms.member.mediator.rpc.dubbo.common;

import com.ly.titc.cc.dubbo.entity.request.log.PageRecordsReq;
import com.ly.titc.cc.dubbo.entity.request.log.RecordReq;
import com.ly.titc.cc.dubbo.entity.response.log.RecordResp;
import com.ly.titc.cc.dubbo.interfaces.OperationLogDubboService;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Author：rui
 * @name：OperationLogDubboInterface
 * @Date：2023-11-27 10:45
 * @Filename：OperationLogDubboInterface
 */
@Slf4j
@Component("logInterface")
public class OperationLogDubboInterface {

    @DubboReference
    public OperationLogDubboService operationLogDubboService;

    /**
     * pageLog
     *
     * @param request
     * @return
     */
    public Pageable<RecordResp> pageLog(PageRecordsReq request) {
        Response<Pageable<RecordResp>> pageableResponse = operationLogDubboService.pageRecordByCondition(request);
        return Response.getValidateData(pageableResponse);
    }

    /**
     * record
     *
     * @param request
     * @return
     */
    public String record(RecordReq request) {

        Response<String> response = operationLogDubboService.record(request);
        return Response.getValidateData(response);
    }


}
