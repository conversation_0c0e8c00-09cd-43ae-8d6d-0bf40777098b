package com.ly.titc.pms.member.mediator.entity.dto.member;


import lombok.Data;
import lombok.experimental.Accessors;

import java.util.UUID;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-2-20 20:38
 */
@Data
@Accessors(chain = true)
public class GetUsableMemberDto {

    /**
     * 集团编码（必传）
     */
    private String blocCode;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    private String masterCode;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 如果传入卡号则查指定卡的，如果没有传入则查默认卡的
     */
    private String memberCardNo;

    /**
     * 会员资产权益归属类型 （注：这里只支持酒店，因为权益现在只能配给酒店）
     */
    private Integer scopeMasterType;

    /**
     * 只能是酒店code ，优惠券只能查酒店的
     * 会员资产和权益的归属值  （注：这里只支持酒店，因为权益现在只能配给酒店）
     */
    private String scopeMasterCode;

    /**
     * 平台渠道 CMS ，PMS ，微订房小程序，微订房公众号，艺龙会小程序
     */
    private String platformChannel;

    private String trackingId = UUID.randomUUID().toString();
}
