package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：rui
 * @name：PageUpgradeRuleReq
 * @Date：2024-11-7 20:21
 * @Filename：PageUpgradeRuleReq
 */
@Data
@Accessors(chain = true)
public class PageCardLevelUpgradeRuleDto {

    private Integer masterType;

    private String masterCode;

    private String name;

    private Integer sourceLevel;

    private Integer state;

    /**
     * 页码
     */
    private Integer pageIndex = 1;

    /**
     * pageSize
     */
    private Integer pageSize = 20;
}
