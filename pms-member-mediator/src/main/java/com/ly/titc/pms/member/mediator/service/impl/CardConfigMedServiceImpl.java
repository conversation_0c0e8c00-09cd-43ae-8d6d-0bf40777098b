package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.pms.ecrm.dubbo.enums.PrivilegeTypeEnum;
import com.ly.titc.pms.member.biz.*;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.PrivilegeClassificationEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.com.utils.PageableUtil;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.entity.bo.ListCardLevelConfigBo;
import com.ly.titc.pms.member.entity.bo.PageCardLevelConfigBo;
import com.ly.titc.pms.member.entity.bo.PageCardLevelRelegationRuleBo;
import com.ly.titc.pms.member.entity.bo.PageCardLevelUpgradeRuleBo;
import com.ly.titc.pms.member.mediator.converter.CardConfigMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.*;
import com.ly.titc.pms.member.mediator.service.CardConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.titc.pms.member.service.CardConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员卡配置实现
 *
 * <AUTHOR>
 * @date 2025/6/25 14:11
 */
@Slf4j
@Service
public class CardConfigMedServiceImpl implements CardConfigMedService {

    @Resource
    private CardConfigBiz cardConfigBiz;

    @Resource
    private CardNoRuleBiz cardNoRuleBiz;

    @Resource
    private CardLevelConfigBiz cardLevelConfigBiz;

    @Resource
    private CardLevelUpgradeRuleBiz cardLevelUpgradeRuleBiz;

    @Resource
    private CardLevelRelegationRuleBiz cardLevelRelegationRuleBiz;

    @Resource
    private CardLevelPrivilegeConfigBiz cardLevelPrivilegeConfigBiz;

    @Resource
    private PrivilegeConfigBiz privilegeConfigBiz;

    @Resource
    private CardConfigMedConverter cardConfigMedConverter;

    @Resource
    private CardConfigService cardConfigService;

    @Resource
    private RedisFactory redisFactory;

    @Resource
    private MemberCardMedService memberCardMedService;

    @Override
    public Long saveCardConfig(SaveCardConfigDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.CARD_CONFIG_SAVE_IDEMPOTENT_PREFIX, dto.getMasterType(), dto.getMasterCode(), dto.getCardName());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this card config save is processing...masterType:{};masterCode:{},cardName:{}", dto.getMasterType(), dto.getMasterCode(), dto.getCardName());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            dto.setId(dto.getId() == null ? IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId() : dto.getId());
            dto.setCardCode(StringUtils.isBlank(dto.getCardCode()) ? CommonUtil.generateUniqueNo() : dto.getCardCode());
            CardConfigInfo cardConfigInfo = cardConfigMedConverter.convertDtoToPo(dto);
            CardNoRuleInfo cardNoRuleInfo = cardConfigMedConverter.convertDtoToPo(dto.getCardNoRule(), dto.getId(), dto.getOperator());
            List<CardApplicableDataMapping> mappings = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(dto.getApplicableDataMappings())) {
                mappings = dto.getApplicableDataMappings().stream().map(mapping -> cardConfigMedConverter.convertDtoToPo(mapping, dto.getId(), dto.getOperator())).collect(Collectors.toList());
            }
            cardConfigService.saveCardConfig(cardConfigInfo, mappings, cardNoRuleInfo);
            return dto.getId();
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public CardConfigDto getDefaultCard(Integer masterType, String masterCode) {
        CardConfigInfo defaultCard = cardConfigBiz.getDefaultCard(masterType, masterCode);
        if (defaultCard == null) {
            return null;
        }
        CardConfigDto cardConfigDto = cardConfigMedConverter.convertPoToDto(defaultCard);
        List<CardApplicableDataMapping> cardApplicableDataMappings = cardConfigBiz.lisApplicableDataMapping(defaultCard.getId());
        cardConfigDto.setScopeValues(cardApplicableDataMappings.stream().map(CardApplicableDataMapping::getScopeValue).collect(Collectors.toList()));
        return cardConfigDto;
    }

    @Override
    public List<CardConfigDto> listCardConfig(List<Long> cardIds) {
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByIds(cardIds);
        if (CollectionUtils.isEmpty(cardConfigInfos)) {
            return Lists.newArrayList();
        }
        List<CardLevelConfigInfo> cardLevelConfigs = cardLevelConfigBiz.listByCardIds(cardIds);
        Map<Long, List<CardLevelConfigInfo>> cardLevelMap = cardLevelConfigs.stream().collect(Collectors.groupingBy(CardLevelConfigInfo::getCardId));

        List<CardApplicableDataMapping> cardApplicableDataMappings = cardConfigBiz.lisApplicableDataMapping(cardIds);
        Map<Long, List<CardApplicableDataMapping>> cardApplicableDataMappingMap = cardApplicableDataMappings.stream().collect(Collectors.groupingBy(CardApplicableDataMapping::getCardId));

        return cardConfigInfos.stream().map(cardConfig -> {
            CardConfigDto cardConfigDto = cardConfigMedConverter.convertPoToDto(cardConfig);
            List<CardLevelConfigInfo> cardLevelConfigInfos = cardLevelMap.getOrDefault(cardConfigDto.getId(), Lists.newArrayList());
            cardConfigDto.setCardLevelConfigs(cardLevelConfigInfos.stream().sorted(Comparator.comparingInt(CardLevelConfigInfo::getCardLevel)).map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
            List<CardApplicableDataMapping> applicableDataMappings = cardApplicableDataMappingMap.getOrDefault(cardConfigDto.getId(), Lists.newArrayList());
            cardConfigDto.setScopeValues(applicableDataMappings.stream().map(CardApplicableDataMapping::getScopeValue).collect(Collectors.toList()));
            return cardConfigDto;
        }).collect(Collectors.toList());
    }

    @Override
    public Long saveCardLevelUpgradeRule(SaveCardLevelUpgradeRuleDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.CARD_LEVEL_CONFIG_SAVE_IDEMPOTENT_PREFIX, dto.getCardId());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this card level upgrade config save is processing...cardId:{}", dto.getCardId());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            List<CardLevelUpgradeRuleInfo> cardLevelUpgradeRuleInfos = cardLevelUpgradeRuleBiz.listByCardId(dto.getCardId());
            // 当前会员卡等级升级规则，无法重复配置
            if (CollectionUtils.isNotEmpty(cardLevelUpgradeRuleInfos)) {
                boolean flag = cardLevelUpgradeRuleInfos.stream().filter(item -> item.getState().equals(StatusEnum.VALID.getStatus()))
                        .anyMatch(item -> item.getTargetLevel().equals(dto.getTargetLevel()) && item.getSourceLevel().equals(dto.getSourceLevel()));
                if (flag) {
                    throw new ServiceException(RespCodeEnum.MEMBER_10024);
                }
            }
            dto.setId(dto.getId() == null ? IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId() : dto.getId());
            CardLevelUpgradeRuleInfo cardLevelUpgradeRuleInfo = cardConfigMedConverter.convertDtoToPo(dto);
            List<CardLevelUpgradeRuleDetailInfo> upgradeRuleDetails = dto.getRuleDetails().stream().map(detail -> cardConfigMedConverter.convertDtoToPo(detail, dto.getCardId(), dto.getId(), dto.getOperator())).collect(Collectors.toList());
            cardConfigService.saveCardLevelUpgradeRule(cardLevelUpgradeRuleInfo, upgradeRuleDetails);
            return dto.getId();
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public Long saveCardLevelRelegationRule(SaveCardLevelRelegationRuleDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.CARD_LEVEL_CONFIG_SAVE_IDEMPOTENT_PREFIX, dto.getCardId());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this card level relegation config save is processing...cardId:{}", dto.getCardId());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            List<CardLevelRelegationRuleInfo> cardLevelUpgradeRuleInfos = cardLevelRelegationRuleBiz.listByCardId(dto.getCardId());
            // 当前会员卡等级保级规则，无法重复配置
            if (CollectionUtils.isNotEmpty(cardLevelUpgradeRuleInfos)) {
                boolean flag = cardLevelUpgradeRuleInfos.stream().filter(item -> item.getState().equals(StatusEnum.VALID.getStatus()))
                        .anyMatch(item -> item.getTargetLevel().equals(dto.getTargetLevel()) && item.getSourceLevel().equals(dto.getSourceLevel()));
                if (flag) {
                    throw new ServiceException(RespCodeEnum.MEMBER_10025);
                }
            }
            dto.setId(dto.getId() == null ? IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId() : dto.getId());
            CardLevelRelegationRuleInfo cardLevelUpgradeRuleInfo = cardConfigMedConverter.convertDtoToPo(dto);
            List<CardLevelRelegationRuleDetailInfo> upgradeRuleDetails = dto.getRuleDetails().stream().map(detail -> cardConfigMedConverter.convertDtoToPo(detail, dto.getCardId(), dto.getId(), dto.getOperator())).collect(Collectors.toList());
            cardConfigService.saveCardLevelRelegationRule(cardLevelUpgradeRuleInfo, upgradeRuleDetails);
            return dto.getId();
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public Long saveCardLevelConfig(SaveCardLevelConfigDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.CARD_LEVEL_CONFIG_SAVE_IDEMPOTENT_PREFIX, dto.getCardId(), dto.getCardLevel());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this card level config save is processing...cardId:{}", dto.getCardId());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            // 查询卡关联等级列表
            List<CardLevelConfigInfo> cardLevelConfigInfoRespList = cardLevelConfigBiz.listByCardIds(Collections.singletonList(dto.getCardId()));
            if (cardLevelConfigInfoRespList.stream().filter(item -> !item.getId().equals(dto.getId()))
                    .anyMatch(item -> item.getCardLevel().equals(dto.getCardLevel()))) {
                throw new ServiceException(RespCodeEnum.MEMBER_10023);
            }
            // 权益校验
            checkPrivilege(dto);
            // 填充数据
            dto.setId(dto.getId() == null ? IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId() : dto.getId());
            List<Long> privilegeIds = dto.getPrivilegeConfigs().stream().map(SaveCardLevelPrivilegeConfigDto::getPrivilegeId).collect(Collectors.toList());
            List<PrivilegeApplicableDataMapping> mappingDtoList = privilegeConfigBiz.listMappingByIds(privilegeIds);
            PrivilegeApplicableDataMapping discountMappingDto = mappingDtoList.stream().filter(item -> item.getClassification().equals(PrivilegeClassificationEnum.DISCOUNT.getType())).findFirst().orElse(null);
            if (Objects.nonNull(discountMappingDto)) {
                dto.setCardDiscount(new BigDecimal(discountMappingDto.getValue()).multiply(new BigDecimal(10)).intValue());
            } else {
                throw new ServiceException(RespCodeEnum.CONFIG_20000);
            }
            // 组装数据
            CardLevelConfigInfo cardLevelConfigInfo = cardConfigMedConverter.convertDtoToPo(dto);
            List<CardLevelPrivilegeConfigInfo> mappings = dto.getPrivilegeConfigs().stream()
                    .map(config -> cardConfigMedConverter.convertDtoToPo(config, dto.getCardId(), dto.getCardLevel(), dto.getOperator())).collect(Collectors.toList());
            cardConfigService.saveCardLevelConfig(cardLevelConfigInfo, mappings);
            return dto.getId();
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public void deleteCardConfig(Long cardId, String operator) {
        CardConfigInfo cardConfigInfo = cardConfigBiz.getByCardId(cardId);
        if (cardConfigInfo == null) {
            return;
        }
        // 先校验有没有会员
        if (memberCardMedService.checkHasCard(cardConfigInfo.getMasterType(), cardConfigInfo.getMasterCode(), cardId, null)) {
            throw new ServiceException(RespCodeEnum.MEMBER_10020);
        }
        cardConfigService.deleteCard(cardId, operator);
    }

    @Override
    public void deleteCardLevelConfig(Long cardId, Integer cardLevel, String operator) {
        CardConfigInfo cardConfigInfo = cardConfigBiz.getByCardId(cardId);
        if (cardConfigInfo == null) {
            return;
        }
        // 先校验有没有会员
        if (memberCardMedService.checkHasCard(cardConfigInfo.getMasterType(), cardConfigInfo.getMasterCode(), cardId, cardLevel)) {
            throw new ServiceException(RespCodeEnum.MEMBER_10020);
        }
        cardConfigService.deleteCardLevel(cardId, cardLevel, operator);
    }

    @Override
    public Pageable<CardLevelConfigDto> pageCardLevelConfig(PageCardLevelConfigDto dto) {
        PageCardLevelConfigBo pageBo = cardConfigMedConverter.convertDtoToBo(dto);
        IPage<CardLevelConfigInfo> page = cardLevelConfigBiz.pageCardLevelConfig(pageBo);
        List<CardLevelConfigInfo> dtoList = page.getRecords();
        if (CollectionUtils.isEmpty(dtoList)) {
            return Pageable.empty();
        }
        List<Long> cardIds = dtoList.stream().map(CardLevelConfigInfo::getCardId).collect(Collectors.toList());
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByIds(cardIds);
        Map<Long, CardConfigInfo> cardConfigInfoDtoMap = cardConfigInfos.stream().collect(Collectors.toMap(CardConfigInfo::getId, Function.identity()));

        return PageableUtil.convert(page, level -> {
            CardLevelConfigDto configDto = cardConfigMedConverter.convertPoToDto(level);
            CardConfigInfo cardConfigInfoDto = cardConfigInfoDtoMap.get(level.getCardId());
            if (cardConfigInfoDto != null) {
                configDto.setCardName(cardConfigInfoDto.getCardName());
            }
            return configDto;
        });
    }

    @Override
    public CardLevelConfigDto getCardLevelConfig(Long levelId) {
        CardLevelConfigInfo cardLevelConfigInfo = cardLevelConfigBiz.getById(levelId);
        return cardConfigMedConverter.convertPoToDto(cardLevelConfigInfo);
    }

    @Override
    public List<CardLevelConfigDto> listMemberCardLevel(List<Long> cardIds) {
        List<CardLevelConfigInfo> cardLevelConfigs = cardLevelConfigBiz.listByCardIds(cardIds);
        return cardLevelConfigs.stream()
                .filter(item -> item.getState().equals(StatusEnum.VALID.getStatus()))
                .sorted(Comparator.comparingInt(CardLevelConfigInfo::getCardLevel))
                .map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList());
    }

    @Override
    public List<CardLevelConfigDto> listCardLevelConfig(ListCardLevelConfigDto dto) {
        ListCardLevelConfigBo listBo = cardConfigMedConverter.convertDtoToBo(dto);
        List<CardLevelConfigInfo> cardLevelConfigInfos = cardLevelConfigBiz.listCardLevelConfig(listBo);
        return cardLevelConfigInfos.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList());
    }

    @Override
    public void actionCardLevel(Long cardId, Integer cardLevel, Integer state, String operator) {
        CardLevelConfigInfo cardLevelConfigInfo = cardLevelConfigBiz.getByCardLevel(cardId, cardLevel);
        if (cardLevelConfigInfo == null) {
            throw new ServiceException(RespCodeEnum.CONFIG_20003);
        }
        cardLevelConfigInfo.setState(state).setModifyUser(operator);
        cardLevelConfigBiz.update(cardLevelConfigInfo);
    }

    @Override
    public List<CardConfigDto> listCardConfig(Integer masterType, String masterCode, Long cardId) {
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByMaster(masterType, masterCode, cardId);
        if (CollectionUtils.isEmpty(cardConfigInfos)) {
            return Lists.newArrayList();
        }
        List<Long> cardIds = cardConfigInfos.stream().map(CardConfigInfo::getId).collect(Collectors.toList());
        // 适用门店
        List<CardApplicableDataMapping> cardApplicableDataMappings = cardConfigBiz.lisApplicableDataMapping(cardIds);
        Map<Long, List<CardApplicableDataMapping>> cardApplicableDataMappingMap = cardApplicableDataMappings.stream().collect(Collectors.groupingBy(CardApplicableDataMapping::getCardId));
        // 卡生成规则
        List<CardNoRuleInfo> memberCardNoRuleInfos = cardNoRuleBiz.listByCardIds(cardIds);
        Map<Long, CardNoRuleInfo> memberCardNoRuleMap = memberCardNoRuleInfos.stream().collect(Collectors.toMap(CardNoRuleInfo::getCardId, Function.identity()));
        // 卡等级
        List<CardLevelConfigInfo> cardLevelConfigs = cardLevelConfigBiz.listByCardIds(cardIds);
        Map<Long, List<CardLevelConfigInfo>> cardLevelMap = cardLevelConfigs.stream().collect(Collectors.groupingBy(CardLevelConfigInfo::getCardId));
        return cardConfigInfos.stream().map(cardConfigInfo -> {
            CardConfigDto cardConfigDto = cardConfigMedConverter.convertPoToDto(cardConfigInfo);
            List<CardApplicableDataMapping> applicableDataMappings = cardApplicableDataMappingMap.getOrDefault(cardConfigDto.getId(), Lists.newArrayList());
            cardConfigDto.setScopeValues(applicableDataMappings.stream().map(CardApplicableDataMapping::getScopeValue).collect(Collectors.toList()));
            CardNoRuleInfo cardNoRuleInfo = memberCardNoRuleMap.get(cardConfigDto.getId());
            cardConfigDto.setCardNoRule(cardConfigMedConverter.convertPoToDto(cardNoRuleInfo));
            List<CardLevelConfigInfo> cardLevels = cardLevelMap.getOrDefault(cardConfigDto.getId(), Lists.newArrayList());
            cardConfigDto.setCardLevelConfigs(cardLevels.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
            return cardConfigDto;
        }).collect(Collectors.toList());
    }

    @Override
    public CardConfigDto getCardConfig(Long cardId) {
        CardConfigInfo cardConfigInfo = cardConfigBiz.getByCardId(cardId);
        if (cardConfigInfo == null) {
            return null;
        }
        // 适用门店
        List<CardApplicableDataMapping> cardApplicableDataMappings = cardConfigBiz.lisApplicableDataMapping(cardConfigInfo.getId());
        CardConfigDto cardConfigDto = cardConfigMedConverter.convertPoToDto(cardConfigInfo);
        cardConfigDto.setScopeValues(cardApplicableDataMappings.stream().map(CardApplicableDataMapping::getScopeValue).collect(Collectors.toList()));
        // 卡生成规则
        CardNoRuleInfo cardNoRuleInfo = cardNoRuleBiz.getByCardId(cardConfigInfo.getId());
        cardConfigDto.setCardNoRule(cardConfigMedConverter.convertPoToDto(cardNoRuleInfo));
        // 卡等级
        List<CardLevelConfigInfo> cardLevelConfigs = cardLevelConfigBiz.listByCardId(cardConfigInfo.getId());
        cardConfigDto.setCardLevelConfigs(cardLevelConfigs.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
        return cardConfigDto;
    }

    @Override
    public CardLevelUpgradeRuleDto getCardLevelUpgradeRule(GetCardLevelUpgradeRuleDto dto) {
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByMaster(dto.getMasterType(), dto.getMasterCode(), dto.getCardId());
        if (Objects.isNull(cardConfigInfos)) {
            return null;
        }
        CardLevelUpgradeRuleInfo cardLevelUpgradeRuleInfo = cardLevelUpgradeRuleBiz.getByCardLevel(dto.getCardId(), dto.getMemberCardLevel());
        if (Objects.isNull(cardLevelUpgradeRuleInfo)) {
            return null;
        }
        List<CardLevelUpgradeRuleDetailInfo> details = cardLevelUpgradeRuleBiz.listDetailByRuleId(cardLevelUpgradeRuleInfo.getId());
        CardLevelUpgradeRuleDto ruleDto = cardConfigMedConverter.convertPoToDto(cardLevelUpgradeRuleInfo);
        ruleDto.setDetails(details.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
        return ruleDto;
    }

    @Override
    public List<CardLevelUpgradeRuleDto> listCardLevelUpgradeRule(List<Long> cardIds, Integer state) {
        List<CardLevelUpgradeRuleInfo> cardLevelUpgradeRuleInfos = cardLevelUpgradeRuleBiz.listByCardIds(cardIds, state);
        if (CollectionUtils.isEmpty(cardLevelUpgradeRuleInfos)) {
            return Lists.newArrayList();
        }
        List<Long> ruleIds = cardLevelUpgradeRuleInfos.stream().map(CardLevelUpgradeRuleInfo::getId).collect(Collectors.toList());
        List<CardLevelUpgradeRuleDetailInfo> details = cardLevelUpgradeRuleBiz.listDetailByRuleIds(ruleIds);
        Map<Long, List<CardLevelUpgradeRuleDetailInfo>> cardLevelUpgradeRuleMap = details.stream().collect(Collectors.groupingBy(CardLevelUpgradeRuleDetailInfo::getUpgradeRuleId));
        return cardLevelUpgradeRuleInfos.stream().map(rule -> {
            CardLevelUpgradeRuleDto ruleDto = cardConfigMedConverter.convertPoToDto(rule);
            List<CardLevelUpgradeRuleDetailInfo> ruleDetailInfos = cardLevelUpgradeRuleMap.getOrDefault(rule.getId(), Lists.newArrayList());
            ruleDto.setDetails(ruleDetailInfos.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
            return ruleDto;
        }).collect(Collectors.toList());
    }

    @Override
    public Pageable<CardLevelUpgradeRuleDto> pageUpgradeRule(PageCardLevelUpgradeRuleDto dto) {
        PageCardLevelUpgradeRuleBo pageBo = cardConfigMedConverter.convertDtoToBo(dto);
        IPage<CardLevelUpgradeRuleInfo> page = cardLevelUpgradeRuleBiz.pageUpgradeRule(pageBo);
        List<CardLevelUpgradeRuleInfo> ruleInfos = page.getRecords();
        if (CollectionUtils.isEmpty(ruleInfos)) {
            return Pageable.empty();
        }
        List<Long> cardIds = ruleInfos.stream().map(CardLevelUpgradeRuleInfo::getCardId).distinct().collect(Collectors.toList());
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByIds(cardIds);
        List<CardLevelConfigInfo> cardLevelConfigInfos = cardLevelConfigBiz.listByCardIds(cardIds);
        Map<String, String> cardLevelMap = cardLevelConfigInfos.stream().collect(Collectors.toMap(e -> String.format("%s_%s", e.getCardId(), e.getCardLevel()), CardLevelConfigInfo::getCardLevelName));
        List<CardLevelUpgradeRuleDto> respList = cardConfigMedConverter.convertUpgradeRuleResp(ruleInfos, cardConfigInfos, cardLevelMap);
        return PageableUtil.convert(page, respList);
    }

    @Override
    public CardLevelUpgradeRuleDto getUpgradeRule(Long id) {
        CardLevelUpgradeRuleInfo upgradeRuleInfo = cardLevelUpgradeRuleBiz.getById(id);
        if (Objects.isNull(upgradeRuleInfo)) {
            log.error("upgrade rule is not exist , id :{}", id);
            return null;
        }
        List<CardLevelUpgradeRuleDetailInfo> details = cardLevelUpgradeRuleBiz.listDetailByRuleId(upgradeRuleInfo.getId());
        CardLevelUpgradeRuleDto ruleDto = cardConfigMedConverter.convertPoToDto(upgradeRuleInfo);
        ruleDto.setDetails(details.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
        return ruleDto;
    }

    @Override
    public CardLevelRelegationRuleDto getCardLevelRelegationRule(GetCardLevelRelegationRuleDto dto) {
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByMaster(dto.getMasterType(), dto.getMasterCode(), dto.getCardId());
        if (Objects.isNull(cardConfigInfos)) {
            return null;
        }
        CardLevelRelegationRuleInfo cardLevelRelegationRuleInfo = cardLevelRelegationRuleBiz.getByCardLevel(dto.getCardId(), dto.getMemberCardLevel());
        if (Objects.isNull(cardLevelRelegationRuleInfo)) {
            return null;
        }
        List<CardLevelRelegationRuleDetailInfo> details = cardLevelRelegationRuleBiz.listDetailByRuleId(cardLevelRelegationRuleInfo.getId());
        CardLevelRelegationRuleDto ruleDto = cardConfigMedConverter.convertPoToDto(cardLevelRelegationRuleInfo);
        ruleDto.setDetails(details.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
        return ruleDto;
    }

    @Override
    public List<CardLevelRelegationRuleDto> listCardLevelRelegationRule(List<Long> cardIds, Integer state) {
        List<CardLevelRelegationRuleInfo> cardLevelRelegationRuleInfos = cardLevelRelegationRuleBiz.listByCardIds(cardIds, state);
        if (CollectionUtils.isEmpty(cardLevelRelegationRuleInfos)) {
            return Lists.newArrayList();
        }
        List<Long> ruleIds = cardLevelRelegationRuleInfos.stream().map(CardLevelRelegationRuleInfo::getId).collect(Collectors.toList());
        List<CardLevelRelegationRuleDetailInfo> details = cardLevelRelegationRuleBiz.listDetailByRuleIds(ruleIds);
        Map<Long, List<CardLevelRelegationRuleDetailInfo>> cardLevelRelegationRuleMap = details.stream().collect(Collectors.groupingBy(CardLevelRelegationRuleDetailInfo::getRelegationRuleId));
        return cardLevelRelegationRuleInfos.stream().map(rule -> {
            CardLevelRelegationRuleDto ruleDto = cardConfigMedConverter.convertPoToDto(rule);
            List<CardLevelRelegationRuleDetailInfo> ruleDetailInfos = cardLevelRelegationRuleMap.getOrDefault(rule.getId(), Lists.newArrayList());
            ruleDto.setDetails(ruleDetailInfos.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
            return ruleDto;
        }).collect(Collectors.toList());
    }

    @Override
    public Pageable<CardLevelRelegationRuleDto> pageRelegationRule(PageCardLevelRelegationRuleDto dto) {
        PageCardLevelRelegationRuleBo pageBo = cardConfigMedConverter.convertDtoToBo(dto);
        IPage<CardLevelRelegationRuleInfo> page = cardLevelRelegationRuleBiz.pageRelegationRule(pageBo);
        List<CardLevelRelegationRuleInfo> ruleInfos = page.getRecords();
        if (CollectionUtils.isEmpty(ruleInfos)) {
            return Pageable.empty();
        }
        List<Long> cardIds = ruleInfos.stream().map(CardLevelRelegationRuleInfo::getCardId).distinct().collect(Collectors.toList());
        List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByIds(cardIds);
        List<CardLevelConfigInfo> cardLevelConfigInfos = cardLevelConfigBiz.listByCardIds(cardIds);
        Map<String, String> cardLevelMap = cardLevelConfigInfos.stream().collect(Collectors.toMap(e -> String.format("%s_%s", e.getCardId(), e.getCardLevel()), CardLevelConfigInfo::getCardLevelName));
        List<CardLevelRelegationRuleDto> respList = cardConfigMedConverter.convertRelegationRuleResp(ruleInfos, cardConfigInfos, cardLevelMap);
        return PageableUtil.convert(page, respList);
    }

    @Override
    public CardLevelRelegationRuleDto getRelegationRule(Long id) {
        CardLevelRelegationRuleInfo relegationRuleInfo = cardLevelRelegationRuleBiz.getById(id);
        if (Objects.isNull(relegationRuleInfo)) {
            log.error("relegation rule is not exist , id :{}", id);
            return null;
        }
        List<CardLevelRelegationRuleDetailInfo> details = cardLevelRelegationRuleBiz.listDetailByRuleId(relegationRuleInfo.getId());
        CardLevelRelegationRuleDto ruleDto = cardConfigMedConverter.convertPoToDto(relegationRuleInfo);
        ruleDto.setDetails(details.stream().map(cardConfigMedConverter::convertPoToDto).collect(Collectors.toList()));
        return ruleDto;
    }

    @Override
    public void deleteCardLevelUpgradeRule(Long ruleId, String operator) {
        cardLevelUpgradeRuleBiz.deleteByRuleId(Collections.singletonList(ruleId), operator);
    }

    @Override
    public void actionCardLevelUpgradeRule(Long ruleId, Integer state, String operator) {
        if (state.equals(StatusEnum.VALID.getStatus())) {
            CardLevelUpgradeRuleInfo upgradeRuleInfo = cardLevelUpgradeRuleBiz.getById(ruleId);
            List<CardLevelUpgradeRuleInfo> cardLevelUpgradeRuleInfos = cardLevelUpgradeRuleBiz.listByCardId(upgradeRuleInfo.getCardId());
            if (CollectionUtils.isNotEmpty(cardLevelUpgradeRuleInfos)) {
                if (cardLevelUpgradeRuleInfos.stream().filter(item -> item.getState().equals(StatusEnum.VALID.getStatus()) && !item.getId().equals(ruleId))
                        .anyMatch(item -> item.getTargetLevel().equals(upgradeRuleInfo.getTargetLevel()) && item.getSourceLevel().equals(upgradeRuleInfo.getSourceLevel()))) {
                    throw new ServiceException(RespCodeEnum.MEMBER_10024);
                }
            }
        }
        cardLevelUpgradeRuleBiz.updateState(ruleId, state, operator);
    }

    @Override
    public void deleteCardLevelRelegationRule(Long ruleId, String operator) {
        cardLevelRelegationRuleBiz.deleteByRuleId(Collections.singletonList(ruleId), operator);
    }

    @Override
    public void actionCardLevelRelegationRule(Long ruleId, Integer state, String operator) {
        if (state.equals(StatusEnum.VALID.getStatus())) {
            CardLevelRelegationRuleInfo ruleInfo = cardLevelRelegationRuleBiz.getById(ruleId);
            List<CardLevelRelegationRuleInfo> cardLevelRelegationRuleInfos = cardLevelRelegationRuleBiz.listByCardId(ruleInfo.getCardId());
            if (CollectionUtils.isNotEmpty(cardLevelRelegationRuleInfos)) {
                if (cardLevelRelegationRuleInfos.stream().filter(item -> item.getState().equals(StatusEnum.VALID.getStatus()) && !item.getId().equals(ruleId))
                        .anyMatch(item -> item.getTargetLevel().equals(ruleInfo.getTargetLevel()) && item.getSourceLevel().equals(ruleInfo.getSourceLevel()))) {
                    throw new ServiceException(RespCodeEnum.MEMBER_10025);
                }
            }
        }
        cardLevelRelegationRuleBiz.updateState(ruleId, state, operator);
    }

    /**
     * 权益校验
     *
     * @param dto
     */
    public void checkPrivilege(SaveCardLevelConfigDto dto) {
        List<SaveCardLevelPrivilegeConfigDto> privilegeConfigs = dto.getPrivilegeConfigs();
        if (CollectionUtils.isEmpty(privilegeConfigs)) {
            throw new ServiceException(RespCodeEnum.CONFIG_20001);
        }
        List<Long> privilegeIds = privilegeConfigs.stream().map(SaveCardLevelPrivilegeConfigDto::getPrivilegeId).collect(Collectors.toList());
        List<PrivilegeConfigInfo> privilegeConfigInfos = privilegeConfigBiz.listByIds(privilegeIds);
        if (CollectionUtils.isEmpty(privilegeConfigInfos)) {
            throw new ServiceException(RespCodeEnum.CONFIG_20001);
        }
        List<PrivilegeConfigInfo> priceRelated = privilegeConfigInfos.stream().filter(item ->
                item.getType().equals(PrivilegeTypeEnum.PRICE.getType())).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(priceRelated)) {
            throw new ServiceException(RespCodeEnum.CONFIG_20002);
        }
        if (priceRelated.stream().noneMatch(item -> item.getClassification().equals(PrivilegeClassificationEnum.DISCOUNT.getType()))) {
            throw new ServiceException(RespCodeEnum.CONFIG_20002);
        }
    }
}
