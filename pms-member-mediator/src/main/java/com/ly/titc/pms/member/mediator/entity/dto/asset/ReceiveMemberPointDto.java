package com.ly.titc.pms.member.mediator.entity.dto.asset;


import lombok.Data;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 16:56
 */
@Data
@Accessors(chain = true)
public class ReceiveMemberPointDto extends BaseMemberDto {

    /**
     * 业务类型：客房单-ROOM，会员-MEMBER，商品部-SHOP ,营销活动 SPM
     */
    private String businessType;

    /**
     * 业务订单编号
     */
    private String businessNo;

    private String actionType;

    /**
     * 积分项目
     */
    private String actionItem;

    /**
     * 积分项目描述
     */
    private String actionItemDesc;

    /**
     * 积分数（加为正，减为负）
     */
    private Integer score;

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 过期时间 yyyy-MM-dd
     */
    private String expireDate;

    /**
     * 备注 原因
     */
    private String remark;



}
