package com.ly.titc.pms.member.mediator.entity.dto.schedule;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：rui
 * @name：ScheduleDataDto
 * @Date：2024-11-21 14:34
 * @Filename：ScheduleDataDto
 */
@Data
@Accessors(chain =true)
public class ScheduleDataDto {

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;
}
