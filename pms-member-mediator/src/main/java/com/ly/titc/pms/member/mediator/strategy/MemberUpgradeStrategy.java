package com.ly.titc.pms.member.mediator.strategy;

import com.ly.titc.pms.member.biz.MemberInfoBiz;
import com.ly.titc.pms.member.com.enums.MemberStateEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardRequestDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardResultDto;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.common.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;



/**
 * 会员升级策略
 *
 * <AUTHOR>
 * @date 2025年06月26日
 */
@Slf4j
@Component
public class MemberUpgradeStrategy extends AbstractMemberCardIssueStrategy {

    @Autowired
    private MemberInfoBiz memberInfoBiz;



    @Override
    public boolean supports(IssueMemberCardRequestDto dto) {
        return StringUtils.isNotBlank(dto.getMemberNo()) && StringUtils.isBlank(dto.getCustomerNo());
    }

    @Override
    protected void validateRequest(IssueMemberCardRequestDto dto) {
        // 调用父类通用校验
        super.validateRequest(dto);
        // 会员升级特有校验
        if (StringUtils.isBlank(dto.getMemberNo())) {
            throw new ServiceException(RespCodeEnum.CODE_400);
        }
    }

    @Override
    protected IssueMemberCardResultDto doExecute(IssueMemberCardRequestDto dto) {
        String memberNo = dto.getMemberNo();
        // 校验会员状态，如果会员不存在或已注销，则走重新发放会员卡的逻辑
        if (!isValidMember(dto)) {
            log.info("会员{}不存在或已注销，转为重新发放会员卡逻辑", memberNo);
            // 这里应该调用客户转会员策略，但为了避免循环依赖，抛出异常
            throw new ServiceException(RespCodeEnum.MEMBER_10011);
        }
        // 调用抽象基类的通用方法
        return handleExistingMemberCardOperation(dto, memberNo);
    }

    /**
     * 检查会员是否有效（存在且未注销）
     */
    private boolean isValidMember(IssueMemberCardRequestDto dto) {
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(dto.getMasterType(), dto.getMasterCode(), dto.getMemberNo());
        if (memberInfo == null) {
            log.info("会员不存在，memberNo:{}", dto.getMemberNo());
            return false;
        }
        if (memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
            log.info("会员已注销，memberNo:{}", dto.getMemberNo());
            return false;
        }
        return true;
    }

}
