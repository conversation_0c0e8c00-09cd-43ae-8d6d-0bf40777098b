package com.ly.titc.pms.member.mediator.handler.schedule;

/**
 * @Author：rui
 * @name：ScheduleHandlerEnum
 * @Date：2024-11-21 14:50
 * @Filename：ScheduleHandlerEnum
 */
public enum ScheduleHandlerEnum {

    // 1 升级 2 降级 3 标签
    MEMBER_UPGRADE_RULE(1, "会员升级规则"),
    MEMBER_RELEGATION_RULE(2, "会员降级规则"),
    MEMBER_TAG(3, "会员标签");

    private Integer action;

    private String desc;

    ScheduleHandlerEnum(Integer action, String desc) {
        this.action = action;
        this.desc = desc;
    }

    public Integer getAction() {
        return action;
    }

    public String getDesc() {
        return desc;
    }
}
