package com.ly.titc.pms.member.mediator.entity.dto.member.config;

import lombok.Data;

/**
 * @Author：rui
 * @name：MemberTagMarkRuleInfoResp
 * @Date：2024-11-8 11:51
 * @Filename：MemberTagMarkRuleInfoResp
 */
@Data
public class MemberTagMarkRuleDto {

    /**
     * id
     */
    private Long id;

    /**
     * 打标条件类型
     */
    private Integer conditionType;

    /**
     * 计算方式
     */
    private Integer calculateType;

    /**
     * 条件值
     */
    private String conditionValue;
}
