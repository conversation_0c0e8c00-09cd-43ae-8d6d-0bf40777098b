package com.ly.titc.pms.member.mediator.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: shawn
 * @email: <EMAIL>
 * @date: 2024/1/29 14:10
 * @description:
 */
@Data
@ConfigurationProperties(prefix = "es.pms.member")
public class ElasticsearchConfig {

    /**
     * es host
     */
    private String host;


    private EsKeyProperties key;


    @Data
    public static class EsKeyProperties {

        /**
         * key mapping 索引token
         */
        private String token;

        /**
         * key mapping 索引index
         */
        private String index;

    }
}
