package com.ly.titc.pms.member.mediator.rpc.dubbo.coupon;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.template.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.ApplicableHotelResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.CouponTemplateDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.CouponTemplateListResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.HotelInfoResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.SaveCouponTemplateInfoResp;
import com.ly.titc.pms.spm.dubbo.interfaces.CouponTemplateDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Slf4j
@Component
public class CouponTemplateDecorator {
    @DubboReference(group = "${spm-dsf-dubbo-group}")
    private CouponTemplateDubboService couponTemplateDubboService;


    /**
     * 优惠券分页列表
     */
    public Pageable<CouponTemplateListResp> pageCouponTemplateList(GetCouponTemplateListReq req){
        Response<Pageable<CouponTemplateListResp>> response = couponTemplateDubboService.pageCouponTemplateList(req);
        return Response.getValidateData(response);
    }

    /**
     * 优惠券列表
     */
    public List<CouponTemplateListResp> listCouponTemplate(GetCouponTemplateListReq req){
        Pageable<CouponTemplateListResp> pageable = this.pageCouponTemplateList(req);
        return pageable.getDatas();
    }

    /**
     * 查询优惠券模版明细
     */
    public CouponTemplateDetailResp getCouponTemplateDetail(GetCouponTemplateDetailReq req){
        Response<CouponTemplateDetailResp> couponTemplateDetail = couponTemplateDubboService.getCouponTemplateDetail(req);
        return Response.getValidateData(couponTemplateDetail);
    }


    /**
     * 保存优惠券模版(创建(复制)/更新)
     */
    public SaveCouponTemplateInfoResp saveCouponTemplate(SaveCouponTemplateReq req){
        Response<SaveCouponTemplateInfoResp> response = couponTemplateDubboService.saveCouponTemplate(req);
        return Response.getValidateData(response);
    }
    /**
     * 模版删除
     */
    public void deleteCouponTemplate(DeleteCouponTemplateReq req){
        Response<String> response = couponTemplateDubboService.deleteCouponTemplate(req);
        Response.getValidateData(response);
    }

    /**
     * 模版作废
     */
    public void repealCouponTemplate(UpdateCouponTemplateStateReq req){
        Response<String> response = couponTemplateDubboService.repealCouponTemplate(req);
        Response.getValidateData(response);
    }


    /**
     * 查询优惠券模版适用酒店
     */
    public ApplicableHotelResp getApplicableHotelInfo(GetApplicableHotelReq req){
        Response<ApplicableHotelResp> response = couponTemplateDubboService.getApplicableHotelInfo(req);
        return Response.getValidateData(response);
    }


    public List<HotelInfoResp> listApplicableHotelInfo(GetApplicableHotelReq req) {
        Response<List<HotelInfoResp>> response = couponTemplateDubboService.listApplicableHotelInfo(req);
        return Response.getValidateData(response);
    }
}
