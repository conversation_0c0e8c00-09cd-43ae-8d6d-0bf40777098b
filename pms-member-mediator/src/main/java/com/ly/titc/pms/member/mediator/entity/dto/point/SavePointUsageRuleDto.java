package com.ly.titc.pms.member.mediator.entity.dto.point;

import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 10:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SavePointUsageRuleDto extends SaveUsageRuleDto {



}
