package com.ly.titc.pms.member.mediator.config;

import com.ly.titc.springboot.dcdb.dal.core.service.CombinedService;
import com.ly.titc.springboot.dcdb.dal.core.service.impl.CombinedServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class BeanConfig {

    @Bean("combinedService")
    @ConditionalOnMissingBean
    public CombinedService getCombinedService() {
        return new CombinedServiceImpl();
    }
}
