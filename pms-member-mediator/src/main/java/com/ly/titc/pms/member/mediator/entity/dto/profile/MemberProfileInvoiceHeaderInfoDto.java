package com.ly.titc.pms.member.mediator.entity.dto.profile;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 常用发票
 *
 * <AUTHOR>
 * @date 2024/11/19 17:55
 */
@Data
public class MemberProfileInvoiceHeaderInfoDto {

    /**
     * 发票抬头编号
     */
    private Long invoiceHeaderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 发票类型；1:个人;2:企业;3:非企业性单位
     */
    private Integer invoiceType;

    /**
     * 发票抬头
     */
    private String headerName;

    /**
     * 税号
     */
    private String taxCode;

    /**
     * 是否需要增值税专用发票
     */
    private Integer needSpecialInvoice;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 公司电话
     */
    private String companyTel;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
