package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.member.dal.entity.po.PrivilegeApplicableDataMapping;
import com.ly.titc.pms.member.dal.entity.po.PrivilegeConfigInfo;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.SavePrivilegeApplicableDataMappingDto;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.SavePrivilegeConfigDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 18:22
 */
@Mapper(componentModel = "spring")
public interface PrivilegeConfigMedConverter {

    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator")
    })
    PrivilegeConfigInfo convertDtoToPo(SavePrivilegeConfigDto dto);

    @Mappings({
            @Mapping(target = "privilegeId",source = "privilegeId"),
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator")
    })
    PrivilegeApplicableDataMapping convertDtoToPo(SavePrivilegeApplicableDataMappingDto mapping, Long privilegeId, String operator);

}
