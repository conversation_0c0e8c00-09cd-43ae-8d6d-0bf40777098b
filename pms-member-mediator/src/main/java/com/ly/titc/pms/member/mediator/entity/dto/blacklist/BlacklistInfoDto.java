package com.ly.titc.pms.member.mediator.entity.dto.blacklist;

import com.ly.titc.pms.member.mediator.entity.dto.general.DictDto;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 黑名单信息
 *
 * <AUTHOR>
 * @date 2024/12/19 11:39
 */
@Data
public class BlacklistInfoDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 黑名单编号
     */
    private String blacklistNo;

    /**
     * 场景适用范围 0 部分 1 全部
     */
    private Integer sceneScope;

    /**
     * 适用场景列表
     */
    private List<DictDto> scenes;

    /**
     * 渠道适用范围 0 部分 1 全部
     */
    private Integer platformChannelScope;

    /**
     * 适用渠道列表
     */
    private List<DictDto> platformChannels;

    /**
     * 原因
     */
    private String reason;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 来源类型
     */
    private Integer sourceType;

    /**
     * 来源（集团/门店）
     */
    private String source;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
