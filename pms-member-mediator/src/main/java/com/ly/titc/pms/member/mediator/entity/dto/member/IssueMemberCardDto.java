package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 发放的会员卡
 *
 * <AUTHOR>
 * @date 2024/11/19 17:14
 */
@Data
@Accessors(chain = true)
public class IssueMemberCardDto {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 会员卡类型
     */
    private Integer cardType;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 会员卡等级名称
     */
    private String cardLevelName;

    /**
     * 会员卡有效期开始日期 yyyy-MM-dd
     */
    private String effectBeginDate;

    /**
     * 会员卡有效期结束日期 yyyy-MM-dd
     */
    private String effectEndDate;

    /**
     * 是否长期有效 0 否 1 是
     */
    private Integer isLongTerm;

    /**
     * 发放门店类型 集团:BLOC;门店:HOTEL
     */
    private String issueHotelType;

    /**
     * 发放门店(集团编号; 酒店编号)
     */
    private String issueHotel;

    /**
     * 等级变化类型，1注册; 2升级；3保级成功；4保级失败; 5手动处理; 6迁移数据
     */
    private Integer changeType;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 发放人
     */
    private String issueUser;

    /**
     * 类型
     */
    private String bizType;

    /**
     *
     */
    private String bizNo;

}
