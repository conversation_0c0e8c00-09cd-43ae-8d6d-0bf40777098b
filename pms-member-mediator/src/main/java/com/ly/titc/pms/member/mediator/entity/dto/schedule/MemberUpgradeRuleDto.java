package com.ly.titc.pms.member.mediator.entity.dto.schedule;

import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberUogradeRuleDto
 * @Date：2024-11-21 14:45
 * @Filename：MemberUogradeRuleDto
 */
@Data
public class MemberUpgradeRuleDto extends ScheduleDataDto{

    /**
     * id
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 原等级
     */
    private Integer sourceLevel;

    /**
     * 目标等级
     */
    private Integer targetLevel;

    /**
     * 升级成功执行类型 ALL - 全部  ANY-满足任何一个条件
     */
    private String upgradeSuccessfulPerformType;

    /**
     * 描述
     */
    private String description;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 状态 0 无效 1 正常
     */
    private Integer state;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    private List<MemberUpgradeRuleDetailDto> details;
}
