package com.ly.titc.pms.member.mediator.entity.dto.asset;


import lombok.Data;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-18 19:46
 */
@Data
@Accessors(chain = true)
public class ConsumeMemberPointDto extends BaseMemberDto {


    /**
     * 业务类型：客房单-ROOM，会员-MEMBER，商品部-SHOP ,营销活动 SPM
     */
    private String businessType;

    /**
     * 业务订单编号
     */
    private String businessNo;

    /**
     * 消费类型
     */
    private String consumeType;

    /**
     * 消费描述不能为空
     */
    private String consumeDesc;

    /**
     * 积分项目
     */
    private String actionItem;

    /**
     * 积分项目描述
     */
    private String actionItemDesc;

    /**
     * 积分数（加为正，减为负）
     */
    private Integer score;

    /**
     * 使用场景
     */
    private String scene;


    /**
     * 备注
     */
    private String remark;

    private String operator;




}
