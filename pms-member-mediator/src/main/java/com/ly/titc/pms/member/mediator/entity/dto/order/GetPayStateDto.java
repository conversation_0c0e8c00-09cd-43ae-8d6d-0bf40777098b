package com.ly.titc.pms.member.mediator.entity.dto.order;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 11:54
 */
@Data
@Accessors(chain = true)
public class GetPayStateDto {

    /**
     * 集团code
     */
    private String blocCode;
    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 会员订单号
     */
    private String memberOrderNo;

    /**
     * 会员支付申请单号
     */
    private String memberOrderPayNo;

    /**
     * 操作人
     */
    private String operator;

}
