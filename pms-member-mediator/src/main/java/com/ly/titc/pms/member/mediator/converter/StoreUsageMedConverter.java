package com.ly.titc.pms.member.mediator.converter;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MasterObject;
import com.ly.titc.pms.member.com.enums.AssetTypeEnum;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageModeScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageRuleScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.StoreUsageRuleInfo;
import com.ly.titc.pms.member.entity.bo.PageUsageRuleScopeMappingBo;
import com.ly.titc.pms.member.entity.wrapper.StoreUsageRuleWrapper;
import com.ly.titc.pms.member.mediator.entity.dto.point.BlocScopeUsageDto;
import com.ly.titc.pms.member.mediator.entity.dto.point.ListBlocScopeUsageDto;
import com.ly.titc.pms.member.mediator.entity.dto.store.PageStoreUsageDto;
import com.ly.titc.pms.member.mediator.entity.dto.store.SaveStoreUsageRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.store.StoreUsageRuleDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.store.StoreUsageRuleDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/26 11:06
 */
@Mapper(componentModel = "spring")
public interface StoreUsageMedConverter extends UsageMedConverter{

    @Mappings({
            @Mapping(target = "usageRuleId", source = "id"),
            @Mapping(target = "scopePlatformChannels", source = "scopePlatformChannels",ignore = true),
    })
    StoreUsageRuleDto convertBase(StoreUsageRuleInfo ruleInfo);

    @Mappings({
            @Mapping(target = "masterType", source = "usageMasterType"),
            @Mapping(target = "masterCode", source = "usageMasterCode"),
    })
    MasterObject convertPoToDto(AssetUsageModeScopeMapping scopeMapping);

    default StoreUsageRuleDto convertPoToDto(StoreUsageRuleInfo ruleInfo){
        StoreUsageRuleDto dto = convertBase(ruleInfo);
        dto.setScopePlatformChannels(Arrays.asList(ruleInfo.getScopePlatformChannels().split(",")));
        return dto;
    }

    PageUsageRuleScopeMappingBo convertDtoToBo(PageStoreUsageDto dto);

    @Mappings({
            @Mapping(target = "scopeSources",source = "scopeSources",ignore = true),
            @Mapping(target = "scopePlatformChannels",source = "scopePlatformChannels",ignore = true),
            @Mapping(target = "scenes",source = "scenes",ignore = true),

    })
    StoreUsageRuleDetailDto convertDetailDto(StoreUsageRuleInfo info);


    default List<StoreUsageRuleDetailDto> convertStoreDetail(List<StoreUsageRuleInfo> records,
                                                             List<AssetUsageModeScopeMapping> usageModeScopeMappings,
                                                             List<AssetUsageRuleScopeMapping> scopeMappings){
        List<StoreUsageRuleDetailDto> detailDtoList = new ArrayList<>();
        Map<Long, List<AssetUsageModeScopeMapping>> usageModeMap = usageModeScopeMappings.stream().collect(Collectors.groupingBy(AssetUsageModeScopeMapping::getRuleId));
        Map<Long, List<AssetUsageRuleScopeMapping>> scopMap = scopeMappings.stream().collect(Collectors.groupingBy(AssetUsageRuleScopeMapping::getRuleId));

        records.forEach(ruleInfo -> {
            StoreUsageRuleDetailDto detailDto = convertDetailDto(ruleInfo);
            Long ruleId = ruleInfo.getId();
            List<AssetUsageModeScopeMapping> usageModeMapping = usageModeMap.get(ruleId);
            if(CollectionUtils.isNotEmpty(usageModeMapping)) {
                usageModeMapping = usageModeMapping.stream().filter(s -> StringUtils.isNotBlank(s.getHotelCode())).collect(Collectors.toList());
                detailDto.setUsageHotelCodes(usageModeMapping.stream().map(AssetUsageModeScopeMapping::getHotelCode).collect(Collectors.toList()));
            }
            List<AssetUsageRuleScopeMapping> scopeMappingList =  scopMap.get(ruleId);
            if(CollectionUtils.isNotEmpty(scopeMappingList)) {
                scopeMappingList = scopeMappingList.stream().filter(s -> StringUtils.isNotBlank(s.getHotelCode())).collect(Collectors.toList());
                detailDto.setScopeHotelCodes(scopeMappingList.stream().map(AssetUsageRuleScopeMapping::getHotelCode).distinct().collect(Collectors.toList()));
            }
            String scopeSources =  ruleInfo.getScopeSources();
            if(StringUtils.isNotEmpty(scopeSources)){
                detailDto.setScopeSources(Arrays.asList(scopeSources.split(",")));
            }
            String scopePlatformChannels = ruleInfo.getScopePlatformChannels();
            if(StringUtils.isNotEmpty(scopePlatformChannels)){
                detailDto.setScopePlatformChannels(Arrays.asList(scopePlatformChannels.split(",")));
            }
            if(StringUtils.isNotEmpty(ruleInfo.getScenes())){
                detailDto.setScenes(Arrays.asList(ruleInfo.getScenes().split(",")));
            }
            detailDtoList.add(detailDto);
        });
        return detailDtoList;
    }

    default StoreUsageRuleWrapper convertWrapper(SaveStoreUsageRuleDto dto){
        StoreUsageRuleWrapper wrapper = new StoreUsageRuleWrapper();
        StoreUsageRuleInfo ruleInfo = convertBase(dto);
        String scopeSourceStr = String.join(",", dto.getScopeSources());
        String scopePlatformChannelStr  = String.join(",",dto.getScopePlatformChannels());
        ruleInfo.setScopeSources(scopeSourceStr);
        ruleInfo.setScopePlatformChannels(scopePlatformChannelStr);
        ruleInfo.setScenes( String.join(",",dto.getScenes()));
        //组装info
        wrapper.setRuleInfo(ruleInfo);
        wrapper.setScopeMappings(convertMapping(dto, AssetTypeEnum.STORE.getType()));
        wrapper.setUsageModeScopeMappings(convertUsage(dto,AssetTypeEnum.STORE.getType()));
        return wrapper;
    }

    @Mappings({
            @Mapping(target = "scopePlatformChannels",source = "scopePlatformChannels",ignore = true),
            @Mapping(target = "scopeSources",source = "scopeSources",ignore = true),
            @Mapping(target = "scenes",source = "scenes",ignore = true),
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator")
    })
    StoreUsageRuleInfo convertBase(SaveStoreUsageRuleDto dto);


    PageUsageRuleScopeMappingBo convertDtoToBo(ListBlocScopeUsageDto dto);

    @Mappings({
            @Mapping(target = "scopeHotelCode", source = "hotelCode")
    })
    BlocScopeUsageDto convertPoToBlocDto(AssetUsageRuleScopeMapping mapping);
}
