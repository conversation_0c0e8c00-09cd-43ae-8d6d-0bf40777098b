package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 回收会员卡结果DTO
 *
 * <AUTHOR>
 * @date 2024/12/25 10:00
 */
@Data
@Accessors(chain = true)
public class RecycleMemberCardResultDto {

    /**
     * 是否回收成功
     */
    private boolean success;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 回收的会员卡等级
     */
    private Integer cardLevel;

    /**
     * 回收的会员卡等级名称
     */
    private String cardLevelName;

    /**
     * 操作类型（CANCEL_MEMBER: 注销会员, DOWNGRADE: 会员降级, RECYCLE_CARD: 回收会员卡）
     */
    private String operationType;

    public static RecycleMemberCardResultDto success(String memberNo, String memberCardNo, Integer cardLevel, String cardLevelName, String operationType) {
        return new RecycleMemberCardResultDto()
                .setSuccess(true)
                .setMemberNo(memberNo)
                .setMemberCardNo(memberCardNo)
                .setCardLevel(cardLevel)
                .setCardLevelName(cardLevelName)
                .setOperationType(operationType);
    }

    public static RecycleMemberCardResultDto failure(String failureReason) {
        return new RecycleMemberCardResultDto()
                .setSuccess(false)
                .setFailureReason(failureReason);
    }

}
