package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.cashier.dubbo.entity.request.CashierTermListReq;
import com.ly.titc.cashier.dubbo.entity.request.config.PayCashierSourceGetReq;
import com.ly.titc.cashier.dubbo.entity.request.merchant.CashierHotelPaySettingReq;
import com.ly.titc.cashier.dubbo.entity.response.CashierTermListResp;
import com.ly.titc.cashier.dubbo.entity.response.config.PayCashierConfigResp;
import com.ly.titc.cashier.dubbo.entity.response.merchant.CashierPayOpenStateDto;
import com.ly.titc.cashier.dubbo.enums.CashierDisplayPayChannelEnum;
import com.ly.titc.cashier.dubbo.enums.PayChannelEnum;
import com.ly.titc.pms.ecrm.dubbo.enums.StateEnum;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.DisplayPayChannelStateDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.PayCashierConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.PayCashierConfigQueryDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.TermListDto;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-3 19:32
 */
@Mapper(componentModel = "spring")
public interface CashierMedConverter {

    CashierTermListReq convertTermListReq(BaseDto dto);

    List<TermListDto> convertTemList(List<CashierTermListResp> termList);

    PayCashierSourceGetReq convert(PayCashierConfigQueryDto dto);

    default PayCashierConfigDto convert(PayCashierConfigResp resp, List<CashierPayOpenStateDto> openStates){
        PayCashierConfigDto dto = convertBase(resp);
        List<DisplayPayChannelStateDto> displayPayChannelDtos = new ArrayList<>();
        List<String> displayPayChannels = resp.getDisplayPayChannels();
        //获取开通的支付渠道
        List<String> openPayChannels = new ArrayList<>();
        AtomicReference<Boolean> isUsePos = new AtomicReference<>(false);
        openStates.forEach(openStateDto -> {
            if(openStateDto.getOpenState().equals(StateEnum.VALID.getCode())){
                openPayChannels.add(openStateDto.getPayChannel());
            }
            if(openStateDto.getIsUsePos().equals(StateEnum.VALID.getCode())){
                isUsePos.set(true);
            }
        });
        displayPayChannels.forEach(displayPayChannel -> {
            DisplayPayChannelStateDto displayPayChannelStateDto = new DisplayPayChannelStateDto();
            if(displayPayChannel.equals(CashierDisplayPayChannelEnum.AGGPAY.getCode())){
               boolean openState = openPayChannels.stream().anyMatch(openPayChannel ->(
                        (openPayChannel.equals(PayChannelEnum.ALIPAY.getCode()) )
                        ||(openPayChannel.equals(PayChannelEnum.WECHAT.getCode()))
                        || (openPayChannel.equals(PayChannelEnum.UNIONPAY.getCode()))
                ));
                displayPayChannelStateDto.setState(openState ?StateEnum.VALID.getCode():StateEnum.NO_VALID.getCode());
                displayPayChannelStateDto.setDisplayPayChannel(CashierDisplayPayChannelEnum.AGGPAY.getCode());
                displayPayChannelStateDto.setIsUsePos(isUsePos.get()?StateEnum.VALID.getCode():StateEnum.NO_VALID.getCode());
            }else if(displayPayChannel.equals(CashierDisplayPayChannelEnum.POSCARD.getCode())){
                boolean openState = openPayChannels.stream().anyMatch(openPayChannel ->(
                        openPayChannel.equals(PayChannelEnum.POSCARD.getCode())
                ));
                displayPayChannelStateDto.setState(openState ?StateEnum.VALID.getCode():StateEnum.NO_VALID.getCode());
                displayPayChannelStateDto.setDisplayPayChannel(CashierDisplayPayChannelEnum.POSCARD.getCode());
                displayPayChannelStateDto.setIsUsePos(isUsePos.get()?StateEnum.VALID.getCode():StateEnum.NO_VALID.getCode());

            }else{
                displayPayChannelStateDto.setState(StateEnum.VALID.getCode());
                displayPayChannelStateDto.setDisplayPayChannel(displayPayChannel);
                displayPayChannelStateDto.setIsUsePos(StateEnum.NO_VALID.getCode());
            }
            displayPayChannelStateDto.setDisplayPayChannelDesc(CashierDisplayPayChannelEnum.getByCode(displayPayChannel).getDesc());
            displayPayChannelDtos.add(displayPayChannelStateDto);
        });
        dto.setDisplayPayChannelDtos(displayPayChannelDtos);
        return dto;
    }

    PayCashierConfigDto convertBase(PayCashierConfigResp resp);

    CashierHotelPaySettingReq convertSetting(BaseDto dto);


}
