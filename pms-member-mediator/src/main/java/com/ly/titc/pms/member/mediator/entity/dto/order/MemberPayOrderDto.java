package com.ly.titc.pms.member.mediator.entity.dto.order;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 11:53
 */
@Data
@Accessors(chain = true)
public class MemberPayOrderDto {
    /**
     * 会员订单号
     */
    private String memberOrderNo;

    /**
     * 金额
     */
    private BigDecimal amount;


    /**
     * 平台渠道
     */

    private String platformChannel;

    /**
     * 归属类型
     */
    private Integer masterType;

    /**
     * 归属编码
     */
    private String masterCode;

    /**
     * 酒店code （Pms端必传）
     */
    private String hotelCode;

    /**
     * 集团code （Crs端必传）
     */
    private String blocCode;

    /**
     * 操作人
     */
    private String operator;
}
