package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 保存会员等级保级规则
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Getter
@Setter
@Accessors(chain = true)
public class SaveCardLevelRelegationRuleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 保级规则ID
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 会员保级等级
     */
    private Integer sourceLevel;

    /**
     * 保级成功执行类型：ALL-全部条件;ANY-满足任一个条件
     */
    private String relegationSuccessfulPerformType;

    /**
     * 保级失败规则；1-降至上一等级、2-降至最低等级、3-降至满足等级、4-降至指定等级
     */
    private Integer relegationFailureRule;

    /**
     * 保级失败降至指定等级
     */
    private Integer targetLevel;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 保级规则明细
     */
    private List<SaveCardLevelRelegationRuleDetailDto> ruleDetails;
}
