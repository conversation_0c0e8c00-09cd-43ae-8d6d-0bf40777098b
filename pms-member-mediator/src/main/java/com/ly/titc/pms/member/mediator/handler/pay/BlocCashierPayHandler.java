package com.ly.titc.pms.member.mediator.handler.pay;

import com.ly.titc.cashier.dubbo.entity.request.CashierPayInfoGetReq;
import com.ly.titc.cashier.dubbo.entity.request.CashierRefundApplyReq;
import com.ly.titc.cashier.dubbo.entity.response.CashierPayDetailInfoResp;
import com.ly.titc.cashier.dubbo.entity.response.CashierRefundDetailResp;
import com.ly.titc.cashier.dubbo.entity.response.CashierRefundResp;
import com.ly.titc.pms.account.dubbo.entity.response.pay.AccountPayTradeResp;
import com.ly.titc.pms.member.com.enums.PayRouteEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.entity.dto.order.GetPayStateDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.OrderRefundResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PayOrderResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;




/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-9-11 19:47
 */
@Component
@Slf4j
public class BlocCashierPayHandler extends AbstractPayHandler {


    /**
     * 查询单条支付结果
     */
    @Override
    public PayOrderResultDto getPayState(GetPayStateDto payStateDto){
        //1.参数转换
        CashierPayInfoGetReq req = orderMedConverter.convertBlocDetail(payStateDto);
        //2.调用收银台查询支付结果
        CashierPayDetailInfoResp detailResp= cashierPayDecorator.getPayInfo(req);
        log.info("调用收银台查询支付结果:{}", detailResp);
        AccountPayTradeResp tradeResp = orderMedConverter.convertTradeResp(detailResp);
        PayOrderResultDto resultDto = orderMedConverter.convertResult(tradeResp);
        resultDto.setOnlinePayNo(detailResp.getOnlinePayNo());
        resultDto.setPayedTime(detailResp.getPayedTime());
        return resultDto;
    }

    @Override
    public OrderRefundResultDto refund(MemberOrderRefundInfo processRefundOrder, MemberOrderPayInfo memberOrderPayInfo) {
        // 调用收银台退款
        CashierRefundApplyReq req = refundOrderMedConverter.convertRefundReq(processRefundOrder, memberOrderPayInfo);
        CashierRefundResp refundResp = cashierPayDecorator.refund(req);
        return refundOrderMedConverter.convert(refundResp);
    }

    @Override
    public OrderRefundResultDto getRefundState(MemberOrderRefundInfo memberOrderRefundInfo, String operator) {
        CashierRefundDetailResp refundResult = cashierPayDecorator.getRefundInfo(refundOrderMedConverter.convertRefundDetailReq(memberOrderRefundInfo, operator));
        CashierRefundResp refundResp = refundOrderMedConverter.convertRefundResultResp(refundResult);
        return refundOrderMedConverter.convert(refundResp);

    }

    @Override
    public String getRoute() {
        return PayRouteEnum.Bloc_Cashier.getRoute();
    }
}
