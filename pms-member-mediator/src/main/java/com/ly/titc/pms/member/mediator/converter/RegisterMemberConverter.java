package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerDetailInfoResp;
import com.ly.titc.pms.member.dubbo.enums.HotelTypeEnum;
import com.ly.titc.pms.member.mediator.entity.dto.member.CustomerInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardRequestDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

/**
 * 注册会员转换器
 *
 * <AUTHOR>
 * @date 2025年06月26日
 */
@Mapper(componentModel = "spring")
public interface RegisterMemberConverter {

    /**
     * 构建注册会员DTO
     *
     * @param dto 发放会员卡请求DTO
     * @param customerInfo 客户详细信息
     * @return 注册会员DTO
     */
    @Mapping(target = "masterType", source = "dto.masterType")
    @Mapping(target = "masterCode", source = "dto.masterCode")
    @Mapping(target = "mobile", source = "customerInfo.mobile")
    @Mapping(target = "realName", source = "customerInfo.realName")
    @Mapping(target = "nickName", source = "customerInfo.nickName")
    @Mapping(target = "enName", source = "customerInfo.enName")
    @Mapping(target = "gender", source = "customerInfo.gender")
    @Mapping(target = "birthday", source = "customerInfo.birthday")
    @Mapping(target = "idType", source = "customerInfo.idType")
    @Mapping(target = "idNo", source = "customerInfo.idNo")
    @Mapping(target = "registerHotelType", source = "dto", qualifiedByName = "mapRegisterHotelType")
    @Mapping(target = "registerHotel", source = "dto", qualifiedByName = "mapRegisterHotel")
    @Mapping(target = "operator", source = "dto.bizType")
    @Mapping(target = "source", source = "dto.bizType")
    @Mapping(target = "bizType", source = "dto.bizType")
    @Mapping(target = "bizNo", source = "dto.bizNo")
    @Mapping(target = "reason", source = "dto.reason")
    @Mapping(target = "memberCardInfo", ignore = true) // 需要单独设置
    @Mapping(target = "customerInfo", ignore = true) // 需要单独设置
    RegisterMemberDto buildRegisterMemberDto(IssueMemberCardRequestDto dto, CustomerDetailInfoResp customerInfo);

    /**
     * 构建客户信息DTO
     *
     * @param dto 发放会员卡请求DTO
     * @return 客户信息DTO
     */
    @Mapping(target = "blocCode", source = "masterCode")
    @Mapping(target = "hotelCode", source = "hotelCode")
    @Mapping(target = "customerNo", source = "customerNo")
    CustomerInfoDto buildCustomerInfoDto(IssueMemberCardRequestDto dto);

    /**
     * 映射注册门店类型
     */
    @Named("mapRegisterHotelType")
    default String mapRegisterHotelType(IssueMemberCardRequestDto dto) {
        if (StringUtils.isNotBlank(dto.getHotelCode())) {
            return HotelTypeEnum.HOTEL.getType();
        } else {
            return HotelTypeEnum.BLOC.getType();
        }
    }

    /**
     * 映射注册门店
     */
    @Named("mapRegisterHotel")
    default String mapRegisterHotel(IssueMemberCardRequestDto dto) {
        if (StringUtils.isNotBlank(dto.getHotelCode())) {
            return dto.getHotelCode();
        } else {
            return null;
        }
    }
}
