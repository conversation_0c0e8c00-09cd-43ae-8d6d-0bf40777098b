package com.ly.titc.pms.member.mediator.handler.check;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.member.biz.MemberInfoBiz;
import com.ly.titc.pms.member.com.enums.MemberRegisterCheckEnum;
import com.ly.titc.pms.member.com.enums.MemberStateEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：rui
 * @name：VerifyCodeCheckHandler
 * @Date：2024-11-25 16:33
 * @Filename：VerifyCodeCheckHandler
 */
@Component
@Slf4j
public class MobileCheckHandler extends AbstractRegisterCheckHandler {

    @Resource
    private MemberInfoBiz memberInfoBiz;

    @Override
    public Integer getAction() {
        return MemberRegisterCheckEnum.MOBILE.getAction();
    }

    @Override
    public void check(RegisterMemberDto dto) {

        Integer masterType = dto.getMasterType();
        String masterCode = dto.getMasterCode(), traceNo = TraceNoUtil.getTraceNo(), mobile = dto.getMobile();

        List<MemberInfo> memberInfos = memberInfoBiz.listByMobiles(masterType, masterCode, mobile, MemberStateEnum.VALID.getState());
        if (CollectionUtils.isNotEmpty(memberInfos)) {
            throw new ServiceException(String.format("手机号【%s】已注册！", mobile), RespCodeEnum.CODE_400.getCode());
        }

    }

}
