package com.ly.titc.pms.member.mediator.rpc.dsf.mdm;

import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.api.RegionService;
import com.ly.titc.mdm.entity.request.area.ListAreasReq;
import com.ly.titc.mdm.entity.request.region.*;
import com.ly.titc.mdm.entity.response.area.AreaResp;
import com.ly.titc.mdm.entity.response.area.AreaTreeResp;
import com.ly.titc.mdm.entity.response.region.RegionResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @classname RegionDecorator
 * @descrition
 * @since 2021/10/8 下午8:13
 */
@Slf4j
@Component
public class RegionDecorator {

    @DubboReference(protocol = "dsf", providedBy = "${mdm-dsf-group}", version = "${mdm-dsf-service-version}")
    private RegionService regionService;

    /**
     * 查询区域
     *
     * @param id
     * @return
     */
    public RegionResp getById(Integer id) {
        GetByIdReq request = new GetByIdReq();
        request.setId(id);
        Response<RegionResp> response = regionService.getById(request);
        return Response.getValidateData(response);
    }

    public List<RegionResp> getByIds(List<Integer> ids) {
        ListByIdsReq request = new ListByIdsReq();
        request.setIds(ids);
        Response<List<RegionResp>> response = regionService.listByIds(request);
        return Response.getValidateData(response);
    }

    public List<RegionResp> listRegionTree(String needChild, Integer regionId) {
        ListRegionTreesReq req = new ListRegionTreesReq();
        req.setNeedChild(needChild);
        req.setRegionId(regionId);
        Response<List<RegionResp>> resp = regionService.listRegionTrees(req);
        return Response.getValidateData(resp);
    }

    public List<RegionResp> listByPid(ListByPidReq req){
        Response<List<RegionResp>> resp = regionService.listByPid(req);
        return Response.getValidateData(resp);
    }

    public List<RegionResp> listByLevel(ListRegionByLevelReq req){
        Response<List<RegionResp>> resp = regionService.listByLevel(req);
        return Response.getValidateData(resp);
    }

    public String getAdministrativeRegion(String code){
        GetAdministrativeRegionReq req = new GetAdministrativeRegionReq();
        req.setCode(code);
        return Response.getValidateData(regionService.getAdministrativeRegion(req));
    }
}
