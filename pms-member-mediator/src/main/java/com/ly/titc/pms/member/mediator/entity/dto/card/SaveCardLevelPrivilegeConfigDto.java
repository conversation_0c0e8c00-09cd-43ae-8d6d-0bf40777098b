package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 保存卡等级权益
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Getter
@Setter
@Accessors(chain = true)
public class SaveCardLevelPrivilegeConfigDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 权益ID
     */
    private Long privilegeId;

    /**
     * 排序
     */
    private Integer sort;

}
