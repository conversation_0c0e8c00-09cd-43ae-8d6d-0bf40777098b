package com.ly.titc.pms.member.mediator.strategy;

import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardRequestDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 会员卡发放策略工厂
 *
 * <AUTHOR>
 * @date 2025年06月26日
 */
@Slf4j
@Component
public class MemberCardIssueStrategyFactory {

    @Autowired
    private List<MemberCardIssueStrategy> strategies;

    /**
     * 根据请求参数选择合适的策略
     *
     * @param dto 发卡请求
     * @return 策略实例
     * @throws IllegalArgumentException 如果没有找到合适的策略
     */
    public MemberCardIssueStrategy getStrategy(IssueMemberCardRequestDto dto) {
        for (MemberCardIssueStrategy strategy : strategies) {
            if (strategy.supports(dto)) {
                log.debug("选择策略: {}", strategy.getClass().getSimpleName());
                return strategy;
            }
        }
        throw new IllegalArgumentException("未找到合适的发卡策略，请检查请求参数");
    }
}
