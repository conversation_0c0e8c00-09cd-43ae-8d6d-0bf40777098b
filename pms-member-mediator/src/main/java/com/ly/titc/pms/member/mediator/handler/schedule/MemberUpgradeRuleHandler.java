package com.ly.titc.pms.member.mediator.handler.schedule;

import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelUpgradeRuleResp;
import com.ly.titc.pms.member.biz.MemberCardInfoBiz;
import com.ly.titc.pms.member.biz.MemberCardLevelChangeRecordBiz;
import com.ly.titc.pms.member.com.enums.*;
import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.mediator.converter.ScheduleMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.*;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.watcher.common.utils.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberUpgradeRuleHandler
 * @Date：2024-11-21 14:24
 * @Filename：MemberUpgradeRuleHandler
 */
public class MemberUpgradeRuleHandler extends AbstractScheduleHandler<MemberUpgradeRuleDto> {

    @Resource
    private ScheduleMedConverter scheduleMedConverter;

    @Resource
    private MemberCardInfoDecorator memberCardInfoDecorator;

    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;

    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;

    @Resource
    private MemberCardMedService memberCardMedService;

    @Override
    public List<MemberUpgradeRuleDto> getData() {
        String trackingId = TraceNoUtil.getTraceNo();
        List<MemberCardLevelUpgradeRuleResp> memberCardLevelUpgradeRuleRespList = memberCardInfoDecorator.listAllValidCardLevelUpgradeRule(StatusEnum.VALID.getStatus());
        return scheduleMedConverter.convertUpgradeRuleDto(memberCardLevelUpgradeRuleRespList);
    }

    @Override
    public void doScheduleMain(Integer masterType, String masterCode, List<MemberUpgradeRuleDto> memberUpgradeRuleDtos, List<String> batchMemberNos, List<MemberCardLevelChangeRecord> records) {

        // 获取每个批次的起始日期
        LocalDateTime startDateLastRegister = records.stream().filter(item -> item.getChangeType().equals(ChangeTypeEnum.REGISTER.getType())).findFirst().orElse(new MemberCardLevelChangeRecord()).getGmtCreate();
        LocalDateTime startDateLastLevelChange = records.stream().filter(item -> item.getChangeType().equals(ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType()) ||
                item.getChangeType().equals(ChangeTypeEnum.UPGRADE_AUTO.getType()) || item.getChangeType().equals(ChangeTypeEnum.UPGRADE_PURCHASE.getType())
                || item.getChangeType().equals(ChangeTypeEnum.DOWN_AUTO.getType())).findFirst().orElse(new MemberCardLevelChangeRecord()).getGmtCreate();
        LocalDateTime endDate = LocalDateTime.now();

        // 预查询所有校验需要的数据
        List<BaseCheckDto> pointRecords = getMemberPointRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        List<BaseCheckDto> pointRecords2 = getMemberPointRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> pointRecordsMap = pointRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        Map<String, List<BaseCheckDto>> pointRecordsMap2 = pointRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));


        List<BaseCheckDto> consumptionRecords = getMemberConsumptionRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> consumptionRecordsMap = consumptionRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> consumptionRecords2 = getMemberConsumptionRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> consumptionRecordsMap2 = consumptionRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));

        List<BaseCheckDto> rechargeRecords = getMemberRechargeRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> rechargeRecordsMap = rechargeRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> rechargeRecords2 = getMemberRechargeRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> rechargeRecordsMap2 = rechargeRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));


        List<BaseCheckDto> checkoutRecords = getMemberCheckoutRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> checkoutRecordsMap = checkoutRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> checkoutRecords2 = getMemberCheckoutRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> checkoutRecordsMap2 = checkoutRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));

        List<BaseCheckDto> stayRecords = getMemberStayRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> stayRecordsMap = stayRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> stayRecords2 = getMemberUnstayRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> stayRecordsMap2 = stayRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));


        List<BaseCheckDto> unstayRecords = getMemberUnstayRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> unstayRecordsMap = unstayRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> unstayRecords2 = getMemberUnstayRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> unstayRecordsMap2 = unstayRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));

        List<BaseCheckDto> avgRoomFeeRecords = getMemberAvgRoomFeeRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> avgRoomFeeRecordsMap = avgRoomFeeRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> avgRoomFeeRecords2 = getMemberAvgRoomFeeRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> avgRoomFeeRecordsMap2 = avgRoomFeeRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));


        List<BaseCheckDto> registerDaysRecords = getMemberRegisterDaysRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> registerDaysRecordsMap = registerDaysRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> registerDaysRecords2 = getMemberRegisterDaysRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> registerDaysRecordsMap2 = registerDaysRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));


        // 根据 relegationSuccessfulPerformType 分组
        Map<String, List<MemberUpgradeRuleDto>> groupedRules = memberUpgradeRuleDtos.stream()
                .collect(Collectors.groupingBy(item -> String.format("%s-%s", item.getUpgradeSuccessfulPerformType(), item.getCycleType())));
        List<MemberChangeDto> levelChangeDtoList = new ArrayList<>();

        groupedRules.forEach((k, v) -> {
            String performType = k.split("-")[0];
            Integer cycleType = Integer.parseInt(k.split("-")[1]);
            if (performType.equals(SuccessfulPerformTypeEnum.ALL.getType())) {
                if (cycleType.equals(CycleTypeEnum.SINCE_REGISTER)) {
                    levelChangeDtoList.addAll(processAllRules(v, batchMemberNos, startDateLastRegister, startDateLastLevelChange, endDate, pointRecordsMap, consumptionRecordsMap, rechargeRecordsMap, checkoutRecordsMap, stayRecordsMap, unstayRecordsMap, avgRoomFeeRecordsMap, registerDaysRecordsMap));
                } else {
                    levelChangeDtoList.addAll(processAllRules(v, batchMemberNos, startDateLastLevelChange, startDateLastLevelChange, endDate, pointRecordsMap2, consumptionRecordsMap2, rechargeRecordsMap2, checkoutRecordsMap2, stayRecordsMap2, unstayRecordsMap2, avgRoomFeeRecordsMap2, registerDaysRecordsMap2));
                }
            } else if (performType.equals(SuccessfulPerformTypeEnum.ANY.getType())) {
                if (cycleType.equals(CycleTypeEnum.SINCE_REGISTER)) {
                    levelChangeDtoList.addAll(processAnyRules(v, batchMemberNos, startDateLastRegister, startDateLastLevelChange, endDate, pointRecordsMap, consumptionRecordsMap, rechargeRecordsMap, checkoutRecordsMap, stayRecordsMap, unstayRecordsMap, avgRoomFeeRecordsMap, registerDaysRecordsMap));
                } else {
                    levelChangeDtoList.addAll(processAnyRules(v, batchMemberNos, startDateLastLevelChange, startDateLastLevelChange, endDate, pointRecordsMap2, consumptionRecordsMap2, rechargeRecordsMap2, checkoutRecordsMap2, stayRecordsMap2, unstayRecordsMap2, avgRoomFeeRecordsMap2, registerDaysRecordsMap2));
                }
            }
        });

        // 等级变更 降级需要会员当前等级以及cardId 与降级规则保持一致，需要等级变更时再进行判断  TODO
    }

    @Override
    public void process(Integer masterType, String masterCode, String memberNo) {
        // 查询这个会员的所有会员卡
        List<MemberCardInfo> memberCardInfos = memberCardInfoBiz.listByMemberNo(masterType, masterCode, memberNo);
        // 查询每个卡对应的升降级规则
        List<Long> cardIds = memberCardInfos.stream().map(MemberCardInfo::getCardId).collect(Collectors.toList());
        List<MemberCardLevelUpgradeRuleResp> rules = memberCardInfoDecorator.listCardLevelUpgradeRule(cardIds);
        Map<String, MemberCardLevelUpgradeRuleResp> ruleMap = rules.stream().collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getSourceLevel()), Function.identity()));
        // 查询卡等级信息
        List<MemberCardLevelConfigInfoResp> memberCardLevelInfos = memberCardInfoDecorator.listMemberCardLevel(cardIds);
        Map<String, MemberCardLevelConfigInfoResp> memberCardLevelInfoMap = memberCardLevelInfos.stream().collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel()), Function.identity()));
        for (MemberCardInfo memberCardInfo : memberCardInfos) {
            Long cardId = memberCardInfo.getCardId();
            Integer cardLevel = memberCardInfo.getCardLevel();
            MemberCardLevelUpgradeRuleResp rule = ruleMap.get(String.format("%s-%s", cardId, cardLevel));
            MemberUpgradeRuleDto ruleDto = scheduleMedConverter.convertUpgradeRuleDto(rule);
            MemberCardLevelChangeRecord memberCardLevelChangeRecord;
            boolean flag;

            if (ruleDto.getCycleType().equals(CycleTypeEnum.SINCE_REGISTER)) {
                // 获取这个会员的注册日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo, Arrays.asList(ChangeTypeEnum.REGISTER.getType()));

                // 获取记录 TODO
            } else {
                // 获取这个会员的上次升降机日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo, Arrays.asList(ChangeTypeEnum.DOWN_AUTO.getType(), ChangeTypeEnum.UPGRADE_AUTO.getType(), ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(), ChangeTypeEnum.UPGRADE_PURCHASE.getType()));

                // 获取记录 TODO

            }
            LocalDateTime startDate = memberCardLevelChangeRecord.getGmtCreate();
            if (ruleDto.getUpgradeSuccessfulPerformType().equals(SuccessfulPerformTypeEnum.ALL.getType())) {
                flag = checkAllConditions(ruleDto.getDetails(), memberNo, null, null, null, null, null, null, null, null);
            } else {
                flag = checkAnyCondition(ruleDto.getDetails(), memberNo, null, null, null, null, null, null, null, null);
            }
            if (flag) {
                MemberCardLevelConfigInfoResp beforeLevelInfo = memberCardLevelInfoMap.get(String.format("%s-%s", cardId, cardLevel));
                MemberCardLevelConfigInfoResp afterLevelInfo = memberCardLevelInfoMap.get(String.format("%s-%s", cardId, ruleDto.getTargetLevel()));
                // 升级
                UpdateCardLevelDto updateCardLevelDto = new UpdateCardLevelDto();
                updateCardLevelDto.setMemberNo(memberNo)
                        .setCardId(cardId)
                        .setMemberCardNo(memberCardInfo.getMemberCardNo())
                        .setPreLevel(cardLevel)
                        .setPreLevelName(beforeLevelInfo.getCardLevelName())
                        .setAfterLevel(ruleDto.getTargetLevel())
                        .setAfterLevelName(ruleDto.getName())
                        .setEffectBeginDate(LocalDateUtil.formatByNormalDate(LocalDate.now()))
                        .setEffectEndDate(LocalDateUtil.formatByNormalDate(LocalDate.now().minusDays(afterLevelInfo.getValidPeriod())))
                        .setIsLongTerm(afterLevelInfo.getIsLongTerm())
                        .setChangeType(ChangeTypeEnum.UPGRADE_AUTO.getType())
                        .setReason(ruleDto.getDescription())
                        .setOperator(TraceNoUtil.getTraceNo());
                memberCardMedService.updateCardLevel(updateCardLevelDto);
            }
        }


    }

    private List<MemberChangeDto> processAllRules(List<MemberUpgradeRuleDto> rules, List<String> memberNos, LocalDateTime startDateLastRegister, LocalDateTime startDateLastLevelChange, LocalDateTime endDate, Map<String, List<BaseCheckDto>> pointRecords, Map<String, List<BaseCheckDto>> consumptionRecords, Map<String, List<BaseCheckDto>> rechargeRecords, Map<String, List<BaseCheckDto>> checkoutRecords, Map<String, List<BaseCheckDto>> stayRecords, Map<String, List<BaseCheckDto>> unstayRecords, Map<String, List<BaseCheckDto>> averagedRecords, Map<String, List<BaseCheckDto>> registrarRecords) {
        List<MemberChangeDto> levelChangeDtoList = new ArrayList<>();
        for (MemberUpgradeRuleDto rule : rules) {
            List<String> memberNosToProcess = new ArrayList<>();
            for (String memberNo : memberNos) {
                boolean allConditionsMet = checkAllConditions(rule.getDetails(), memberNo, startDateLastRegister, startDateLastLevelChange, endDate,
                        pointRecords.getOrDefault(memberNo, new ArrayList<>()),
                        consumptionRecords.getOrDefault(memberNo, new ArrayList<>()),
                        rechargeRecords.getOrDefault(memberNo, new ArrayList<>()),
                        checkoutRecords.getOrDefault(memberNo, new ArrayList<>()),
                        stayRecords.getOrDefault(memberNo, new ArrayList<>()),
                        unstayRecords.getOrDefault(memberNo, new ArrayList<>()),
                        averagedRecords.getOrDefault(memberNo, new ArrayList<>()),
                        registrarRecords.getOrDefault(memberNo, new ArrayList<>()));
                if (allConditionsMet) {
                    memberNosToProcess.add(memberNo);
                }
            }
            levelChangeDtoList.add(executeRelegationSuccess(rule, memberNosToProcess));
        }
        return levelChangeDtoList;
    }

    private List<MemberChangeDto> processAnyRules(List<MemberUpgradeRuleDto> rules, List<String> memberNos, LocalDateTime startDateLastRegister, LocalDateTime startDateLastLevelChange, LocalDateTime endDate, Map<String, List<BaseCheckDto>> pointRecords, Map<String, List<BaseCheckDto>> consumptionRecords, Map<String, List<BaseCheckDto>> rechargeRecords, Map<String, List<BaseCheckDto>> checkoutRecords, Map<String, List<BaseCheckDto>> stayRecords, Map<String, List<BaseCheckDto>> unstayRecords, Map<String, List<BaseCheckDto>> averagedRecords, Map<String, List<BaseCheckDto>> registrarRecords) {
        List<MemberChangeDto> levelChangeDtoList = new ArrayList<>();
        for (MemberUpgradeRuleDto rule : rules) {
            List<String> memberNosToProcess = new ArrayList<>();
            for (String memberNo : memberNos) {
                boolean anyConditionMet = checkAnyCondition(rule.getDetails(), memberNo, startDateLastRegister, startDateLastLevelChange, endDate,
                        pointRecords.getOrDefault(memberNo, new ArrayList<>()),
                        consumptionRecords.getOrDefault(memberNo, new ArrayList<>()),
                        rechargeRecords.getOrDefault(memberNo, new ArrayList<>()),
                        checkoutRecords.getOrDefault(memberNo, new ArrayList<>()),
                        stayRecords.getOrDefault(memberNo, new ArrayList<>()),
                        unstayRecords.getOrDefault(memberNo, new ArrayList<>()),
                        averagedRecords.getOrDefault(memberNo, new ArrayList<>()),
                        registrarRecords.getOrDefault(memberNo, new ArrayList<>()));
                if (anyConditionMet) {
                    memberNosToProcess.add(memberNo);
                }
            }
            levelChangeDtoList.add(executeRelegationSuccess(rule, memberNosToProcess));
        }
        return levelChangeDtoList;
    }

    private boolean checkAllConditions(List<MemberUpgradeRuleDetailDto> details, String member, LocalDateTime startDateLastRegister, LocalDateTime startDateLastLevelChange, LocalDateTime endDate, List<BaseCheckDto> pointRecords, List<BaseCheckDto> consumptionRecords, List<BaseCheckDto> rechargeRecords, List<BaseCheckDto> checkoutRecords, List<BaseCheckDto> stayRecords, List<BaseCheckDto> unstayRecords, List<BaseCheckDto> averageRecords, List<BaseCheckDto> registerRecords) {
        for (MemberUpgradeRuleDetailDto detail : details) {
            if (!checkCondition(detail, member, startDateLastRegister, startDateLastLevelChange, endDate, pointRecords, consumptionRecords, rechargeRecords, checkoutRecords, stayRecords, unstayRecords, averageRecords, registerRecords)) {
                return false;
            }
        }
        return true;
    }

    private boolean checkAllConditions(List<MemberUpgradeRuleDetailDto> details, String member, List<BaseCheckDto> pointRecords, List<BaseCheckDto> consumptionRecords, List<BaseCheckDto> rechargeRecords, List<BaseCheckDto> checkoutRecords, List<BaseCheckDto> stayRecords, List<BaseCheckDto> unstayRecords, List<BaseCheckDto> averageRecords, List<BaseCheckDto> registerRecords) {
        for (MemberUpgradeRuleDetailDto detail : details) {
            if (!checkCondition(detail, member, pointRecords, consumptionRecords, rechargeRecords, checkoutRecords, stayRecords, unstayRecords, averageRecords, registerRecords)) {
                return false;
            }
        }
        return true;
    }

    private boolean checkAnyCondition(List<MemberUpgradeRuleDetailDto> details, String member, LocalDateTime startDateLastRegister, LocalDateTime startDateLastLevelChange, LocalDateTime endDate, List<BaseCheckDto> pointRecords, List<BaseCheckDto> consumptionRecords, List<BaseCheckDto> rechargeRecords, List<BaseCheckDto> checkoutRecords, List<BaseCheckDto> stayRecords, List<BaseCheckDto> unstayRecords, List<BaseCheckDto> averageRecords, List<BaseCheckDto> registerRecords) {
        for (MemberUpgradeRuleDetailDto detail : details) {
            if (checkCondition(detail, member, startDateLastRegister, startDateLastLevelChange, endDate, pointRecords, consumptionRecords, rechargeRecords, checkoutRecords, stayRecords, unstayRecords, averageRecords, registerRecords)) {
                return true;
            }
        }
        return false;
    }
    private boolean checkAnyCondition(List<MemberUpgradeRuleDetailDto> details, String member, List<BaseCheckDto> pointRecords, List<BaseCheckDto> consumptionRecords, List<BaseCheckDto> rechargeRecords, List<BaseCheckDto> checkoutRecords, List<BaseCheckDto> stayRecords, List<BaseCheckDto> unstayRecords, List<BaseCheckDto> averageRecords, List<BaseCheckDto> registerRecords) {
        for (MemberUpgradeRuleDetailDto detail : details) {
            if (checkCondition(detail, member, pointRecords, consumptionRecords, rechargeRecords, checkoutRecords, stayRecords, unstayRecords, averageRecords, registerRecords)) {
                return true;
            }
        }
        return false;
    }

    private boolean checkCondition(MemberUpgradeRuleDetailDto detail, String memberNo, LocalDateTime startDateLastRegister, LocalDateTime startDateLastLevelChange, LocalDateTime endDate, List<BaseCheckDto> pointRecords, List<BaseCheckDto> consumptionRecords, List<BaseCheckDto> rechargeRecords, List<BaseCheckDto> checkoutRecords, List<BaseCheckDto> stayRecords, List<BaseCheckDto> unstayRecords, List<BaseCheckDto> averageRecords, List<BaseCheckDto> registerRecords) {

        switch (ConditionTypeEnum.getByType(detail.getConditionType())) {
            case POINT:
                return checkPointCondition(detail, memberNo, pointRecords);
            case CONSUME_AMOUNT:
                return checkConsumptionCondition(detail, memberNo, consumptionRecords);
            case RECHARGE_AMOUNT:
                return checkRechargeCondition(detail, memberNo, rechargeRecords);
            case IN_COUNT:
                return checkCheckoutCondition(detail, memberNo, checkoutRecords);
            case IN_NIGHT:
                return checkStayCondition(detail, memberNo, stayRecords);
            case UNCHECKED_DAYS:
                return checkUnstayCondition(detail, memberNo, unstayRecords);
            case AVERAGE_ROOM_FEE:
                return checkAverageRoomFeeCondition(detail, memberNo, averageRecords);
            case REGISTER_DAYS:
                return checkRegisterCondition(detail, memberNo, registerRecords);
            default:
                throw new ServiceException("Unsupported condition type: " + detail.getConditionType(), RespCodeEnum.CODE_500.getCode());
        }
    }

    private boolean checkCondition(MemberUpgradeRuleDetailDto detail, String memberNo, List<BaseCheckDto> pointRecords, List<BaseCheckDto> consumptionRecords, List<BaseCheckDto> rechargeRecords, List<BaseCheckDto> checkoutRecords, List<BaseCheckDto> stayRecords, List<BaseCheckDto> unstayRecords, List<BaseCheckDto> averageRecords, List<BaseCheckDto> registerRecords) {

        switch (ConditionTypeEnum.getByType(detail.getConditionType())) {
            case POINT:
                return checkPointCondition(detail, memberNo, pointRecords);
            case CONSUME_AMOUNT:
                return checkConsumptionCondition(detail, memberNo, consumptionRecords);
            case RECHARGE_AMOUNT:
                return checkRechargeCondition(detail, memberNo, rechargeRecords);
            case IN_COUNT:
                return checkCheckoutCondition(detail, memberNo, checkoutRecords);
            case IN_NIGHT:
                return checkStayCondition(detail, memberNo, stayRecords);
            case UNCHECKED_DAYS:
                return checkUnstayCondition(detail, memberNo, unstayRecords);
            case AVERAGE_ROOM_FEE:
                return checkAverageRoomFeeCondition(detail, memberNo, averageRecords);
            case REGISTER_DAYS:
                return checkRegisterCondition(detail, memberNo, registerRecords);
            default:
                throw new ServiceException("Unsupported condition type: " + detail.getConditionType(), RespCodeEnum.CODE_500.getCode());
        }
    }
    // TODO 获取具体的条件值
    private boolean checkPointCondition(MemberUpgradeRuleDetailDto detail, String member, List<BaseCheckDto> pointRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(pointRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private boolean checkConsumptionCondition(MemberUpgradeRuleDetailDto detail, String member, List<BaseCheckDto> consumptionRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(consumptionRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private boolean checkRechargeCondition(MemberUpgradeRuleDetailDto detail, String member, List<BaseCheckDto> rechargeRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(rechargeRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private boolean checkCheckoutCondition(MemberUpgradeRuleDetailDto detail, String member, List<BaseCheckDto> checkoutRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(checkoutRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private boolean checkStayCondition(MemberUpgradeRuleDetailDto detail, String member, List<BaseCheckDto> stayRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(stayRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private boolean checkUnstayCondition(MemberUpgradeRuleDetailDto detail, String member, List<BaseCheckDto> unstayRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(unstayRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private boolean checkRegisterCondition(MemberUpgradeRuleDetailDto detail, String member, List<BaseCheckDto> stayRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(stayRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    /**
     * 校验平均房费
     */
    private boolean checkAverageRoomFeeCondition(MemberUpgradeRuleDetailDto detail, String member, List<BaseCheckDto> stayRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(stayRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private MemberChangeDto executeRelegationSuccess(MemberUpgradeRuleDto dto, List<String> memberNo) {
        MemberChangeDto levelChangeDto = new MemberChangeDto();
        levelChangeDto.setMemberNo(memberNo);
        levelChangeDto.setSourceLevel(dto.getSourceLevel());
        levelChangeDto.setTargetLevel(dto.getTargetLevel());
        levelChangeDto.setCardId(dto.getCardId());
        levelChangeDto.setType(2);
        return levelChangeDto;
    }

    @Override
    public Integer getAction() {
        return ScheduleHandlerEnum.MEMBER_UPGRADE_RULE.getAction();
    }
}
