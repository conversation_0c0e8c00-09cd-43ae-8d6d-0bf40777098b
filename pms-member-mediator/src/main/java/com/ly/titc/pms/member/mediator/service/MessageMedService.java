package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.pms.member.dubbo.entity.message.MemberEventMsg;

/**
 * 消息通知
 *
 * <AUTHOR>
 * @date 2024/10/31 11:12
 */
public interface MessageMedService {

    /**
     * 会员发送场景验证码
     *
     * @param mobile
     * @return
     */
    String sendSceneSms(Integer masterType, String masterCode, String mobile, String scene);

    /**
     * 验证会员注册验证码
     *
     * @param mobile
     * @param code
     */
    void verifyRegisterSms(String mobile, String code, String scene);

    /**
     * 发送OA消息通知
     *
     * @param trackingId
     * @param title
     * @param context
     */
    void sendOaAlertMsg(String trackingId, String title, String context);

    /**
     * 发送会员事件消息
     *
     * @param trackingId
     * @param memberEventMsg
     */
    void sendMemberEventMsg(String trackingId, MemberEventMsg memberEventMsg);
}
