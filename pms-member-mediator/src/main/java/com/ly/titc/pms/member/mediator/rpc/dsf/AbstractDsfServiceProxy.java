package com.ly.titc.pms.member.mediator.rpc.dsf;

import com.ly.spat.dsf.client.proxy.ProxyFactory;
import org.springframework.beans.factory.annotation.Value;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @classname AbstractDsfServiceProxy
 * @descrition dsf服务代理抽象类
 * @since 2023/12/2 15:33
 */
public abstract class AbstractDsfServiceProxy {

  protected static final String MDM_DSF_GS_NAME = "dsf.master.data";

  protected static final String CHM_DSF_GS_NAME = "dsf.chm";

  protected static final String OAUTH_DSF_GS_NAME = "dsf.oauth";

  protected static final String EHR_DSF_GS_NAME = "dsf.ehr";


  /**
   * mdm dsf 版本
   */
  @Value("${mdm-dsf-service-version}")
  protected String mdmVersion;

  @Value("${ehr-dsf-service-version}")
  protected String ehrVersion;

  /**
   * oauth dsf 版本
   */
  @Value("${oauth-dsf-service-version}")
  protected String oauthVersion;


  @Value("${chm-dsf-service-version}")
  protected String chmVersion;

  /**
   * get proxy
   *
   * @param clz
   * @param gsName
   * @param sname
   * @param version
   * @param <T>
   * @return
   */
  protected static <T> T getProxy(Class<T> clz, String gsName, String sname, String version)  {

    Object obj = ProxyFactory.getProxy(clz, gsName, sname, version,0, 20, TimeUnit.SECONDS,null);
    return (T)obj;
  }

}
