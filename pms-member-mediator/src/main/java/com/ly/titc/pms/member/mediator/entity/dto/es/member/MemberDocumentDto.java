package com.ly.titc.pms.member.mediator.entity.dto.es.member;

import lombok.Data;

import java.util.List;

/**
 * 会员信息
 *
 * <AUTHOR>
 * @date 2024/11/13 11:32
 */
@Data
public class MemberDocumentDto {

    /**
     * 主建ID
     */
    private Long id;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 手机号后四位
     */
    private String mobileTail;

    /**
     * 证件号分类
     */
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 性别 1:男;2:女;3:保密
     */
    private Integer gender;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 会员来源
     */
    private String source;

    /**
     * 注册门店类型 集团:BLOC;门店:HOTEL
     */
    private String registerHotelType;

    /**
     * 注册门店(集团编号; 酒店编号)
     */
    private String registerHotel;

    /**
     * 注册时间
     */
    private Long registerDate;

    /**
     * 销售员
     */
    private String salesman;

    /**
     * 黑名单标记
     */
    private Integer blackFlag;

    /**
     * 会员卡信息
     */
    private List<MemberCardDocumentDto> memberCardInfos;

    /**
     * 会员标签信息
     */
    private List<MemberTagDocumentDto> memberTagInfos;
}
