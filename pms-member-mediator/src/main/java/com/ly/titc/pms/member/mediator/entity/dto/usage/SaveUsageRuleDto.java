package com.ly.titc.pms.member.mediator.entity.dto.usage;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 10:03
 */
@Data
@Accessors(chain = true)
public class SaveUsageRuleDto {
    private Long id;

    /**
     * 主体类型（创建） 1:集团 2:门店 3：艺龙
     */
    private Integer masterType;

    /**
     * 主体类型编码（创建） ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 酒馆组编码（冗余存储）
     */
    private String clubCode;

    /**
     * 集团编码（冗余存储）
     */
    private String blocCode;

    /**
     * 酒店编码（冗余存储）
     */
    private Integer hotelCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;
    /**
     * 适用渠道，逗号隔开 使用渠道 线下酒店：PMS、CRM  微订房：微订房公众号、微订房小程序
     */
    private List<String> scopePlatformChannels;

    /**
     * 配置的适用来源 CLUB(集团组) BLOC (集团)，HOTEL(门店)
     */
    private List<String> scopeSources;

    /**
     * 配置的门店范围 1 全部门店 2 指定门店
     */
    private Integer scopeHotelRange;
    /**
     * 适用酒店codes
     */
    private List<String> scopeHotelCodes;

    /**
     * 是否可用 1可使用 0不可使用
     */
    private Integer isCanUse;
    /**
     * 使用模式 1.指定门店可用，2.仅充值门店可用，3.全部门店可用
     */
    private Integer usageMode;

    /**
     * 指定可用酒店codes
     */
    private List<String> usageHotelCodes;

    /**
     * 使用是否需要密码 1：需要 0 不需要
     */
    private Integer isUsePassword;

}
