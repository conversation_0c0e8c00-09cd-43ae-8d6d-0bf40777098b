package com.ly.titc.pms.member.mediator.rpc.dubbo.account;

import com.google.common.collect.Lists;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.account.dubbo.entity.request.expense.GetExpenseReq;
import com.ly.titc.pms.account.dubbo.entity.request.expense.ListExpenseReq;
import com.ly.titc.pms.account.dubbo.entity.response.ExpenseInfoResp;
import com.ly.titc.pms.account.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.account.dubbo.interfaces.ExpenseDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
 * 消费项
 *
 * <AUTHOR>
 * @date 2024/12/28 16:04
 */
@Slf4j
@Component
public class AccountExpenseDecorator {

    @DubboReference(group = "${account-dsf-dubbo-group}",timeout = 30000)
    private ExpenseDubboService expenseDubboService;


    public List<ExpenseInfoResp> listExpense(ListExpenseReq req){
        return Response.getValidateData(expenseDubboService.listExpense(req));
    }

    public ExpenseInfoResp getExpense(String blocCode,String itemCode){
        GetExpenseReq req = new GetExpenseReq();
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(blocCode);
        req.setItemCode(itemCode);
        req.setTrackingId(UUID.randomUUID().toString());
        Response<ExpenseInfoResp> response = expenseDubboService.getExpense(req);
        return Response.getValidateData(response);
    }

}
