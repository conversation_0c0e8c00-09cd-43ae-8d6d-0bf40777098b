package com.ly.titc.pms.member.mediator.converter;


import com.alibaba.fastjson.JSON;
import com.ly.titc.cashier.dubbo.entity.dto.trade.CashierPaymentInfoDetailDto;
import com.ly.titc.cashier.dubbo.entity.request.CashierPayInfoGetReq;
import com.ly.titc.cashier.dubbo.entity.request.onlinePay.CashierOnlinePayReq;
import com.ly.titc.cashier.dubbo.entity.request.trade.CashierAccountSettlePaymentGetStateReq;
import com.ly.titc.cashier.dubbo.entity.response.CashierPayDetailInfoResp;
import com.ly.titc.cashier.dubbo.entity.response.onlinePay.CashierOnlinePayResp;
import com.ly.titc.cashier.dubbo.entity.response.trade.CashierAccountSettlePaymentResp;
import com.ly.titc.pms.account.dubbo.entity.request.pay.SaveAccountPayTradeReq;
import com.ly.titc.pms.account.dubbo.entity.response.pay.AccountPayTradeResp;
import com.ly.titc.pms.ecrm.dubbo.enums.MemberStoreUsageRuleModeEnum;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.MemberStoreRechargeReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.MemberStoreRechargeRollBackReq;
import com.ly.titc.pms.member.asset.dubbo.enums.RechargeTypeEnum;
import com.ly.titc.pms.member.com.utils.GenerateUtils;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.entity.wrapper.MemberOrderWrapper;
import com.ly.titc.pms.member.mediator.entity.dto.order.*;
import com.ly.titc.pms.member.mediator.entity.dto.recharge.MemberStoreRechargeDto;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.member.GetDiscountBenefitReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 14:43
 */
@Mapper(componentModel = "spring")
public interface MemberOrderMedConverter {


    default <T> MemberOrderWrapper convert(CreateOrderDto<T> dto ){
        MemberOrderWrapper wrapper = new MemberOrderWrapper();
        MemberOrderInfo orderInfo = convertOrder(dto);
        orderInfo.setMemberOrderNo(GenerateUtils.generateOrderNO(dto.getMemberScene()));
        ActivityOrderDto activityOrderDto =   dto.getActivityOrderDto();
        MemberOrderDetailInfo  detailInfo = convertDetail(orderInfo,activityOrderDto);
        detailInfo.setMemberSceneNote(JSON.toJSONString(dto.getMemberSceneNoteDto()));
        wrapper.setOrderInfo(orderInfo);
        wrapper.setDetailInfo(detailInfo);
        return wrapper;
    }

    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator")
    })
    MemberOrderInfo convertOrder(CreateOrderDto dto);

   MemberOrderDetailInfo  convertDetail(MemberOrderInfo orderInfo,ActivityOrderDto activityOrderDto);


    CreateOrderResultDto convert(MemberOrderInfo orderInfo);


    CashierOnlinePayReq convertOnline(MemberPayOrderDto dto);

    @Mappings({
            @Mapping(target = "memberOrderPayNo",source = "businessNo"),
    })
    GetPayStateDto convert(CashierOnlinePayResp resp);
    @Mappings({
            @Mapping(target = "bizPayNo",source = "memberOrderPayNo"),
            @Mapping(target = "sourceSystem",expression = "java(\"MEMBER\")")
    })
    CashierAccountSettlePaymentGetStateReq convertGetDetail(GetPayStateDto payStateDto);

    @Mappings({
            @Mapping(target = "bizPayNo",source = "memberOrderPayNo"),
            @Mapping(target = "sourceSystem",expression = "java(\"MEMBER\")")
    })
    CashierPayInfoGetReq convertBlocDetail(GetPayStateDto payStateDto);


    @Mappings({
            @Mapping(target = "amount",source = "payAmount"),
            @Mapping(target = "tradeNo",source = "onlinePayNo"),
            @Mapping(target = "tradeOperatorNo",source = "onlinePayNo"),
            @Mapping(target = "tradeState",source = "payState"),
    })
    SaveAccountPayTradeReq convertBase(CashierPayDetailInfoResp detailResp);

    @Mappings({
            @Mapping(target = "payState",source = "tradeState"),
    })
    PayOrderResultDto convertResult(AccountPayTradeResp tradeResp);

    MemberOrderPayInfo convert(PayOrderResultDto resultDto);

    MemberOrderPayInfo convert(MemberPayOrderDto dto);

    PayOrderResultDto convertResult(MemberPayOrderDto dto);

    PayOrderResultDto convertPayInfo(MemberOrderPayInfo payInfo);

    default MemberOrderPayInfo convert(PayOrderResultDto resultDto,MemberOrderPayInfo payInfo){
        payInfo.setOrderPayState(resultDto.getPayState());
        payInfo.setPayChannel(resultDto.getPayChannel());
        payInfo.setPayVendor(resultDto.getPayVendor());
        payInfo.setPayType(resultDto.getPayType());
        payInfo.setPayedTime(resultDto.getPayedTime());
        payInfo.setFailReason(resultDto.getFailReason());
        payInfo.setOnlinePayNo(resultDto.getOnlinePayNo());
        payInfo.setTransactionId(resultDto.getTransactionId());
        payInfo.setPayedTime(resultDto.getPayedTime());
        payInfo.setOnlinePayNo(resultDto.getOnlinePayNo());
        return payInfo;
    }

    AccountPayTradeResp convertTradeResp(CashierAccountSettlePaymentResp resp, CashierPaymentInfoDetailDto dto);

    @Mappings({
            @Mapping(target = "tradeState",source = "payState"),
    })
    AccountPayTradeResp convertTradeResp(CashierPayDetailInfoResp detailResp);

    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator")
    })
    MemberOrderRechargeSceneInfo convert(MemberStoreRechargeDto dto);

    default MemberStoreRechargeReq convert(MemberOrderRechargeSceneInfo info, MemberOrderInfo memberOrderInfo, MemberOrderDetailInfo detailInfo){
        MemberStoreRechargeReq req = convertBase(info);
        req.setTradeType(RechargeTypeEnum.RECHARGE.getType());
        req.setTradeNo(info.getMemberOrderNo());
        req.setPlatformChannel(memberOrderInfo.getPlatformChannel());
        req.setActivityCode(detailInfo.getActivityCode());
        req.setUsageRuleMode(MemberStoreUsageRuleModeEnum.SINGLE_STORE.getCode());
        req.setRemark(memberOrderInfo.getMemberSceneDesc());
        return req;
    }

    @Mappings({
            @Mapping(target = "operator",source = "createUser"),
    })
    MemberStoreRechargeReq convertBase(MemberOrderRechargeSceneInfo info);

    @Mappings({
            @Mapping(target = "remark",constant = "退款"),
            @Mapping(target = "trackingId",source = "trackingId"),
    })
    MemberStoreRechargeRollBackReq convert(MemberOrderRefundInfo orderInfo, String trackingId);

    @Mappings({
            @Mapping(target = "cashierScene",source = "memberScene"),
    })
    MemberPayUnifiedOrderDto convert(MemberOrderPayInfo payInfo);
}
