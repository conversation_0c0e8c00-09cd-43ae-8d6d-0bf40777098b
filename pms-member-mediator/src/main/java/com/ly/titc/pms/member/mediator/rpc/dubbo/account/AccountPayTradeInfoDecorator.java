//package com.ly.titc.pms.member.mediator.rpc.dubbo.account;
//
//import com.alibaba.fastjson2.JSON;
//import com.ly.titc.common.entity.Pageable;
//import com.ly.titc.common.entity.Response;
//import com.ly.titc.pms.account.dubbo.entity.request.pay.ListByTradeOperatorNosReq;
//import com.ly.titc.pms.account.dubbo.entity.request.pay.PageTradeReq;
//import com.ly.titc.pms.account.dubbo.entity.request.pay.SaveAccountPayTradeReq;
//import com.ly.titc.pms.account.dubbo.entity.response.pay.AccountPayTradeResp;
//import com.ly.titc.pms.account.dubbo.interfaces.AccountPayTradeInfoDubboService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @classname
// * @descrition
// * @since 2024-10-30 15:36
// */
//@Slf4j
//@Component
//public class AccountPayTradeInfoDecorator {
//    @DubboReference(group = "${account-dsf-dubbo-group}",timeout = 30000)
//    private AccountPayTradeInfoDubboService accountPayTradeInfoDubboService;
//
//
//    /**
//     * 保存支付交易流水
//     * @param req
//     * @return
//     */
//    public AccountPayTradeResp save(SaveAccountPayTradeReq req) {
//        log.info("保存支付交易流水 req: {}", JSON.toJSONString(req));
//        Response<AccountPayTradeResp> response =  accountPayTradeInfoDubboService.save(req);
//        log.info("保存支付交易流水 resp: {}", JSON.toJSONString(response));
//        return Response.getValidateData(response);
//    }
//
//    /**
//     * 根据交易操作号查询支付交易流水
//     */
//    public List<AccountPayTradeResp> listByTradeOperatorNos(ListByTradeOperatorNosReq req) {
//        log.info("根据交易操作号查询支付交易流水 req: {}", JSON.toJSONString(req));
//        Response<List<AccountPayTradeResp>> response = accountPayTradeInfoDubboService.listByTradeOperatorNos(req);
//        log.info("根据交易操作号查询支付交易流水 resp: {}", JSON.toJSONString(response));
//        return Response.getValidateData(response);
//    }
//
//    /**
//     * 分页查询交易流水
//     */
//    public Pageable<AccountPayTradeResp> pageTrade(PageTradeReq req) {
//
//        Response<Pageable<AccountPayTradeResp>> response = accountPayTradeInfoDubboService.pageTrade(req);
//        return Response.getValidateData(response);
//    }
//
//}
