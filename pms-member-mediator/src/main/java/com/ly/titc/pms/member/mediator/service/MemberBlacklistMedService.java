package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.pms.member.mediator.entity.dto.blacklist.*;

import java.util.List;

/**
 * 会员黑名单Med服务
 *
 * <AUTHOR>
 * @date 2024/12/19 11:31
 */
public interface MemberBlacklistMedService {

    /**
     * 拉黑会员
     *
     * @param dto
     * @return 黑名单编号
     */
    String blacklist(BlacklistMemberDto dto);

    /**
     * 取消拉黑会员
     *
     * @param dto
     * @return 黑名单编号
     */
    String cancelBlacklist(CancelBlacklistMemberDto dto);

    /**
     * 分页查询拉黑信息
     *
     * @param dto
     * @return
     */
    List<BlacklistInfoDto> listBlacklist(ListBlacklistParamDto dto);

    /**
     * 校验会员是否被拉黑
     *
     * @param memberNo
     * @return
     */
    boolean checkWhetherBlacklist(String memberNo, String scene, String platformChannel);

    /**
     * 黑名单列表
     *
     * @param memberNos
     * @return
     */
    List<String> listByMemberNos(List<String> memberNos);
}
