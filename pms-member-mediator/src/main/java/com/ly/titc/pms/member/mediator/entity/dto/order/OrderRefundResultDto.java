package com.ly.titc.pms.member.mediator.entity.dto.order;

import com.ly.titc.cashier.dubbo.enums.CashierRefundStateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderRefundResultDto {

    /**
     * 收银台退款交易号
     */
    private String refundPayNo;


    /**
     * OMS退款号 （改动 由omsRefundNo，删除omsPayNo）
     */
    private String refundTradeNo;

    /**
     * 渠道交易号
     */
    private String transactionId;

    /**
     * 渠道交易号
     */
    private String refundTransactionId;

    /**
     * 退款状态 1 退款中、2 退款成功、3 退款失败
     * @see CashierRefundStateEnum
     */
    private Integer refundState;

    /**
     * 备注
     */
    private String failReason;
}
