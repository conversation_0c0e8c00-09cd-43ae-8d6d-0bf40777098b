package com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.QueryMemberCardLevelReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.ecrm.dubbo.interfaces.MemberCardLevelDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberCardLevelDecorator
 * @Date：2024-12-4 14:13
 * @Filename：MemberCardLevelDecorator
 */
@Slf4j
@Component
public class MemberCardLevelDecorator {

    @DubboReference(group = "${ecrm-dsf-group}", check = false)
    private MemberCardLevelDubboService memberCardLevelDubboService;

    public List<MemberCardLevelConfigInfoResp> queryMemberCardLevel(Integer masterType, String masterCode, String name) {
        QueryMemberCardLevelReq req = new QueryMemberCardLevelReq();
        req.setMasterType(masterType);
        req.setMasterCode(masterCode);
        req.setName(name);
        req.setTrackingId(TraceNoUtil.getTraceNo());
        return Response.getValidateData(memberCardLevelDubboService.queryMemberCardLevel(req));
    }
}
