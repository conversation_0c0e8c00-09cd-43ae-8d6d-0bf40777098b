package com.ly.titc.pms.member.mediator.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-3 19:25
 */
@Data
@Accessors(chain = true)
public class BaseDto {

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 追踪ID，后端自动生成
     */
    private String trackingId;
}
