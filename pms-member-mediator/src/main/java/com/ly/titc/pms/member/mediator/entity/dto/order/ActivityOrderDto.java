package com.ly.titc.pms.member.mediator.entity.dto.order;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 14:26
 */
@Data
@Accessors(chain = true)
public class ActivityOrderDto {

    /**
     * 活动类型
     */
    private String eventType;
    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动成本
     */
    private String activityCost;

    /**
     * 礼包信息JSON
     */
    private String giftPack;

    /**
     * 金额类型
     * todo 看活动的定义
     */
    private String amountType;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 档位
     */
    private String gearCode;

}
