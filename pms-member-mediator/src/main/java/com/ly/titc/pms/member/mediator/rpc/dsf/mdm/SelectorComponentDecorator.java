package com.ly.titc.pms.member.mediator.rpc.dsf.mdm;

import com.ly.titc.common.entity.CodeObject;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.mdm.api.HotelService;
import com.ly.titc.mdm.api.SelectorComponentDsfService;
import com.ly.titc.mdm.entity.request.selector.ListSelectorComponentInfoReq;
import com.ly.titc.mdm.entity.request.selector.SaveSelectorComponentReq;
import com.ly.titc.mdm.entity.request.selector.SelectorComponentInfoReq;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.selector.SelectorComponentInfoResp;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.mediator.entity.dto.usage.ScopeHotelsDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-1-8 19:25
 */
@Slf4j
@Component
public class SelectorComponentDecorator {
    @DubboReference(protocol = "dsf", providedBy = "${mdm-dsf-group}", version = "${mdm-dsf-service-version}")
    private SelectorComponentDsfService componentDsfService;


    /**
     * 过滤酒店
     */
    public List<HotelBaseInfoResp> filterHotel(List<HotelBaseInfoResp> respList, ScopeHotelsDto scopeHotelDto) {
        if(scopeHotelDto ==null){
            throw new ServiceException("指定酒店的适用范围不能为空", RespCodeEnum.CODE_400.getCode());
        }
        //过滤满足条件的酒店
        List<HotelBaseInfoResp> resps = new ArrayList<>();
        respList.forEach(hotelBaseInfoResp -> {
            if(!CollectionUtils.isEmpty(scopeHotelDto.getHotelCodes())){
                if(scopeHotelDto.getHotelCodes().contains(hotelBaseInfoResp.getHotelCode())){
                    resps.add(hotelBaseInfoResp);
                }
            }
            if(!CollectionUtils.isEmpty(scopeHotelDto.getClubLabels())){
                List<CodeObject> clubLabels = scopeHotelDto.getClubLabels();
                List<String> blocCodes = clubLabels.stream().map(CodeObject::getCode).collect(Collectors.toList());
                if(blocCodes.contains(hotelBaseInfoResp.getBlocCode())){
                    resps.add(hotelBaseInfoResp);
                }
            }
            if(!CollectionUtils.isEmpty(scopeHotelDto.getBrandLabels())){
                List<CodeObject> brandLabels = scopeHotelDto.getBrandLabels();
                List<String> brandCodes = brandLabels.stream().map(CodeObject::getCode).collect(Collectors.toList());
                if(brandCodes.contains(hotelBaseInfoResp.getBrandCode())){
                    resps.add(hotelBaseInfoResp);
                }
            }
            //省市区
            if(!CollectionUtils.isEmpty(scopeHotelDto.getRegionLabels())){
                List<CodeObject> regionLabels = scopeHotelDto.getRegionLabels();
                List<String> regionCodes = regionLabels.stream().map(CodeObject::getCode).collect(Collectors.toList());
                if(hotelBaseInfoResp.getProvinceId() !=null){
                    if(regionCodes.contains(hotelBaseInfoResp.getProvinceId().toString())){
                        resps.add(hotelBaseInfoResp);
                    }
                }
                if(hotelBaseInfoResp.getCityId() !=null){
                    if(regionCodes.contains(hotelBaseInfoResp.getCityId().toString())){
                        resps.add(hotelBaseInfoResp);
                    }
                }
                if(hotelBaseInfoResp.getDistrictId()!=null){
                    if(regionCodes.contains(hotelBaseInfoResp.getDistrictId().toString())){
                        resps.add(hotelBaseInfoResp);
                    }
                }
            }
            //区域
            if(!CollectionUtils.isEmpty(scopeHotelDto.getAreaLabels())){
                List<CodeObject> areaLabels = scopeHotelDto.getAreaLabels();
                List<String> areaCodes = areaLabels.stream().map(CodeObject::getCode).collect(Collectors.toList());
                if(areaCodes.contains(hotelBaseInfoResp.getAreaId().toString())){
                    resps.add(hotelBaseInfoResp);
                }
            }
        });
        return resps.stream().distinct().collect(Collectors.toList());
    }


    /**
     * 保存门店组件选择器
     */
    public Boolean saveSelector(SaveSelectorComponentReq req){
        Response<Boolean> response = componentDsfService.saveSelector(req);
        return Response.getValidateData(response);
    }

    /**
     * 查询门店组件选择器
     */
    public List<SelectorComponentInfoResp> listSelector(ListSelectorComponentInfoReq req){
        Response<List<SelectorComponentInfoResp>> response = componentDsfService.listSelector(req);
        return Response.getValidateData(response);
    }

    /**
     * 删除门店组件选择器
     */
    public Boolean deleteSelector(SelectorComponentInfoReq req) {
        Response<Boolean> response =  componentDsfService.deleteSelector(req);
        return Response.getValidateData(response);
    }


}
