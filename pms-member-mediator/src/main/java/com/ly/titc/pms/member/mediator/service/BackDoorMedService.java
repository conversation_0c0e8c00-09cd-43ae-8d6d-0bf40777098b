package com.ly.titc.pms.member.mediator.service;

/**
 * 后门
 *
 * <AUTHOR>
 * @date 2024/11/27 10:30
 */
public interface BackDoorMedService {

    /**
     * update
     *
     * @param sql
     */
    void doUpdateSomething(String sql);

    /**
     * insert
     *
     * @param sql
     */
    void doInsertSomething(String sql);

    /**
     * delete
     *
     * @param sql
     */
    void doDeleteSomething(String sql);

}
