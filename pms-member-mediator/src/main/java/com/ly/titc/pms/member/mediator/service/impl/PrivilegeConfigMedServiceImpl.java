package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.util.IdUtil;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.pms.member.biz.CardLevelPrivilegeConfigBiz;
import com.ly.titc.pms.member.biz.PrivilegeConfigBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.entity.po.PrivilegeApplicableDataMapping;
import com.ly.titc.pms.member.dal.entity.po.PrivilegeConfigInfo;
import com.ly.titc.pms.member.mediator.converter.PrivilegeConfigMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.SavePrivilegeApplicableDataMappingDto;
import com.ly.titc.pms.member.mediator.entity.dto.privilege.SavePrivilegeConfigDto;
import com.ly.titc.pms.member.mediator.service.PrivilegeConfigMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权益配置服务实现类
 *
 * <AUTHOR>
 * @date 2025/6/25 18:15
 */
@Slf4j
@Service
public class PrivilegeConfigMedServiceImpl implements PrivilegeConfigMedService {

    @Resource
    private PrivilegeConfigBiz privilegeConfigBiz;

    @Resource
    private PrivilegeConfigMedConverter privilegeConfigMedConverter;

    @Resource
    private CardLevelPrivilegeConfigBiz cardLevelPrivilegeConfigBiz;

    @Resource
    private RedisFactory redisFactory;

    @Override
    public Long savePrivilegeConfig(SavePrivilegeConfigDto dto) {
        String idempotentKey = CommonUtil.concat(CommonConstant.PRIVILEGE_SAVE_IDEMPOTENT_PREFIX, dto.getMasterType(), dto.getMasterCode(), dto.getName());
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this privilege config save is processing...masterType:{},masterCode:{},name:{}", dto.getMasterType(), dto.getMasterCode(), dto.getName());
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            // 重复校验
            checkRepeatData(dto);
            // 名称重复校验
            PrivilegeConfigInfo existPrivilegeConfig = privilegeConfigBiz.getByName(dto.getMasterType(), dto.getMasterCode(), dto.getName());
            if (existPrivilegeConfig != null && !existPrivilegeConfig.getId().equals(dto.getId())) {
                throw new ServiceException(RespCodeEnum.CONFIG_20008);
            }
            // 数据组装
            PrivilegeConfigInfo privilegeConfigInfo = privilegeConfigMedConverter.convertDtoToPo(dto);
            if (dto.getId() == null) {
                privilegeConfigInfo.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
                List<PrivilegeApplicableDataMapping> mappings = dto.getApplicableDataMapping().stream()
                        .map(mapping -> {
                            PrivilegeApplicableDataMapping applicableDataMapping = privilegeConfigMedConverter.convertDtoToPo(mapping, privilegeConfigInfo.getId(), dto.getOperator());
                            applicableDataMapping.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
                            return applicableDataMapping;
                        }).collect(Collectors.toList());
                privilegeConfigBiz.insert(privilegeConfigInfo, mappings);
            } else {
                List<PrivilegeApplicableDataMapping> mappings = dto.getApplicableDataMapping().stream()
                        .map(mapping -> {
                            PrivilegeApplicableDataMapping applicableDataMapping = privilegeConfigMedConverter.convertDtoToPo(mapping, privilegeConfigInfo.getId(), dto.getOperator());
                            applicableDataMapping.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
                            return applicableDataMapping;
                        }).collect(Collectors.toList());
                privilegeConfigBiz.update(privilegeConfigInfo, mappings);
            }
            return privilegeConfigInfo.getId();
        } finally {
            redisFactory.del(idempotentKey);
        }

    }



    @Override
    public void deletePrivilegeConfig(Long privilegeId, String operator) {
        if (cardLevelPrivilegeConfigBiz.checkHasPrivilege(privilegeId)) {
            throw new ServiceException(RespCodeEnum.CONFIG_20009);
        }
        privilegeConfigBiz.deleteByPrivilegeId(privilegeId, operator);
    }

    @Override
    public Long actionPrivilegeConfig(Long privilegeId, Integer state, String operator) {
        PrivilegeConfigInfo privilegeConfigInfo = privilegeConfigBiz.getById(privilegeId);
        if (privilegeConfigInfo == null) {
            throw new ServiceException(RespCodeEnum.CONFIG_20010);
        }
        privilegeConfigBiz.updateState(privilegeId, state, operator);
        return privilegeId;
    }
    /**
     * 重复校验，scopeType 不能同时存在0 和 1 的，为 1的 时候，mappingDtoList只能有1挑，为0的时候，scopeValue不能重复
     *
     * @param dto
     */
    private static void checkRepeatData(SavePrivilegeConfigDto dto) {
        Map<Integer, List<SavePrivilegeApplicableDataMappingDto>> map = dto.getApplicableDataMapping().stream().collect(Collectors.groupingBy(SavePrivilegeApplicableDataMappingDto::getScopeType));
        if (map.keySet().size() > 1) {
            if (map.containsKey(1)) {
                throw new ServiceException(RespCodeEnum.CONFIG_20006);
            } else {
                Set<String> set = new HashSet<>();
                map.forEach((key, list) -> {
                    for (SavePrivilegeApplicableDataMappingDto memberPrivilegeApplicableDataMappingDto : list) {
                        if (set.contains(memberPrivilegeApplicableDataMappingDto.getScopeValue())) {
                            throw new ServiceException(RespCodeEnum.CONFIG_20007);
                        } else {
                            set.add(memberPrivilegeApplicableDataMappingDto.getScopeValue());
                        }
                    }
                });
            }
        } else if (map.keySet().size() == 1) {
            Integer key = map.keySet().stream().findFirst().get();
            List<SavePrivilegeApplicableDataMappingDto> dtoList = map.get(key);
            if (key == 1 && dtoList.size() > 1) {
                throw new ServiceException(RespCodeEnum.CONFIG_20006);
            }
            if (key == 0) {
                Set<String> set = new HashSet<>();
                dtoList.forEach(item -> {
                    if (set.contains(item.getScopeValue())) {
                        throw new ServiceException(RespCodeEnum.CONFIG_20007);
                    } else {
                        set.add(item.getScopeValue());
                    }
                });
            }
        }
    }
}
