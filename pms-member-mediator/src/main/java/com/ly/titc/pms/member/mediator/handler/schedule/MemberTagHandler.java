package com.ly.titc.pms.member.mediator.handler.schedule;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberTagConfigInfoResp;
import com.ly.titc.pms.member.biz.MemberProfileTagInfoBiz;
import com.ly.titc.pms.member.com.enums.*;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileTagInfo;
import com.ly.titc.pms.member.mediator.converter.ScheduleMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.profile.AddMemberProfileTagDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.*;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.service.MemberProfileMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberTagHandler
 * @Date：2024-11-21 14:25
 * @Filename：MemberTagHandler
 */
@Slf4j
@Component
public class MemberTagHandler extends AbstractScheduleHandler<MemberTagDto> {

    @Resource
    private MemberCardInfoDecorator memberCardInfoDecorator;

    @Resource
    private ScheduleMedConverter scheduleMedConverter;

    @Resource
    private MemberProfileTagInfoBiz memberProfileTagInfoBiz;

    @Resource
    private MemberProfileMedService memberProfileMedService;

    @Override
    public List<MemberTagDto> getData() {
        List<MemberTagConfigInfoResp> memberTagConfigInfoRespList = memberCardInfoDecorator.listAllTags();
        return scheduleMedConverter.convertTagDto(memberTagConfigInfoRespList);
    }

    @Override
    public void doScheduleMain(Integer masterType, String masterCode, List<MemberTagDto> memberTagDtos, List<String> batchMemberNos, List<MemberCardLevelChangeRecord> records) {
        // 获取每个批次的起始日期
        LocalDateTime startDateLastRegister = records.stream().filter(item -> item.getChangeType().equals(ChangeTypeEnum.REGISTER.getType())).findFirst().orElse(new MemberCardLevelChangeRecord()).getGmtCreate();
        LocalDateTime startDateLastLevelChange = records.stream().filter(item -> item.getChangeType().equals(ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType()) ||
                item.getChangeType().equals(ChangeTypeEnum.UPGRADE_AUTO.getType()) || item.getChangeType().equals(ChangeTypeEnum.UPGRADE_PURCHASE.getType())
                || item.getChangeType().equals(ChangeTypeEnum.DOWN_AUTO.getType())).findFirst().orElse(new MemberCardLevelChangeRecord()).getGmtCreate();
        LocalDateTime endDate = LocalDateTime.now();

        // 预查询所有校验需要的数据
        List<BaseCheckDto> pointRecords = getMemberPointRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        List<BaseCheckDto> pointRecords2 = getMemberPointRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> pointRecordsMap = pointRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        Map<String, List<BaseCheckDto>> pointRecordsMap2 = pointRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));


        List<BaseCheckDto> consumptionRecords = getMemberConsumptionRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> consumptionRecordsMap = consumptionRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> consumptionRecords2 = getMemberConsumptionRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> consumptionRecordsMap2 = consumptionRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));

        List<BaseCheckDto> rechargeRecords = getMemberRechargeRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> rechargeRecordsMap = rechargeRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> rechargeRecords2 = getMemberRechargeRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> rechargeRecordsMap2 = rechargeRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));


        List<BaseCheckDto> checkoutRecords = getMemberCheckoutRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> checkoutRecordsMap = checkoutRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> checkoutRecords2 = getMemberCheckoutRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> checkoutRecordsMap2 = checkoutRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));

        List<BaseCheckDto> stayRecords = getMemberStayRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> stayRecordsMap = stayRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> stayRecords2 = getMemberUnstayRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> stayRecordsMap2 = stayRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));


        List<BaseCheckDto> unstayRecords = getMemberUnstayRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> unstayRecordsMap = unstayRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> unstayRecords2 = getMemberUnstayRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> unstayRecordsMap2 = unstayRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));

        List<BaseCheckDto> avgRoomFeeRecords = getMemberAvgRoomFeeRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> avgRoomFeeRecordsMap = avgRoomFeeRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> avgRoomFeeRecords2 = getMemberAvgRoomFeeRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> avgRoomFeeRecordsMap2 = avgRoomFeeRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));


        List<BaseCheckDto> registerDaysRecords = getMemberRegisterDaysRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastRegister, endDate);
        Map<String, List<BaseCheckDto>> registerDaysRecordsMap = registerDaysRecords.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));
        List<BaseCheckDto> registerDaysRecords2 = getMemberRegisterDaysRecordByMemberNo(masterType, masterCode, batchMemberNos, startDateLastLevelChange, endDate);
        Map<String, List<BaseCheckDto>> registerDaysRecordsMap2 = registerDaysRecords2.stream().collect(Collectors.groupingBy(BaseCheckDto::getMemberNo));


        // 根据 satisfyPerformType  和 cycleType 分组
        Map<String, List<MemberTagDto>> groupedRules2 = memberTagDtos.stream().collect(Collectors.groupingBy(item ->
                String.format("%s-%s", item.getSatisfyPerformType(), item.getCycleType())));

        List<MemberChangeDto> levelChangeDtoList = new ArrayList<>();

        groupedRules2.forEach((k, v) -> {
            String performType = k.split("-")[0];
            Integer cycleType = Integer.parseInt(k.split("-")[1]);
            if (performType.equals(SuccessfulPerformTypeEnum.ALL.getType())) {
                if (cycleType.equals(CycleTypeEnum.SINCE_REGISTER)) {
                    levelChangeDtoList.addAll(processAllRules(v, batchMemberNos, startDateLastRegister, startDateLastLevelChange, endDate, pointRecordsMap, consumptionRecordsMap, rechargeRecordsMap, checkoutRecordsMap, stayRecordsMap, unstayRecordsMap, avgRoomFeeRecordsMap, registerDaysRecordsMap));
                } else {
                    levelChangeDtoList.addAll(processAllRules(v, batchMemberNos, startDateLastLevelChange, startDateLastLevelChange, endDate, pointRecordsMap2, consumptionRecordsMap2, rechargeRecordsMap2, checkoutRecordsMap2, stayRecordsMap2, unstayRecordsMap2, avgRoomFeeRecordsMap2, registerDaysRecordsMap2));
                }
            } else if (performType.equals(SuccessfulPerformTypeEnum.ANY.getType())) {
                if (cycleType.equals(CycleTypeEnum.SINCE_REGISTER)) {
                    levelChangeDtoList.addAll(processAnyRules(v, batchMemberNos, startDateLastRegister, startDateLastLevelChange, endDate, pointRecordsMap, consumptionRecordsMap, rechargeRecordsMap, checkoutRecordsMap, stayRecordsMap, unstayRecordsMap, avgRoomFeeRecordsMap, registerDaysRecordsMap));
                } else {
                    levelChangeDtoList.addAll(processAnyRules(v, batchMemberNos, startDateLastLevelChange, startDateLastLevelChange, endDate, pointRecordsMap2, consumptionRecordsMap2, rechargeRecordsMap2, checkoutRecordsMap2, stayRecordsMap2, unstayRecordsMap2, avgRoomFeeRecordsMap2, registerDaysRecordsMap2));
                }
            }
        });

        // levelChangeDtoList 根据type分组，处理删除和新增标签的会员
        Map<Integer, List<MemberChangeDto>> groupedLevelChangeDtoList = levelChangeDtoList.stream().collect(Collectors.groupingBy(MemberChangeDto::getType));
        groupedLevelChangeDtoList.forEach((k, v) -> {
            if (k.equals(3)) {
                // 批量删除会员标签 TODO
            } else {
                // 批量新增会员标签 TODO
            }
        });

    }

    @Override
    public void process(Integer masterType, String masterCode, String memberNo) {
        // 先查询这个会员所有的标签
        List<MemberProfileTagInfo> memberProfileTagInfos = memberProfileTagInfoBiz.listMemberTag(memberNo);
        if (CollectionUtils.isEmpty(memberProfileTagInfos)) {
            return;
        }
        List<Long> cardId = memberProfileTagInfos.stream().map(MemberProfileTagInfo::getTagId)
                .collect(Collectors.toList());
        List<MemberTagConfigInfoResp> memberTagConfigInfos = memberCardInfoDecorator.listTagConfig(masterType, masterCode, cardId);
        List<MemberTagDto> memberTagDtos = scheduleMedConverter.convertTagDto(memberTagConfigInfos);
        for (MemberTagDto ruleDto : memberTagDtos) {
            Boolean flag;
            MemberCardLevelChangeRecord memberCardLevelChangeRecord;
            if (ruleDto.getCycleType().equals(CycleTypeEnum.SINCE_REGISTER)) {
                // 获取这个会员的注册日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo, Arrays.asList(ChangeTypeEnum.REGISTER.getType()));
                // 获取记录 TODO
            } else {
                // 获取这个会员的上次升降机日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo, Arrays.asList(ChangeTypeEnum.DOWN_AUTO.getType(), ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(), ChangeTypeEnum.UPGRADE_PURCHASE.getType(), ChangeTypeEnum.UPGRADE_AUTO.getType()));
                // 获取记录 TODO
            }
            LocalDateTime startDate = memberCardLevelChangeRecord.getGmtCreate();
            if (ruleDto.getSatisfyPerformType().equals(SuccessfulPerformTypeEnum.ALL.getType())) {
                flag = checkAllConditions(ruleDto.getMarkRuleList(), memberNo, null, null, null, null, null, null, null, null);
            } else {
                flag = checkAnyCondition(ruleDto.getMarkRuleList(), memberNo, null, null, null, null, null, null, null, null);
            }
            if (flag) {
                // 打标
                AddMemberProfileTagDto addMemberProfileTagDto = new AddMemberProfileTagDto();
                addMemberProfileTagDto.setMasterType(ruleDto.getMasterType());
                addMemberProfileTagDto.setMasterCode(ruleDto.getMasterCode());
                addMemberProfileTagDto.setTagId(ruleDto.getId());
                addMemberProfileTagDto.setTagNo(ruleDto.getId());
                addMemberProfileTagDto.setTagName(ruleDto.getName());
                addMemberProfileTagDto.setMarkType(2);
                addMemberProfileTagDto.setCreateUser("schedule");
                addMemberProfileTagDto.setTagType(ruleDto.getType());
                addMemberProfileTagDto.setMemberNo(memberNo);
                memberProfileMedService.addMemberTag(addMemberProfileTagDto);
            } else {
                if (ruleDto.getAutoDelete().equals(1)) {
                    // 不满足条件，删除
                    memberProfileMedService.deleteMemberTag(ruleDto.getMasterType(), ruleDto.getMasterCode(), memberNo, ruleDto.getId(), "schedule");
                }
            }
        }
    }

    private List<MemberChangeDto> processAllRules(List<MemberTagDto> rules, List<String> memberNos, LocalDateTime startDateLastRegister, LocalDateTime startDateLastLevelChange, LocalDateTime endDate, Map<String, List<BaseCheckDto>> pointRecords, Map<String, List<BaseCheckDto>> consumptionRecords, Map<String, List<BaseCheckDto>> rechargeRecords, Map<String, List<BaseCheckDto>> checkoutRecords, Map<String, List<BaseCheckDto>> stayRecords, Map<String, List<BaseCheckDto>> unstayRecords, Map<String, List<BaseCheckDto>> averagedRecords, Map<String, List<BaseCheckDto>> registrarRecords) {
        List<MemberChangeDto> levelChangeDtoList = new ArrayList<>();
        for (MemberTagDto rule : rules) {
            List<String> memberAddTagList = new ArrayList<>();
            List<String> memberDeleteTagList = new ArrayList<>();
            for (String memberNo : memberNos) {
                boolean allConditionsMet = checkAllConditions(rule.getMarkRuleList(), memberNo,
                        pointRecords.getOrDefault(memberNo, new ArrayList<>()),
                        consumptionRecords.getOrDefault(memberNo, new ArrayList<>()),
                        rechargeRecords.getOrDefault(memberNo, new ArrayList<>()),
                        checkoutRecords.getOrDefault(memberNo, new ArrayList<>()),
                        stayRecords.getOrDefault(memberNo, new ArrayList<>()),
                        unstayRecords.getOrDefault(memberNo, new ArrayList<>()),
                        averagedRecords.getOrDefault(memberNo, new ArrayList<>()),
                        registrarRecords.getOrDefault(memberNo, new ArrayList<>()));
                if (allConditionsMet) {
                    memberAddTagList.add(memberNo);
                } else {
                    if (rule.getAutoDelete().equals(1)) {
                        memberDeleteTagList.add(memberNo);
                    }
                    memberDeleteTagList.add(memberNo);
                }
            }
            levelChangeDtoList.add(executeRelegationSuccess(rule, memberAddTagList, 4));
            levelChangeDtoList.add(executeRelegationSuccess(rule, memberDeleteTagList, 3));
        }
        return levelChangeDtoList;
    }

    private List<MemberChangeDto> processAnyRules(List<MemberTagDto> rules, List<String> memberNos, LocalDateTime startDateLastRegister, LocalDateTime startDateLastLevelChange, LocalDateTime endDate, Map<String, List<BaseCheckDto>> pointRecords, Map<String, List<BaseCheckDto>> consumptionRecords, Map<String, List<BaseCheckDto>> rechargeRecords, Map<String, List<BaseCheckDto>> checkoutRecords, Map<String, List<BaseCheckDto>> stayRecords, Map<String, List<BaseCheckDto>> unstayRecords, Map<String, List<BaseCheckDto>> averagedRecords, Map<String, List<BaseCheckDto>> registrarRecords) {
        List<MemberChangeDto> levelChangeDtoList = new ArrayList<>();
        for (MemberTagDto rule : rules) {
            List<String> memberAddTagList = new ArrayList<>();
            List<String> memberDeleteTagList = new ArrayList<>();
            for (String memberNo : memberNos) {
                boolean anyConditionMet = checkAnyCondition(rule.getMarkRuleList(), memberNo,
                        pointRecords.getOrDefault(memberNo, new ArrayList<>()),
                        consumptionRecords.getOrDefault(memberNo, new ArrayList<>()),
                        rechargeRecords.getOrDefault(memberNo, new ArrayList<>()),
                        checkoutRecords.getOrDefault(memberNo, new ArrayList<>()),
                        stayRecords.getOrDefault(memberNo, new ArrayList<>()),
                        unstayRecords.getOrDefault(memberNo, new ArrayList<>()),
                        averagedRecords.getOrDefault(memberNo, new ArrayList<>()),
                        registrarRecords.getOrDefault(memberNo, new ArrayList<>()));
                if (anyConditionMet) {
                    memberAddTagList.add(memberNo);
                } else {
                    if (rule.getAutoDelete().equals(1)) {
                        memberDeleteTagList.add(memberNo);
                    }
                    memberDeleteTagList.add(memberNo);
                }
            }
            levelChangeDtoList.add(executeRelegationSuccess(rule, memberAddTagList, 4));
            levelChangeDtoList.add(executeRelegationSuccess(rule, memberDeleteTagList, 3));
        }
        return levelChangeDtoList;
    }

    private boolean checkAllConditions(List<MemberTagDetailDto> details, String member, List<BaseCheckDto> pointRecords, List<BaseCheckDto> consumptionRecords, List<BaseCheckDto> rechargeRecords, List<BaseCheckDto> checkoutRecords, List<BaseCheckDto> stayRecords, List<BaseCheckDto> unstayRecords, List<BaseCheckDto> averageRecords, List<BaseCheckDto> registerRecords) {
        for (MemberTagDetailDto detail : details) {
            if (!checkCondition(detail, member, pointRecords, consumptionRecords, rechargeRecords, checkoutRecords, stayRecords, unstayRecords, averageRecords, registerRecords)) {
                return false;
            }
        }
        return true;
    }

    private boolean checkAnyCondition(List<MemberTagDetailDto> details, String member, List<BaseCheckDto> pointRecords, List<BaseCheckDto> consumptionRecords, List<BaseCheckDto> rechargeRecords, List<BaseCheckDto> checkoutRecords, List<BaseCheckDto> stayRecords, List<BaseCheckDto> unstayRecords, List<BaseCheckDto> averageRecords, List<BaseCheckDto> registerRecords) {
        for (MemberTagDetailDto detail : details) {
            if (checkCondition(detail, member, pointRecords, consumptionRecords, rechargeRecords, checkoutRecords, stayRecords, unstayRecords, averageRecords, registerRecords)) {
                return true;
            }
        }
        return false;
    }

    private boolean checkCondition(MemberTagDetailDto detail, String memberNo, List<BaseCheckDto> pointRecords, List<BaseCheckDto> consumptionRecords, List<BaseCheckDto> rechargeRecords, List<BaseCheckDto> checkoutRecords, List<BaseCheckDto> stayRecords, List<BaseCheckDto> unstayRecords, List<BaseCheckDto> averageRecords, List<BaseCheckDto> registerRecords) {

        switch (ConditionTypeEnum.getByType(detail.getConditionType())) {
            case POINT:
                return checkPointCondition(detail, memberNo, pointRecords);
            case CONSUME_AMOUNT:
                return checkConsumptionCondition(detail, memberNo, consumptionRecords);
            case RECHARGE_AMOUNT:
                return checkRechargeCondition(detail, memberNo, rechargeRecords);
            case IN_COUNT:
                return checkCheckoutCondition(detail, memberNo, checkoutRecords);
            case IN_NIGHT:
                return checkStayCondition(detail, memberNo, stayRecords);
            case UNCHECKED_DAYS:
                return checkUnstayCondition(detail, memberNo, unstayRecords);
            case AVERAGE_ROOM_FEE:
                return checkAverageRoomFeeCondition(detail, memberNo, averageRecords);
            case REGISTER_DAYS:
                return checkRegisterCondition(detail, memberNo, registerRecords);
            default:
                throw new ServiceException("Unsupported condition type: " + detail.getConditionType(), RespCodeEnum.CODE_500.getCode());
        }
    }

    // TODO 获取具体的条件值
    private boolean checkPointCondition(MemberTagDetailDto detail, String member, List<BaseCheckDto> pointRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(pointRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private boolean checkConsumptionCondition(MemberTagDetailDto detail, String member, List<BaseCheckDto> consumptionRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(consumptionRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private boolean checkRechargeCondition(MemberTagDetailDto detail, String member, List<BaseCheckDto> rechargeRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(rechargeRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private boolean checkCheckoutCondition(MemberTagDetailDto detail, String member, List<BaseCheckDto> checkoutRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(checkoutRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private boolean checkStayCondition(MemberTagDetailDto detail, String member, List<BaseCheckDto> stayRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(stayRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private boolean checkUnstayCondition(MemberTagDetailDto detail, String member, List<BaseCheckDto> unstayRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(unstayRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private boolean checkRegisterCondition(MemberTagDetailDto detail, String member, List<BaseCheckDto> stayRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(stayRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    /**
     * 校验平均房费
     */
    private boolean checkAverageRoomFeeCondition(MemberTagDetailDto detail, String member, List<BaseCheckDto> stayRecords) {
        String value = "0";
        if (!CollectionUtils.isEmpty(stayRecords)) {
            value = "0";
        }
        return !calculateItem(detail.getCalculateType(), detail.getConditionValue(), value);
    }

    private MemberChangeDto executeRelegationSuccess(MemberTagDto dto, List<String> memberNo, Integer type) {
        MemberChangeDto levelChangeDto = new MemberChangeDto();
        levelChangeDto.setMemberNo(memberNo);
        levelChangeDto.setTagId(dto.getId());
        levelChangeDto.setType(type);
        return levelChangeDto;
    }


    @Override
    public Integer getAction() {
        return ScheduleHandlerEnum.MEMBER_TAG.getAction();
    }
}
