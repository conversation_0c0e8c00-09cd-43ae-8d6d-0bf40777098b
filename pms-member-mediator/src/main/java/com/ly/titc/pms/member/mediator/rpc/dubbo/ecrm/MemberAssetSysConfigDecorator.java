package com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberAssetSysConfigGetReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberAssetSysConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MemberAssetSysConfigResp;
import com.ly.titc.pms.ecrm.dubbo.interfaces.MemberAssetSysConfigDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2025/5/9
 */
@Slf4j
@Component
public class MemberAssetSysConfigDecorator {

    @DubboReference(group = "${ecrm-dsf-group}", check = false)
    private MemberAssetSysConfigDubboService configDubboService;

    /**
     * 保存设置
     */
    public String save(MemberAssetSysConfigReq req){
        req.setTrackingId(TraceNoUtil.getTraceNo());
        Response<String> response = configDubboService.save(req);
        return Response.getValidateData(response);
    }

    /**
     * 获取设置
     */
    public MemberAssetSysConfigResp get(MemberAssetSysConfigGetReq req){
        req.setTrackingId(TraceNoUtil.getTraceNo());
        Response<MemberAssetSysConfigResp> response = configDubboService.get(req);
        return Response.getValidateData(response);
    }



}
