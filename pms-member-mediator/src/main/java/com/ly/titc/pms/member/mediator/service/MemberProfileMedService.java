package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.pms.member.mediator.entity.dto.member.BatchAddMemberTagDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.BatchDeleteMemberTagDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.*;

import java.util.List;

/**
 * 会员档案Med服务
 *
 * <AUTHOR>
 * @date 2024/11/5 19:50
 */
public interface MemberProfileMedService {

    /**
     * 保存常用地址
     *
     * @param info
     */
    void saveCommonAddress(SaveMemberProfileAddressDto info);

    /**
     * 删除常用地址
     *
     * @param addressNo
     */
    void deleteCommonAddress(Integer masterType, String masterCode, String memberNo, Long addressNo, String operator);

    /**
     * 查询常用地址
     *
     * @param memberNo
     * @param name
     * @return
     */
    List<MemberProfileAddressInfoDto> listCommonAddress(Integer masterType, String masterCode, String memberNo, String name);

    /**
     * 查询地址详情
     *
     * @param memberNo
     * @param addressNo
     * @return
     */
    MemberProfileAddressInfoDto getCommonAddress(Integer masterType, String masterCode, String memberNo, Long addressNo);

    /**
     * 保存常用发票抬头
     *
     * @param info
     */
    void saveCommonInvoiceHeader(SaveMemberProfileInvoiceHeaderDto info);

    /**
     * 删除发票抬头
     *
     * @param memberNo
     * @param invoiceHeaderNo
     * @param operator
     */
    void deleteCommonInvoiceHeader(Integer masterType, String masterCode, String memberNo, Long invoiceHeaderNo, String operator);

    /**
     * 查询常用发票抬头
     *
     * @param memberNo
     * @param name
     * @return
     */
    List<MemberProfileInvoiceHeaderInfoDto> listCommonInvoiceHeader(Integer masterType, String masterCode, String memberNo, String name);

    /**
     * 查询发票抬头详情
     *
     * @param masterType
     * @param masterCode
     * @param memberNo
     * @param invoiceHeaderNo
     * @return
     */
    MemberProfileInvoiceHeaderInfoDto getCommonInvoiceHeader(Integer masterType, String masterCode, String memberNo, Long invoiceHeaderNo);

    /**
     * 保存常用入住人
     *
     * @param info
     */
    void saveCommonOccupants(SaveMemberProfileOccupantsDto info);

    /**
     * 删除常用入住人
     *
     * @param memberNo
     * @param occupantsNo
     * @param operator
     */
    void deleteCommonOccupants(Integer masterType, String masterCode, String memberNo, Long occupantsNo, String operator);

    /**
     * 查询常用入住人
     *
     * @param memberNo
     * @param name
     * @return
     */
    List<MemberProfileOccupantsInfoDto> listCommonOccupants(Integer masterType, String masterCode, String memberNo, String name);

    /**
     * 查询常住人详情
     * @param memberNo
     * @param occupantsNo
     * @return
     */
    MemberProfileOccupantsInfoDto getCommonOccupants(Integer masterType, String masterCode, String memberNo, Long occupantsNo);

    /**
     * 新增会员标签
     *
     * @param info
     */
    void addMemberTag(AddMemberProfileTagDto info);

    /**
     * 批量新增会员标签（单会员多标签）
     *
     * @param dto
     */
    void batchAddSingleMemberTag(BatchAddSingleMemberTagDto dto);

    /**
     * 删除会员标签
     *
     * @param memberNo
     * @param tagNo
     * @param operator
     */
    void deleteMemberTag(Integer masterType, String masterCode, String memberNo, Long tagNo, String operator);

    /**
     * 查询会员标签
     *
     * @param memberNo
     * @return
     */
    List<MemberProfileTagInfoDto> listMemberTag(Integer masterType, String masterCode, String memberNo);

    /**
     * 查询会员标签分组
     *
     * @param memberNo
     * @return
     */
    List<MemberProfileTagInfoGroupDto> listMemberTagGroup(Integer masterType, String masterCode, String memberNo);

    /**
     * 查询会员标签分组
     *
     * @param memberNos
     * @return
     */
    List<SimpleMemberProfileTagInfoGroupDto> listMemberTagGroup(Integer masterType, String masterCode, List<String> memberNos);

    /**
     * 查询会员标签
     *
     * @param memberNos
     * @return
     */
    List<MemberProfileTagInfoDto> listMemberTag(List<String> memberNos);

    /**
     * 查询会员标签
     *
     * @param tagIds
     * @return
     */
    List<MemberProfileTagCountDto> listMemberTagCount(List<Long> tagIds);

    /**
     * 批量新增会员标签（多会员多标签）
     *
     * @param dto
     */
    void batchAddMemberTag(BatchAddMemberTagDto dto);

    /**
     * 批量删除会员标签
     *
     * @param dto
     */
    void batchDeleteMemberTag(BatchDeleteMemberTagDto dto);

}
