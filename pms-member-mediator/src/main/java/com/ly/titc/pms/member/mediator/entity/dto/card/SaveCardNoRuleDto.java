package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 保存卡号规则
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@Accessors(chain = true)
public class SaveCardNoRuleDto {

    /**
     * 卡号前缀
     */
    private String cardPrefix;

    /**
     * 卡号长度
     */
    private Integer cardLength;

    /**
     * 排除的数字（分号分隔）
     */
    private String excludeNumber;

}
