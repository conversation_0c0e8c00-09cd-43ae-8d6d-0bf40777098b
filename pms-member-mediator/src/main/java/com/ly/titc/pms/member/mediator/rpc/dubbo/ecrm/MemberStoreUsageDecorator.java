package com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.usageRule.*;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberStoreUsageRuleAssetResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberStoreUsageRuleResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberUsageRuleSaveResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberUsageRuleSaveResultResp;
import com.ly.titc.pms.ecrm.dubbo.interfaces.MemberStoreUsageDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition 储值设置
 * @since 2024-11-15 11:54
 */
@Slf4j
@Component
public class MemberStoreUsageDecorator {
    @DubboReference(group = "${ecrm-dsf-group}", check = false,parameters = {"dubbo.consumer.serialization.disable-circular-reference-detect", "false"} )
    private MemberStoreUsageDubboService storeUsageDubboService;

    /**
     * 根据规则适用主体查询使用规则
     * 资产服务调用
     */
    public  List<MemberStoreUsageRuleAssetResp> listRuleForAsset(@Valid QueryMemberScopeUsageForAssetReq req){
        req.setTrackingId(TraceNoUtil.getTraceNo());
        Response<List<MemberStoreUsageRuleAssetResp>> response = storeUsageDubboService.listRuleForAsset(req);
        return Response.getValidateData(response);
    }


    /**
     * 根据搜索条件分页查询配置
     * CRM调用
     */
    public Pageable<MemberStoreUsageRuleResp> page(@Valid QueryMemberMasterUsageReq req){
        req.setTrackingId(TraceNoUtil.getTraceNo());
        Response<Pageable<MemberStoreUsageRuleResp>> response =storeUsageDubboService.page(req);
        return Response.getValidateData(response);
    }

    /**
     * 保存储值设置（新增和编辑）
     */
    public MemberUsageRuleSaveResp save(@Valid MemberStoreUsageRuleConfigSaveReq req){
        req.setTrackingId(TraceNoUtil.getTraceNo());
        Response<MemberUsageRuleSaveResp> response =  storeUsageDubboService.save(req);
        return Response.getValidateData(response);
    }

    /**
     * 更新状态
     */
    public Boolean updateState(@Valid UpdateMemberUsageStateReq req){
        req.setTrackingId(TraceNoUtil.getTraceNo());
        Response<Boolean> response = storeUsageDubboService.updateState(req);
        return Response.getValidateData(response);
    }

    /**
     * 删除
     */
    public Boolean delete(@Valid DeleteMemberUsageReq req){
        req.setTrackingId(TraceNoUtil.getTraceNo());
        Response<Boolean> response =  storeUsageDubboService.delete(req);
        return Response.getValidateData(response);
    }


    /**
     * 获取已设置的储值规则
     */
   public List<MemberUsageRuleSaveResultResp> listBlocScopeUsageRule(QueryMemberBlocScopeUsageReq req){
       req.setTrackingId(TraceNoUtil.getTraceNo());
       Response<List<MemberUsageRuleSaveResultResp>> response= storeUsageDubboService.listBlocScopeUsageRule(req);
       return Response.getValidateData(response);
   }


}
