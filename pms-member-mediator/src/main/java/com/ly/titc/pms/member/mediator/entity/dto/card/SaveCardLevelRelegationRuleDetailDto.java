package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 保存会员等级保级规则
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Getter
@Setter
@Accessors(chain = true)
public class SaveCardLevelRelegationRuleDetailDto {

    /**
     * 会员保级等级
     */
    private Integer sourceLevel;

    /**
     * 条件类型： 0-入住次数 1-房晚 2-充值金额 3-消费金 4-成长值
     */
    private Integer conditionType;

    /**
     * 条件值
     */
    private String conditionValue;

    /**
     * 计算方式计算方式 1 大于等于 2 大于 3 小于等于 4 小于
     */
    private Integer calculateType;

    /**
     * 是否限制渠道：0-不限制，1-限制
     */
    private Integer isRestrictChannel;

    /**
     * 限制渠道；多个渠道英文分号;分隔
     */
    private String restrictChannelCodes;

}
