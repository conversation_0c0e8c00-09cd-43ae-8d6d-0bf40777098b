package com.ly.titc.pms.member.mediator.entity.dto.member.config;

import com.ly.titc.pms.ecrm.dubbo.entity.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：rui
 * @name：GetMemberRelatedConfigReq
 * @Date：2024-11-25 11:34
 * @Filename：GetMemberRelatedConfigReq
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetMemberRelatedConfigDto extends BaseReq {
    private Integer masterType;
    private String masterCode;
    private Integer type;
}
