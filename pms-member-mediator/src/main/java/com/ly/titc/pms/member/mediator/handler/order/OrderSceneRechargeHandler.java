package com.ly.titc.pms.member.mediator.handler.order;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.MemberStoreRechargeReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.MemberStoreRechargeRollBackReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreRecordOPResultResp;
import com.ly.titc.pms.member.biz.MemberOrderDetailInfoBiz;
import com.ly.titc.pms.member.biz.MemberOrderInfoBiz;
import com.ly.titc.pms.member.biz.MemberOrderRechargeSceneInfoBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRechargeSceneInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.converter.MemberOrderMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.order.ActivityOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.CreateOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.OrderPostResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.recharge.MemberStoreRechargeDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.activity.MemberActivityDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.MemberStoreOpDecorator;
import com.ly.titc.pms.spm.dubbo.mq.message.MemberActivityDiscountGrantMsg;
import com.ly.titc.pms.spm.dubbo.mq.message.MemberActivityRevokeDiscountMsg;
import com.ly.titc.springboot.mq.producer.TurboMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_ACTIVITY_GRANT_TAG;
import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_ACTIVITY_ROLLBACK_TAG;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-26 17:41
 */
@Slf4j
@Component
public class OrderSceneRechargeHandler extends AbstractOrderSceneHandler<MemberStoreRechargeDto> {

    @Resource
    private MemberOrderRechargeSceneInfoBiz rechargeSceneInfoBiz;
    @Resource
    private MemberOrderMedConverter orderMedConverter;
    @Resource
    private MemberOrderDetailInfoBiz detailInfoBiz;
    @Resource
    private MemberOrderInfoBiz memberOrderInfoBiz;
    @Resource
    private MemberStoreOpDecorator rechargeDecorator;
    @Resource(type = TurboMQProducer.class)
    private TurboMQProducer producer;
    @Resource
    private MemberActivityDecorator memberActivityDecorator;


    @Override
    public CreateOrderDto<MemberStoreRechargeDto> doPreCheck(CreateOrderDto<MemberStoreRechargeDto> dto) {
        if(!Objects.isNull(dto.getActivityOrderDto())){
            ActivityOrderDto activityOrderDto = dto.getActivityOrderDto();
            MemberStoreRechargeDto memberSceneNoteDto = dto.getMemberSceneNoteDto();
            boolean flag = memberActivityDecorator.judgeAvailableMemberActivity(dto.getBlocCode(),activityOrderDto.getActivityCode(),
                    activityOrderDto.getGearCode(),memberSceneNoteDto.getMemberNo(),null,null);
            if (!flag) {
                throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00017);
            }
        }
        return dto;
    }

    @Override
    public String doGetLockKey(MemberStoreRechargeDto dto) {
        return CommonConstant.CREATE_ORDER_LOCK_KEY_PREFIX + String.format("%s_%s", getScene(), dto.getMemberNo());

    }

    @Override
    public void saveSceneOrder(CreateOrderDto<MemberStoreRechargeDto> dto) {
        MemberStoreRechargeDto rechargeDto = dto.getMemberSceneNoteDto();
        rechargeDto.setMasterCode(dto.getMasterCode());
        MemberOrderRechargeSceneInfo sceneInfo = orderMedConverter.convert(rechargeDto);
        sceneInfo.setMemberOrderNo(dto.getMemberOrderNo());
        rechargeSceneInfoBiz.add(sceneInfo);
    }

    @Override
    public OrderPostResultDto postHandle(MemberOrderInfo orderInfo) {
        //支付成功后置处理，调用会员资产服务充值
        String memberOrderNo = orderInfo.getMemberOrderNo();
        MemberOrderInfo memberOrderInfo = memberOrderInfoBiz.getByOrderNo(memberOrderNo);
        MemberOrderDetailInfo detailInfo = detailInfoBiz.getByOrderNo(memberOrderNo);
        MemberOrderRechargeSceneInfo rechargeSceneInfo = rechargeSceneInfoBiz.getByOrderNo(memberOrderNo);
        MemberStoreRechargeReq req = orderMedConverter.convert(rechargeSceneInfo, memberOrderInfo, detailInfo);
        rechargeDecorator.recharge(req);

        OrderPostResultDto resultDto = new OrderPostResultDto();
        resultDto.setMemberNo(rechargeSceneInfo.getMemberNo());
        resultDto.setMemberOrderNo(memberOrderNo);
        // 发放礼包
        try {
            MemberActivityDiscountGrantMsg msg = buildSendGIftMsg(orderInfo, detailInfo);
            String msgJson = JSONObject.toJSONString(msg);
            log.info("储值活动发放礼包,msg:{}", msgJson);
            producer.sendMsgWithTag(TurboMqTopic.PMS_SPM_ACTIVITY_TOPIC, MEMBER_ACTIVITY_GRANT_TAG, msgJson);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return resultDto;

    }

    @Override
    public void refundHandle(MemberOrderRefundInfo orderInfo) {
        MemberStoreRechargeRollBackReq rollBackReq = orderMedConverter.convert(orderInfo, TraceNoUtil.getTraceNo());
        rollBackReq.setOperator(orderInfo.getModifyUser());
        rollBackReq.setTradeNo(orderInfo.getMemberOrderNo());
        MemberStoreRecordOPResultResp memberStoreRecordOPResultResp = rechargeDecorator.rechargeRollback(rollBackReq);
        log.info("储值已会退，memberStoreRecordOPResultResp:{}", memberStoreRecordOPResultResp);

        // 礼包退回
        MemberOrderDetailInfo detailInfo = detailInfoBiz.getByOrderNo(orderInfo.getMemberOrderNo());
        MemberActivityRevokeDiscountMsg msg = new MemberActivityRevokeDiscountMsg();
        msg.setBlocCode(orderInfo.getBlocCode());
        msg.setActivityCode(detailInfo.getActivityCode());
        msg.setSourceBizCode(orderInfo.getMemberOrderNo());
        msg.setSourceClient(orderInfo.getPlatformChannel());
        msg.setTraceId(TraceNoUtil.getTraceNo());
        try {
            producer.sendMsgWithTag(TurboMqTopic.PMS_SPM_ACTIVITY_TOPIC, MEMBER_ACTIVITY_ROLLBACK_TAG, JSONObject.toJSONString(msg));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public String getScene() {
        return MemberSceneEnum.RECHARGE.getScene();
    }

    private static MemberActivityDiscountGrantMsg buildSendGIftMsg(MemberOrderInfo orderInfo, MemberOrderDetailInfo detailInfo) {
        MemberActivityDiscountGrantMsg msg = new MemberActivityDiscountGrantMsg();
        msg.setBlocCode(orderInfo.getBlocCode());
        msg.setHotelCode(orderInfo.getHotelCode());
        msg.setActivityCode(detailInfo.getActivityCode());
        msg.setSourceBizCode(orderInfo.getMemberOrderNo());
        msg.setActivityItemCode(detailInfo.getGearCode());
        msg.setMemberNo(orderInfo.getMemberNo());
        msg.setSourceClient(orderInfo.getPlatformChannel());
        msg.setTraceId(TraceNoUtil.getTraceNo());
        return msg;
    }

}
