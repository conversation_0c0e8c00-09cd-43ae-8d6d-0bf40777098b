package com.ly.titc.pms.member.mediator.entity.dto.recharge;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 会员充值DTO
 *
 * <AUTHOR>
 * @date 2024/11/19 17:13
 */
@Data
public class MemberStoreRechargeDto {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    private String masterCode;
    /**
     * 酒馆组
     */
    private String clubCode;

    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 门店编号
     */
    private String hotelCode;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 本金金额
     */
    private BigDecimal capitalAmount;

    /**
     * 礼金金额
     */
    private BigDecimal giftAmount;

    /**
     * 礼金金额过期时间
     */
    private String giftExpireDate;


    /**
     * 操作人
     */
    private String operator;
}
