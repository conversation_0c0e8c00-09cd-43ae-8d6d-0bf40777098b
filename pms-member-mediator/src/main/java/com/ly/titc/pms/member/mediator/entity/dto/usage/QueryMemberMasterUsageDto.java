package com.ly.titc.pms.member.mediator.entity.dto.usage;

import com.ly.titc.pms.member.mediator.entity.dto.BasePageDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:10
 */
@Data
@Accessors(chain = true)
public class QueryMemberMasterUsageDto extends BasePageDto {

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 适用来源
     */
    private List<String> scopeSources;

    /**
     * 适用平台渠道
     */
    private List<String> scopePlatformChannels;

    /**
     * 适用门店编码
     */
    private List<String> scopeHotelCodes;


    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

}
