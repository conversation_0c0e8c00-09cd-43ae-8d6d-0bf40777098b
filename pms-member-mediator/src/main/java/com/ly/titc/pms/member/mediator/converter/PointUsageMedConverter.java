package com.ly.titc.pms.member.mediator.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.mdm.entity.response.hotel.SelectHotelResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MasterObject;
import com.ly.titc.pms.member.com.enums.AssetTypeEnum;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageModeScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageRuleScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.PointUsageRuleInfo;
import com.ly.titc.pms.member.entity.bo.PageUsageRuleScopeMappingBo;
import com.ly.titc.pms.member.entity.wrapper.PointUsageRuleWrapper;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.point.*;
import com.ly.titc.pms.member.mediator.entity.dto.usage.MemberUsageRuleSaveResultDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/25 20:38
 */
@Mapper(componentModel = "spring")
public interface PointUsageMedConverter extends UsageMedConverter{

    @Mappings({
            @Mapping(target = "usageRuleId", source = "id"),
            @Mapping(target = "usageRuleName", source = "ruleName"),
            @Mapping(target = "scopePlatformChannels", source = "scopePlatformChannels", ignore = true),
    })
    PointUsageRuleDto convertPoToDto(PointUsageRuleInfo ruleInfo);

    @Mappings({
            @Mapping(target = "masterType", source = "usageMasterType"),
            @Mapping(target = "masterCode", source = "usageMasterCode"),
    })
    MasterObject convertPoToDto(AssetUsageModeScopeMapping scopeMapping);

    PageUsageRuleScopeMappingBo convertDtoToBo(PagePointUsageDto dto);


    @Mappings({
            @Mapping(target = "scopeSources", source = "scopeSources", ignore = true),
            @Mapping(target = "scopePlatformChannels", source = "scopePlatformChannels", ignore = true),

    })
    PointUsageRuleDetailDto convertPoint(PointUsageRuleInfo info);

    default List<PointUsageRuleDetailDto> convertPointDetail(List<PointUsageRuleInfo> records,
                                                             List<AssetUsageModeScopeMapping> usageModeScopeMappings,
                                                             List<AssetUsageRuleScopeMapping> scopeMappings) {
        List<PointUsageRuleDetailDto> detailDtoList = new ArrayList<>();
        Map<Long, List<AssetUsageModeScopeMapping>> usageModeMap = usageModeScopeMappings.stream().collect(Collectors.groupingBy(AssetUsageModeScopeMapping::getRuleId));
        Map<Long, List<AssetUsageRuleScopeMapping>> scopMap = scopeMappings.stream().collect(Collectors.groupingBy(AssetUsageRuleScopeMapping::getRuleId));

        records.forEach(ruleInfo -> {
            PointUsageRuleDetailDto detailDto = convertPoint(ruleInfo);
            Long ruleId = ruleInfo.getId();
            List<AssetUsageModeScopeMapping> usageModeMapping = usageModeMap.get(ruleId);
            if (usageModeMapping != null && CollectionUtil.isNotEmpty(usageModeMapping)) {
                usageModeMapping = usageModeMapping.stream().filter(s -> StringUtils.isNotBlank(s.getHotelCode())).collect(Collectors.toList());
                detailDto.setUsageHotelCodes(usageModeMapping.stream().map(AssetUsageModeScopeMapping::getHotelCode).collect(Collectors.toList()));
            }
            List<AssetUsageRuleScopeMapping> scopeMappingList = scopMap.get(ruleId);
            if (CollectionUtils.isNotEmpty(scopeMappingList)) {
                //去除scopeMappingList中酒店编号为空的数据
                scopeMappingList = scopeMappingList.stream().filter(s -> StringUtils.isNotBlank(s.getHotelCode())).collect(Collectors.toList());
                detailDto.setScopeHotelCodes(scopeMappingList.stream().map(AssetUsageRuleScopeMapping::getHotelCode).distinct().collect(Collectors.toList()));
            }
            String scopeSources = ruleInfo.getScopeSources();
            if (StringUtils.isNotEmpty(scopeSources)) {
                detailDto.setScopeSources(Arrays.asList(scopeSources.split(",")));
            }
            String scopePlatformChannels = ruleInfo.getScopePlatformChannels();
            if (StringUtils.isNotEmpty(scopePlatformChannels)) {
                detailDto.setScopePlatformChannels(Arrays.asList(scopePlatformChannels.split(",")));
            }

            detailDtoList.add(detailDto);
        });
        return detailDtoList;
    }

    @Mappings({
            @Mapping(target = "scopePlatformChannels", source = "scopePlatformChannels", ignore = true),
            @Mapping(target = "scopeSources", source = "scopeSources", ignore = true)
    })
    PointUsageRuleInfo convertDtoToPo(SavePointUsageRuleDto dto);

    @Mappings({
            @Mapping(target = "scopeHotelCode", source = "hotelCode")
    })
    BlocScopeUsageDto convertPoToBlocDto(AssetUsageRuleScopeMapping mapping);

    default PointUsageRuleWrapper convertPointWrapper(SavePointUsageRuleDto dto) {
        PointUsageRuleWrapper wrapper = new PointUsageRuleWrapper();
        PointUsageRuleInfo ruleInfo = convertDtoToPo(dto);
        String scopeSourceStr = String.join(",", dto.getScopeSources());
        String scopePlatformChannelStr = String.join(",", dto.getScopePlatformChannels());
        ruleInfo.setScopeSources(scopeSourceStr);
        ruleInfo.setScopePlatformChannels(scopePlatformChannelStr);
        //组装info
        wrapper.setRuleInfo(ruleInfo);
        wrapper.setScopeMappings(convertMapping(dto, AssetTypeEnum.POINT.getType()));
        wrapper.setUsageModeScopeMappings(convertUsage(dto, AssetTypeEnum.POINT.getType()));
        return wrapper;
    }

    PageUsageRuleScopeMappingBo convertDtoToBo(ListBlocScopeUsageDto dto);

    default List<MemberUsageRuleSaveResultDto> convertConfig(List<SelectHotelResp> hotelResps){
        List<MemberUsageRuleSaveResultDto> resultDtos = new ArrayList<>();
        PlatformChannelEnum.listAllPlatformChannel().forEach(s -> {
            //集团维度
            MemberUsageRuleSaveResultDto resultDto = new MemberUsageRuleSaveResultDto();
            resultDto.setScopePlatformChannel(s);
            resultDto.setScopePlatformChannelName(PlatformChannelEnum.getByPlatformChannel(s).getPlatformChannelDesc());
            resultDto.setScopeSource(ScopeSourceEnum.BLOC.getCode());
            resultDtos.add(resultDto);
            //酒店维度
            hotelResps.forEach(resp -> {
                MemberUsageRuleSaveResultDto hotelDto = new MemberUsageRuleSaveResultDto();
                hotelDto.setScopeSource(ScopeSourceEnum.HOTEL.getCode());
                hotelDto.setScopeHotelCode(resp.getHotelVid().toString());
                hotelDto.setScopeHotelName(resp.getHotelName());
                hotelDto.setScopePlatformChannel(s);
                hotelDto.setScopePlatformChannelName(PlatformChannelEnum.getByPlatformChannel(s).getPlatformChannelDesc());
                resultDtos.add(hotelDto);
            });

        });
        return resultDtos;
    }

    ListBlocScopeUsageDto convertDtoToDto(BaseDto dto);
}
