package com.ly.titc.pms.member.mediator.rpc.dsf.mdm;

import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.api.BrandService;
import com.ly.titc.mdm.entity.request.brand.SelectBrandReq;
import com.ly.titc.mdm.entity.response.brand.SelectBrandResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;


/**
 * <AUTHOR>
 * @classname BrandDecorator
 * @descrition
 * @since 2021/10/8 下午8:13
 */
@Slf4j
@Component
public class BrandDecorator {

    @DubboReference(protocol = "dsf", providedBy = "${mdm-dsf-group}", version = "${mdm-dsf-service-version}")
    private BrandService brandService;

    public List<SelectBrandResp> listBrand(String blocCode,Integer state,String brandName) {
        SelectBrandReq req = new SelectBrandReq();
        req.setBlocCode(blocCode);
        req.setState(state);
        req.setBrandName(brandName);
        req.setTrackingId(UUID.randomUUID().toString());
        Response<List<SelectBrandResp>> resp = brandService.selectBrand(req);
        return Response.getValidateData(resp);
    }

    /**
     * 品牌下拉列表
     *
     * @param req
     * @return
     */
    public List<SelectBrandResp> selectBrands(SelectBrandReq req) {
        return Response.getValidateData(brandService.selectBrand(req));
    }
}
