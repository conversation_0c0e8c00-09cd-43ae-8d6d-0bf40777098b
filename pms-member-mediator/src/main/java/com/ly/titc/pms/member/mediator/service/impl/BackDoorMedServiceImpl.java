package com.ly.titc.pms.member.mediator.service.impl;

import com.ly.titc.pms.member.biz.BackDoorBiz;
import com.ly.titc.pms.member.mediator.service.BackDoorMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/11/27 10:34
 */
@Slf4j
@Service
public class BackDoorMedServiceImpl implements BackDoorMedService {

    @Resource
    private BackDoorBiz backDoorBiz;

    @Override
    public void doUpdateSomething(String sql) {
        backDoorBiz.doUpdateSomething(sql);
    }

    @Override
    public void doInsertSomething(String sql) {
        backDoorBiz.doInsertSomething(sql);
    }

    @Override
    public void doDeleteSomething(String sql) {
        backDoorBiz.doDeleteSomething(sql);
    }

}
