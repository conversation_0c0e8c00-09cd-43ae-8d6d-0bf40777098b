package com.ly.titc.pms.member.mediator.entity.message;

import com.ly.titc.cashier.dubbo.enums.CashierPrePayStateEnum;
import com.ly.titc.cashier.dubbo.enums.CashierPrePayTradeStateEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-8-29 20:35
 */
@Data
@Accessors(chain = true)
public class PrePaySuccessInfoMsgExt {
    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 授权号(非线上支付的用户输入)
     */
    private String prePayNo;

    /**
     * 收银台预授权申请号
     */
    private String prePayOperateNo;

    /**
     * 渠道交易号
     */
    private String transactionId;


    /**
     * 支付中台交易号
     */
    private String prePayTradeNo;

    /**
     * 收款账户号
     */
    private String accountNo;

    /**
     * 收款账户名
     */
    private String accountName;

    /**
     * 预授权金额
     */
    private BigDecimal prePayAmount;

    /**
     * 预授权完成金额
     */
    private BigDecimal prePaySettleAmount;

    /**
     * 支付渠道：微信，支付宝，银行，POS机
     */
    private String payChannel;

    /**
     * 支付产品
     */
    private String payProduct;

    /**
     * 支付方式： wxPay 微信支付； aliPay 支付宝支付； unionpay 银联支付； posSlotCard 国内卡刷卡； posWildcard 境外卡刷卡； posWxPay pos微信支付； posAliPay pos支付宝支付； posUnionPay pos银联
     */
    private String payType;

    /**
     * POS机终端ID
     */
    private String termId;

    /**
     * 当面付(付款条码),授权码
     */
    private String authCode;

    /**
     * 银行卡号
     */
    private String cardNo;

    /**
     * 预授权状态 0 预授权、1 完成、2 取消 3,退款
     * @see CashierPrePayStateEnum
     */
    private Integer prePayState;

    /**
     * 预授权交易状态
     * @see CashierPrePayTradeStateEnum
     */
    private Integer prePayTradeState;


    /**
     * 商品描述
     */
    private String goodsDes;

    /**
     * 备注
     */
    private String remark;

    /**
     * 原始凭证号（用户输入）
     */
    private String originalVoucherNo;


    /**
     * 业务线唯一号
     * 收银台对业务请求校验幂等的key，业务线需保证唯一
     */
    private String businessNo;

    /**
     * 业务线类型不能为空
     * @See com.ly.titc.cashier.dubbo.enums.BusinessTypeEnum
     */
    private String businessType;
    /**
     * 业务线请求体
     * 仅仅只保存，支付查询和回调会回传
     */
    private String businessNote;

    /**
     * 操作业务线请求体
     */
    private String prePayOperateNote;


    /**
     * 预授权操作类型
     */
    private String prePayOperateType;

    /**
     * 预授权完成时间
     */
    private String completeTime;

    /**
     * 操作人
     */
    private String operator;



    /**
     * 创建时间
     */
    private String gmtCreate;
}
