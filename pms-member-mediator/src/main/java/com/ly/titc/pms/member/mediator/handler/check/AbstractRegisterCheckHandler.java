package com.ly.titc.pms.member.mediator.handler.check;

import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import com.ly.titc.pms.member.mediator.manager.RegisterCheckHandlerManager;

/**
 * @Author：rui
 * @name：AbstractMemberRegisterCheckHandler
 * @Date：2024-11-25 16:27
 * @Filename：AbstractMemberRegisterCheckHandler
 */
public abstract class AbstractRegisterCheckHandler {



    public abstract Integer getAction();

    public AbstractRegisterCheckHandler() {
        RegisterCheckHandlerManager.putHandler(this.getAction(), this);
    }

    protected AbstractRegisterCheckHandler getHandler() {
        return RegisterCheckHandlerManager.getHandler(this.getAction());
    }

    public abstract void check(RegisterMemberDto dto);
}
