package com.ly.titc.pms.member.mediator.entity.message;

import com.ly.titc.cashier.dubbo.enums.CashierPayStateEnum;
import com.ly.titc.cashier.dubbo.enums.PayProductEnum;
import com.ly.titc.common.mq.msg.MsgExt;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-7 21:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class PaySuccessInfoMsgExt extends MsgExt {
    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 收银台流水号
     */
    private String onlinePayNo;

    /**
     * 渠道交易号
     */
    private String transactionId;

    /**
     * 业务支付申请号
     */
    private String bizPayNo;

    /**
     * 系统来源
     */
    private String sourceSystem;

    /**
     * 收款账户号
     */
    private String accountNo;

    /**
     * 收款账户名
     */
    private String accountName;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 支付渠道：微信，支付宝，银行，POS机
     */
    private String payChannel;

    /**
     * 支付产品
     * @see PayProductEnum
     */
    private String payProduct;
    /**
     * 支付厂商： FUIOU 富友 WX 微信 ZFB 支付宝 TALN 通联 UNP 银联
     */
    private String payVendor;
    /**
     * 支付方式： wxPay 微信支付； aliPay 支付宝支付； unionpay 银联支付； posSlotCard 国内卡刷卡； posWildcard 境外卡刷卡； posWxPay pos微信支付； posAliPay pos支付宝支付； posUnionPay pos银联
     */
    private String payType;

    /**
     * POS机终端ID
     */
    private String termId;

    /**
     * 当面付(付款条码),授权码
     */
    private String authCode;

    /**
     * 银行卡号
     */
    private String cardNo;

    /**
     * 支付状态 1 支付中、2 支付成功、3 支付失败
     * @see CashierPayStateEnum
     */
    private Integer payState;
    /**
     * 支付完成时间
     */
    private String payedTime;


    /**
     * 商品描述
     */
    private String goodsDes;

    /**
     * 备注
     */
    private String remark;

    /**
     * 失败原因 状态为支付失败时返回
     */
    private String reason;


    /**
     * 业务线唯一号
     * 收银台对业务请求校验幂等的key，业务线需保证唯一
     */
    private String businessNo;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 业务线请求体
     */
    private String businessNote;
    /**
     * 操作人
     */
    private String operator;
}
