package com.ly.titc.pms.member.mediator.entity.dto.general;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：rui
 * @name：DictDto
 * @Date：2024-12-4 16:39
 * @Filename：DictDto
 */
@Data
@Accessors(chain = true)
public class DictDto {

    /**
     * 名称
     */
    private String name;

    /**
     * 值
     */
    private String value;

    /**
     * 子集
     */
    private List<DictItemDto> items;
}
