package com.ly.titc.pms.member.mediator.rpc.dubbo.account;

import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.enums.StateEnum;
import com.ly.titc.pms.account.dubbo.entity.request.expense.ListExpenseReq;
import com.ly.titc.pms.account.dubbo.entity.request.settlement.GetSettlementItemInfoReq;
import com.ly.titc.pms.account.dubbo.entity.request.settlement.GetSettlementItemListReq;
import com.ly.titc.pms.account.dubbo.entity.response.ExpenseInfoResp;
import com.ly.titc.pms.account.dubbo.entity.response.SettlementInfoResp;
import com.ly.titc.pms.account.dubbo.entity.response.SettlementItemInfoResp;
import com.ly.titc.pms.account.dubbo.interfaces.ExpenseDubboService;
import com.ly.titc.pms.account.dubbo.interfaces.SettlementItemDubboService;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
 * 消费项
 *
 * <AUTHOR>
 * @date 2024/12/28 16:04
 */
@Slf4j
@Component
public class AccountSettlementDecorator {

    @DubboReference(group = "${account-dsf-dubbo-group}",timeout = 30000)
    private SettlementItemDubboService settlementItemDubboService;


    public List<SettlementInfoResp> listSettlement(String blocCode){
        GetSettlementItemListReq req = new GetSettlementItemListReq();
        req.setMasterCode(blocCode);
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setState(StateEnum.VALID.getValue());
        req.setTrackingId(UUID.randomUUID().toString());
        return Response.getValidateData(settlementItemDubboService.getSettlementInfoList(req));
    }

    public SettlementItemInfoResp getSettlementInfo(String blocCode,String itemCode){
        GetSettlementItemInfoReq req = new GetSettlementItemInfoReq();
        req.setMasterCode(blocCode);
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setItemCode(itemCode);
        req.setTrackingId(UUID.randomUUID().toString());
        return Response.getValidateData(settlementItemDubboService.getSettlementInfo(req));
    }

}
