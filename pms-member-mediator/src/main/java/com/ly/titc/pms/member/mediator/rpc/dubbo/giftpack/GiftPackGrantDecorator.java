package com.ly.titc.pms.member.mediator.rpc.dubbo.giftpack;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.spm.dubbo.entity.request.giftpack.GetBatchInfoReq;
import com.ly.titc.pms.spm.dubbo.entity.request.giftpack.GrantGiftPackReq;
import com.ly.titc.pms.spm.dubbo.entity.request.giftpack.PageGiftPackBatchInfoReq;
import com.ly.titc.pms.spm.dubbo.entity.request.giftpack.PageGiftPackGrantDetailReq;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.GiftPackBatchDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.GiftPackBatchInfoResp;
import com.ly.titc.pms.spm.dubbo.interfaces.GiftPackGrantDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GiftPackGrantDecorator {
    @DubboReference(group = "${spm-dsf-dubbo-group}",check = false)
    private GiftPackGrantDubboService giftPackGrantDubboService;


    public void batchGrant(GrantGiftPackReq req){
        Response<Void> response = giftPackGrantDubboService.batchGrant(req);
        Response.getValidateData(response);
    }

    public Pageable<GiftPackBatchInfoResp> pageBatchInfo(PageGiftPackBatchInfoReq req) {
        Response<Pageable<GiftPackBatchInfoResp>> pageableResponse = giftPackGrantDubboService.pageBatchInfo(req);
        return Response.getValidateData(pageableResponse);
    }

    public void deleteBatch(GetBatchInfoReq req){
        Response<Void> response = giftPackGrantDubboService.deleteBatch(req);
        Response.getValidateData(response);
    }

    public void invalidBatch(GetBatchInfoReq req){
        Response<Void> response = giftPackGrantDubboService.invalidBatch(req);
        Response.getValidateData(response);
    }

    public GiftPackBatchInfoResp getBatchInfo(GetBatchInfoReq req){
        Response<GiftPackBatchInfoResp> response = giftPackGrantDubboService.getBatchInfo(req);
        return Response.getValidateData(response);
    }

    public Pageable<GiftPackBatchDetailResp> pageGrantDetailInfo(PageGiftPackGrantDetailReq req) {
        Response<Pageable<GiftPackBatchDetailResp>> response = giftPackGrantDubboService.pageGrantDetailInfo(req);
        return Response.getValidateData(response);
    }

//    public List<GiftPackBatchDetailListResp> queryBizGrantDetails(QueryGiftPackGrantDetailsReq req) {
//        Response<List<GiftPackBatchDetailListResp>> listResponse = giftPackGrantDubboService.queryBizGrantDetails(req);
//        return Response.getValidateData(listResponse);
//    }
}
