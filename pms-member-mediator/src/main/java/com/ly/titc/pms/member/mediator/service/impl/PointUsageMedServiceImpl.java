package com.ly.titc.pms.member.mediator.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.mdm.entity.response.hotel.SelectHotelResp;
import com.ly.titc.mdm.enums.HotelOpenStateEnum;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MasterObject;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberUsageRuleSaveResultResp;
import com.ly.titc.pms.member.biz.AssetUsageModeScopeMappingBiz;
import com.ly.titc.pms.member.biz.AssetUsageRuleScopeMappingBiz;
import com.ly.titc.pms.member.biz.PointUsageRuleBiz;
import com.ly.titc.pms.member.com.enums.AssetTypeEnum;
import com.ly.titc.pms.member.com.enums.StoreUsageModeEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.enums.StateEnum;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageModeScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageRuleScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.PointUsageRuleInfo;
import com.ly.titc.pms.member.entity.bo.PageUsageRuleScopeMappingBo;
import com.ly.titc.pms.member.entity.wrapper.PointUsageRuleWrapper;
import com.ly.titc.pms.member.mediator.converter.PointUsageMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.point.*;
import com.ly.titc.pms.member.mediator.entity.dto.usage.MemberUsageRuleSaveResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleResultDto;
import com.ly.titc.pms.member.mediator.help.UsageRuleCheckParamHelper;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.service.PointUsageMedService;
import com.ly.titc.pms.member.service.UsageRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/25 19:57
 */
@Slf4j
@Service
public class PointUsageMedServiceImpl implements PointUsageMedService {

    @Resource
    private PointUsageRuleBiz pointRuleInfoBiz;
    @Resource
    private AssetUsageRuleScopeMappingBiz usageRuleScopeMappingBiz;
    @Resource
    private AssetUsageModeScopeMappingBiz usageModeScopeMappingBiz;
    @Resource(type = PointUsageMedConverter.class)
    private PointUsageMedConverter pointUsageMedConverter;
    @Resource
    private UsageRuleCheckParamHelper usageRuleCheckParamHelper;
    @Resource
    private UsageRuleService usageRuleService;
    @Resource
    private HotelDecorator hotelDecorator;

    @Override
    public List<PointUsageRuleDto> listPointRule(ListPointRuleDto dto) {
        String scopeSource = dto.getScopeSource();
        String platformChannel = dto.getPlatformChannel();
        String scopeSourceCode = dto.getScopeSourceCode();
        //1.查询使用的主体方的适用的规则
        List<AssetUsageRuleScopeMapping> usageRuleScopeMappings = usageRuleScopeMappingBiz.listByScope(scopeSource, scopeSourceCode, AssetTypeEnum.POINT.getType(), platformChannel);
        if (CollectionUtils.isEmpty(usageRuleScopeMappings)) {
            log.error("没有配置规则:scopeSource={},scopeMasterCode={}", scopeSource, scopeSourceCode);
            return Collections.emptyList();
        }
        //2.查询规则
        List<Long> ruleIds = usageRuleScopeMappings.stream().map(AssetUsageRuleScopeMapping::getRuleId).distinct().collect(Collectors.toList());
        List<PointUsageRuleInfo> ruleInfos = pointRuleInfoBiz.listByIds(ruleIds);
        if (CollectionUtils.isEmpty(ruleInfos)) {
            log.error("没有查询到规则:ruleIds={}", ruleIds);
            return Collections.emptyList();
        }
        //过滤usageMode为指定指定可用的规则
        List<PointUsageRuleInfo> usageRuleInfos = ruleInfos.stream().filter(rule -> rule.getUsageMode().equals(StoreUsageModeEnum.ALLOCATE.getValue())).collect(Collectors.toList());
        //如果是为指定指定可用的使用规则，查询指定可用的酒店范围 （指定可用如果想用本店充值的，配置的时候一定要包含本店才能使用）
        List<AssetUsageModeScopeMapping> usageModeScopeMappings = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(usageRuleInfos)) {
            usageModeScopeMappings = usageModeScopeMappingBiz.listByRuleIds(usageRuleInfos.stream().map(PointUsageRuleInfo::getId).collect(Collectors.toList()));
        }
        Map<Long, PointUsageRuleInfo> ruleInfoMap = ruleInfos.stream().collect(Collectors.toMap(PointUsageRuleInfo::getId, rule -> rule));
        Map<Long, List<AssetUsageModeScopeMapping>> usageModeScopeMappingMap = usageModeScopeMappings.stream().collect(Collectors.groupingBy(AssetUsageModeScopeMapping::getRuleId));
        List<PointUsageRuleDto> ruleDtos = new ArrayList<>();
        //组装响应体
        usageRuleScopeMappings.forEach(usageRuleScopeMapping -> {
            Long ruleId = usageRuleScopeMapping.getRuleId();
            PointUsageRuleInfo ruleInfo = ruleInfoMap.get(ruleId);
            if (ruleInfo.getState().equals(StateEnum.CLOSE.getState())) {
                log.info("规则状态已停用：ruleId={}", ruleId);
                return;
            }
            PointUsageRuleDto usageRuleDto = pointUsageMedConverter.convertPoToDto(ruleInfo);
            usageRuleDto.setScopePlatformChannels(Arrays.asList(ruleInfo.getScopePlatformChannels().split(",")));
            if (ruleInfo.getUsageMode().equals(StoreUsageModeEnum.ALLOCATE.getValue())) {
                List<AssetUsageModeScopeMapping> ruleModeScopeMapping = usageModeScopeMappingMap.getOrDefault(ruleId, Lists.newArrayList());
                List<MasterObject> usageModeScopes = ruleModeScopeMapping.stream().map(pointUsageMedConverter::convertPoToDto).collect(Collectors.toList());
                usageRuleDto.setUsageModeScopes(usageModeScopes);
            }
            ruleDtos.add(usageRuleDto);

        });
        return ruleDtos;
    }

    @Override
    public Pageable<PointUsageRuleDetailDto> pagePointUsageRule(PagePointUsageDto dto) {
        PageUsageRuleScopeMappingBo pageBo = pointUsageMedConverter.convertDtoToBo(dto);
        List<AssetUsageRuleScopeMapping> scopeMappings = usageRuleScopeMappingBiz.listByScopeSource(AssetTypeEnum.POINT.getType(), pageBo);
        if (CollectionUtils.isNotEmpty(pageBo.getScopeSources()) || CollectionUtils.isNotEmpty(pageBo.getScopePlatformChannels()) || CollectionUtils.isNotEmpty(pageBo.getScopeHotelCodes())) {
            if (CollectionUtils.isEmpty(scopeMappings)) {
                return Pageable.empty();
            }
        }
        //获取ruleIds
        List<Long> scopeRuleIds = scopeMappings.stream().map(AssetUsageRuleScopeMapping::getRuleId).distinct().collect(Collectors.toList());
        pageBo.setRuleIds(scopeRuleIds);
        Page<PointUsageRuleInfo> ruleInfoPage = pointRuleInfoBiz.pagePointRule(pageBo);
        List<PointUsageRuleInfo> records = ruleInfoPage.getRecords();
        List<Long> ruleIds = records.stream().map(PointUsageRuleInfo::getId).collect(Collectors.toList());
        List<AssetUsageModeScopeMapping> usageModeScopeMappings = usageModeScopeMappingBiz.listByRuleIds(ruleIds);
        List<PointUsageRuleDetailDto> detailDtoList = pointUsageMedConverter.convertPointDetail(records, usageModeScopeMappings, scopeMappings);
        return PageableUtil.convert(ruleInfoPage, detailDtoList);
    }

    @Override
    public List<SaveUsageRuleResultDto> savePointUsageRule(SavePointUsageRuleDto dto) {
        //1.校验入参
        usageRuleCheckParamHelper.checkArgUsage(dto);
        //2.校验唯一性
        List<SaveUsageRuleResultDto> resultList = usageRuleCheckParamHelper.checkRepeat(dto, AssetTypeEnum.POINT.getType());
        if (CollectionUtils.isNotEmpty(resultList)) {
            return resultList;
        }
        PointUsageRuleWrapper wrapper = pointUsageMedConverter.convertPointWrapper(dto);
        usageRuleService.savePointUsageRule(wrapper);
        return Collections.emptyList();
    }

    @Override
    public Boolean updateState(UpdatePointUsageStateDto dto) {
        PointUsageRuleInfo pointUsageRuleInfo = pointRuleInfoBiz.getById(dto.getRuleId());
        if (pointUsageRuleInfo == null || (!pointUsageRuleInfo.getMasterType().equals(dto.getMasterType())
                || !pointUsageRuleInfo.getMasterCode().equals(dto.getMasterCode()))) {
            throw new ServiceException(RespCodeEnum.POINT_30002);
        }
        pointRuleInfoBiz.updateState(dto.getRuleId(), dto.getState(), dto.getOperator());
        return true;
    }

    @Override
    public void deletePointUsageRule(DeletePointUsageRuleDto dto) {
        PointUsageRuleInfo pointUsageRuleInfo = pointRuleInfoBiz.getById(dto.getRuleId());
        if (pointUsageRuleInfo == null || (!pointUsageRuleInfo.getMasterType().equals(dto.getMasterType())
                || !pointUsageRuleInfo.getMasterCode().equals(dto.getMasterCode()))) {
            throw new ServiceException(RespCodeEnum.POINT_30002);
        }
        pointRuleInfoBiz.deleteById(dto.getRuleId(), dto.getOperator());
    }

    @Override
    public List<BlocScopeUsageDto> listBlocScopeUsageRule(ListBlocScopeUsageDto dto) {
        PageUsageRuleScopeMappingBo query = pointUsageMedConverter.convertDtoToBo(dto);
        List<AssetUsageRuleScopeMapping> scopeMappings = usageRuleScopeMappingBiz.listByScopeSource(AssetTypeEnum.POINT.getType(), query);
        return scopeMappings.stream().map(pointUsageMedConverter::convertPoToBlocDto).collect(Collectors.toList());
    }

    @Override
    public List<MemberUsageRuleSaveResultDto> remind(BaseDto dto) {
        //获取全部的酒店信息
        List<SelectHotelResp> respList = hotelDecorator.selectHotels(dto.getBlocCode(), null, "", null);
        //过滤掉未知 筹建中，已停业的状态
        List<SelectHotelResp> hotelResps = respList.stream().filter(resp -> (resp.getOpenState().equals(HotelOpenStateEnum.SOFT_OPENING.getOpenState()) ||
                resp.getOpenState().equals(HotelOpenStateEnum.OPENING.getOpenState())
        )).collect(Collectors.toList());
        List<MemberUsageRuleSaveResultDto> allNeedConfigs = pointUsageMedConverter.convertConfig(hotelResps);
        List<BlocScopeUsageDto> resps = listBlocScopeUsageRule(pointUsageMedConverter.convertDtoToDto(dto));
        List<MemberUsageRuleSaveResultDto> unConfigs = new ArrayList<>();

        allNeedConfigs.forEach(config -> {
            boolean isConfig = resps.stream().anyMatch(resp -> {
                if (config.getScopeSource().equals(ScopeSourceEnum.BLOC.getCode())) {
                    return config.getScopePlatformChannel().equals(resp.getScopePlatformChannel());
                } else if (config.getScopeSource().equals(ScopeSourceEnum.HOTEL.getCode())) {
                    return config.getScopePlatformChannel().equals(resp.getScopePlatformChannel())
                            && config.getScopeHotelCode().equals(resp.getScopeHotelCode());
                } else {
                    return false;
                }
            });
            if (!isConfig) {
                unConfigs.add(config);
            }
        });
        return unConfigs;
    }
}
