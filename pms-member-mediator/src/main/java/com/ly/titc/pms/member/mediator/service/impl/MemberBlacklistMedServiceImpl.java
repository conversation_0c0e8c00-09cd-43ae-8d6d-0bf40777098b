package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.util.IdUtil;
import com.google.common.collect.Lists;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.ecrm.dubbo.enums.StateEnum;
import com.ly.titc.pms.member.biz.MemberBlacklistApplicableDataMappingBiz;
import com.ly.titc.pms.member.biz.MemberBlacklistInfoBiz;
import com.ly.titc.pms.member.com.enums.BlacklistApplicationTypeEnum;
import com.ly.titc.pms.member.com.enums.BlacklistSceneEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.entity.po.MemberBlacklistApplicableDataMapping;
import com.ly.titc.pms.member.dal.entity.po.MemberBlacklistInfo;
import com.ly.titc.pms.member.dubbo.entity.message.MemberEventMsg;
import com.ly.titc.pms.member.dubbo.enums.MemberEventEnum;
import com.ly.titc.pms.member.mediator.converter.MemberBlacklistMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.*;
import com.ly.titc.pms.member.mediator.entity.dto.general.DictDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileTagInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.service.MemberBlacklistMedService;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.member.mediator.service.MessageMedService;
import com.ly.titc.pms.spm.dubbo.enums.MasterTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员黑名单
 *
 * <AUTHOR>
 * @date 2024/12/19 11:47
 */
@Slf4j
@Service
public class MemberBlacklistMedServiceImpl implements MemberBlacklistMedService {

    @Resource
    private MemberBlacklistInfoBiz memberBlacklistInfoBiz;

    @Resource
    private MemberBlacklistApplicableDataMappingBiz memberBlacklistApplicableDataMappingBiz;

    @Resource
    private MemberBlacklistMedConverter memberBlacklistMedConverter;

    @Resource
    private MessageMedService messageMedService;

    @Resource
    private MemberMedService memberMedService;

    @Resource
    private HotelDecorator hotelDecorator;

    @Override
    public String blacklist(BlacklistMemberDto dto) {
        String blacklistNo = IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextIdStr();
        MemberBlacklistInfo blacklistInfo = memberBlacklistMedConverter.convertDtoToPo(dto);
        blacklistInfo.setBlacklistNo(blacklistNo);
        blacklistInfo.setState(StateEnum.VALID.getCode());
        memberBlacklistInfoBiz.insert(blacklistInfo);
        if (!CollectionUtils.isEmpty(dto.getScenes()) || !CollectionUtils.isEmpty(dto.getPlatformChannels())) {
            List<MemberBlacklistApplicableDataMapping> mappings = new ArrayList<>();
            mappings.addAll(assembleSceneMappings(dto.getScenes(), blacklistNo));
            mappings.addAll(assembleChannelMappings(dto.getPlatformChannels(), blacklistNo));
            memberBlacklistApplicableDataMappingBiz.insertBatch(mappings);
        }

        // 发送事件消息
        MemberEventMsg memberEventMsg = new MemberEventMsg();
        memberEventMsg.setMasterType(dto.getMasterType()).setMasterCode(dto.getMasterCode())
                .setMemberNo(dto.getMemberNo()).setEventType(MemberEventEnum.BLACKLIST);
        messageMedService.sendMemberEventMsg(TraceNoUtil.getTraceNo(), memberEventMsg);
        return blacklistNo;
    }

    @Override
    public String cancelBlacklist(CancelBlacklistMemberDto dto) {
        String blacklistNo = dto.getBlacklistNo();
        String memberNo = dto.getMemberNo();

        MemberBlacklistInfo blacklistInfo = memberBlacklistInfoBiz.getByBlacklistNo(memberNo, blacklistNo);
        if (blacklistInfo == null || blacklistInfo.getState().equals(StateEnum.NO_VALID.getCode())) {
            return blacklistNo;
        }

        memberBlacklistInfoBiz.updateState(memberNo, blacklistNo, StateEnum.NO_VALID.getCode(), dto.getOperator());

        // 发送事件消息
        MemberEventMsg memberEventMsg = new MemberEventMsg();
        memberEventMsg.setMasterType(dto.getMasterType()).setMasterCode(dto.getMasterCode())
                .setMemberNo(dto.getMemberNo()).setEventType(MemberEventEnum.CANCEL_BLACKLIST);
        messageMedService.sendMemberEventMsg(TraceNoUtil.getTraceNo(), memberEventMsg);
        return blacklistNo;
    }

    @Override
    public List<BlacklistInfoDto> listBlacklist(ListBlacklistParamDto dto) {

        MemberInfoDto memberInfo = memberMedService.getByMemberNo(dto.getMemberNo());
        if (memberInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10011);
        }

        List<MemberBlacklistInfo> blacklistInfos = memberBlacklistInfoBiz.listBlacklist(dto.getMemberNo(), dto.getState());

        if (CollectionUtils.isEmpty(blacklistInfos)) {
            return Lists.newArrayList();
        }
        List<String> blacklistNos = blacklistInfos.stream().map(MemberBlacklistInfo::getBlacklistNo).collect(Collectors.toList());
        List<MemberBlacklistApplicableDataMapping> customerBlacklistApplicableDataMappings = memberBlacklistApplicableDataMappingBiz.listApplicableDataMapping(blacklistNos, BlacklistApplicationTypeEnum.SCENE.getType());
        Map<String, List<DictDto>> applicableMappingMap = customerBlacklistApplicableDataMappings.stream().collect(Collectors.groupingBy(MemberBlacklistApplicableDataMapping::getBlacklistNo, Collectors.mapping(e -> new DictDto().setValue(e.getScopeValue()).setName(BlacklistSceneEnum.getDescByType(e.getScopeValue())), Collectors.toList())));

        List<MemberBlacklistApplicableDataMapping> channelBlacklistApplicableDataMappings = memberBlacklistApplicableDataMappingBiz.listApplicableDataMapping(blacklistNos, BlacklistApplicationTypeEnum.PLATFORM_CHANNEL.getType());
        Map<String, List<DictDto>> channelApplicableMappingMap = channelBlacklistApplicableDataMappings.stream().collect(Collectors.groupingBy(MemberBlacklistApplicableDataMapping::getBlacklistNo,Collectors.mapping(e -> new DictDto().setValue(e.getScopeValue()).setName(PlatformChannelEnum.getByPlatformChannel(e.getScopeValue()).getPlatformChannelDesc()), Collectors.toList())));

        List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(memberInfo.getBlocCode(), new ArrayList<>());
        Map<String, String> hotelMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, HotelBaseInfoResp::getHotelName, (v1, v2) -> v1));

        return blacklistInfos.stream().map(e -> memberBlacklistMedConverter.convertPoToDto(e, applicableMappingMap.get(e.getBlacklistNo()), channelApplicableMappingMap.get(e.getBlacklistNo()), hotelMap.getOrDefault(e.getSource(), "集团"))).collect(Collectors.toList());
    }

    @Override
    public boolean checkWhetherBlacklist(String memberNo, String scene, String platformChannel) {

        List<MemberBlacklistInfo> blacklistInfos = memberBlacklistInfoBiz.listBlacklist(memberNo, StateEnum.VALID.getCode());
        if (!CollectionUtils.isEmpty(blacklistInfos)) {
            if (StringUtils.isBlank(scene) && StringUtils.isBlank(platformChannel)) {
                return true;
            }
            // 渠道&场景校验
            return verifyScene(scene, blacklistInfos) || verifyPlatformChannel(platformChannel, blacklistInfos);
        }
        return false;
    }

    @Override
    public List<String> listByMemberNos(List<String> memberNos) {

        List<MemberBlacklistInfo> blacklistInfos = memberBlacklistInfoBiz.listBlacklist(memberNos, StateEnum.VALID.getCode());
        return blacklistInfos.stream().map(MemberBlacklistInfo::getMemberNo).distinct().collect(Collectors.toList());
    }

    private boolean verifyScene(String scene, List<MemberBlacklistInfo> blacklistInfos) {
        if (StringUtils.isBlank(scene)) {
            return false;
        }
        Optional<MemberBlacklistInfo> optional = blacklistInfos.stream().filter(e -> e.getSceneScope().equals(Constant.ONE)).findFirst();
        if (!optional.isPresent()) {
            // 部分选择选择了该场景
            List<String> blacklistNos = blacklistInfos.stream().map(MemberBlacklistInfo::getBlacklistNo).collect(Collectors.toList());
            List<MemberBlacklistApplicableDataMapping> customerBlacklistApplicableDataMappings = memberBlacklistApplicableDataMappingBiz.listApplicableDataMapping(blacklistNos, BlacklistApplicationTypeEnum.SCENE.getType());
            List<String> scenes = customerBlacklistApplicableDataMappings.stream().map(MemberBlacklistApplicableDataMapping::getScopeValue).distinct().collect(Collectors.toList());
            return scenes.contains(scene);
        } else {
            return false;
        }
    }

    private boolean verifyPlatformChannel(String platformChannel, List<MemberBlacklistInfo> blacklistInfos) {
        if (StringUtils.isBlank(platformChannel)) {
            return false;
        }
        Optional<MemberBlacklistInfo> optional = blacklistInfos.stream().filter(e -> e.getPlatformChannelScope().equals(Constant.ONE)).findFirst();
        if (!optional.isPresent()) {
            // 部分选择选择了该渠道
            List<String> blacklistNos = blacklistInfos.stream().map(MemberBlacklistInfo::getBlacklistNo).collect(Collectors.toList());
            List<MemberBlacklistApplicableDataMapping> customerBlacklistApplicableDataMappings = memberBlacklistApplicableDataMappingBiz.listApplicableDataMapping(blacklistNos, BlacklistApplicationTypeEnum.PLATFORM_CHANNEL.getType());
            List<String> platformChannels = customerBlacklistApplicableDataMappings.stream().map(MemberBlacklistApplicableDataMapping::getScopeValue).distinct().collect(Collectors.toList());
            return platformChannels.contains(platformChannel);
        } else {
            return false;
        }
    }

    private static List<MemberBlacklistApplicableDataMapping> assembleSceneMappings(List<String> scenes, String blacklistNo) {
        if (CollectionUtils.isEmpty(scenes)) {
            return Lists.newArrayList();
        }
        return scenes.stream().map(scene -> {
            MemberBlacklistApplicableDataMapping mapping = new MemberBlacklistApplicableDataMapping();
            mapping.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
            mapping.setBlacklistNo(blacklistNo);
            mapping.setApplicationType(BlacklistApplicationTypeEnum.SCENE.getType());
            mapping.setScopeValue(scene);
            return mapping;
        }).collect(Collectors.toList());
    }

    private static List<MemberBlacklistApplicableDataMapping> assembleChannelMappings(List<String> platformChannels, String blacklistNo) {
        if (CollectionUtils.isEmpty(platformChannels)) {
            return Lists.newArrayList();
        }
        return platformChannels.stream().map(platformChannel -> {
            MemberBlacklistApplicableDataMapping mapping = new MemberBlacklistApplicableDataMapping();
            mapping.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
            mapping.setBlacklistNo(blacklistNo);
            mapping.setApplicationType(BlacklistApplicationTypeEnum.PLATFORM_CHANNEL.getType());
            mapping.setScopeValue(platformChannel);
            return mapping;
        }).collect(Collectors.toList());
    }
}
