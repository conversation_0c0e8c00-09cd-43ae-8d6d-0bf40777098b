package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.pms.member.mediator.entity.dto.point.PointConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.point.SavePointConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.store.SaveStoreConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.store.StoreConfigDto;

/**
 * 储值配置
 *
 * <AUTHOR>
 * @date 2025/6/26 10:34
 */
public interface StoreConfigMedService {

    /**
     * 保存储值设置
     *
     * @param dto
     */
    void saveStoreConfig(SaveStoreConfigDto dto);

    /**
     * 获取储值设置
     *
     * @param blocCode
     * @param masterType
     * @param masterCode
     * @return
     */
    StoreConfigDto getStoreConfig(String blocCode, Integer masterType, String masterCode);

}
