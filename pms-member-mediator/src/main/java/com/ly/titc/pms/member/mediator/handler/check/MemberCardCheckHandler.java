package com.ly.titc.pms.member.mediator.handler.check;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.ecrm.dubbo.enums.StateEnum;
import com.ly.titc.pms.member.com.enums.MemberRegisterCheckEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Optional;

/**
 * 会员卡校验
 *
 * <AUTHOR>
 * @date 2024/12/18 11:46
 */
@Component
@Slf4j
public class MemberCardCheckHandler extends AbstractRegisterCheckHandler{

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private MemberCardInfoDecorator memberCardInfoDecorator;

    @Override
    public Integer getAction() {
        return MemberRegisterCheckEnum.MEMBER_CARD.getAction();
    }

    @Override
    public void check(RegisterMemberDto dto) {
        Integer masterType = dto.getMasterType();
        String masterCode = dto.getMasterCode();
        IssueMemberCardDto memberCardInfo = dto.getMemberCardInfo();
        Long cardId = memberCardInfo.getCardId();
        String memberCardNo = memberCardInfo.getMemberCardNo();
        // 会员卡是否存在
        MemberCardConfigResp memberCardConfig = memberCardInfoDecorator.getById(cardId);
        if (memberCardConfig == null || memberCardConfig.getState().equals(StateEnum.NO_VALID.getCode())) {
            log.error("会员注册check 会员卡不存在或已停用，cardId:{}", cardId);
            throw new ServiceException(RespCodeEnum.MEMBER_10051);
        }
        Integer cardLevel = memberCardInfo.getCardLevel();
        // 会员卡等级是否存在
        Optional<MemberCardLevelConfigInfoResp> optional = memberCardConfig.getCardLevelConfigs().stream()
                .filter(e -> e.getState().equals(StateEnum.VALID.getCode()) && e.getCardLevel().equals(cardLevel)).findFirst();
        if (!optional.isPresent()) {
            log.error("会员注册check 会员卡等级不存在，cardId:{}, cardLevel:{}", cardId, cardLevel);
            throw new ServiceException(RespCodeEnum.MEMBER_10014);
        }
        // 会员卡号是否被占用
        boolean cardNoExistFlag = memberCardMedService.checkCardNoExist(masterType, masterCode, memberCardNo);
        if (cardNoExistFlag) {
            log.error("会员注册check 会员卡号已存在，cardId:{}, cardNo:{}",cardId, memberCardNo);
            throw new ServiceException(RespCodeEnum.MEMBER_10040);
        }
        // 填充数据
        MemberCardLevelConfigInfoResp cardLevelConfig = optional.get();
        memberCardInfo.setIssueUser(dto.getOperator());
        memberCardInfo.setCardType(memberCardConfig.getCardType());
        memberCardInfo.setCardLevelName(cardLevelConfig.getCardLevelName());
        memberCardInfo.setIssueHotelType(dto.getRegisterHotelType());
        memberCardInfo.setIssueHotel(dto.getRegisterHotel());
        // 会员卡等级的有效期时间填充
        if (StringUtils.isBlank(memberCardInfo.getEffectBeginDate()) || StringUtils.isBlank(memberCardInfo.getEffectEndDate())) {
            memberCardInfo.setEffectBeginDate(LocalDateUtil.formatByNormalDate(LocalDate.now()));
            memberCardInfo.setEffectEndDate(LocalDateUtil.formatByNormalDate(LocalDate.now().plusDays(cardLevelConfig.getValidPeriod())));
            memberCardInfo.setIsLongTerm(cardLevelConfig.getIsLongTerm());
        }
    }
}
