package com.ly.titc.pms.member.mediator.entity.dto.store;

import com.ly.titc.pms.ecrm.dubbo.entity.BasePageReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class PageStoreUsageDto extends BasePageReq {

    /**
     * 归属主体类型
     */
    private Integer masterType;

    /**
     * 归属主体值
     */
    private String masterCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 适用来源
     */
    private List<String> scopeSources;

    /**
     * 适用平台渠道
     */
    private List<String> scopePlatformChannels;

    /**
     * 适用门店编码
     */
    private List<String> scopeHotelCodes;


    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

}
