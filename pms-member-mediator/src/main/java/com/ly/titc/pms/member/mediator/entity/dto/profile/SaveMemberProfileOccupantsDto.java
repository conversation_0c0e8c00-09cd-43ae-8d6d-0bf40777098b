package com.ly.titc.pms.member.mediator.entity.dto.profile;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 常用入住人
 *
 * <AUTHOR>
 * @date 2024/11/19 17:55
 */
@Data
public class SaveMemberProfileOccupantsDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 入住人编号
     */
    private Long occupantsNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 证件号分类
     */
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 标签
     */
    private String tag;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

}
