package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 发放会员卡请求DTO
 *
 * <AUTHOR>
 * @date 2025年06月25日11:10:08
 */
@Data
@Accessors(chain = true)
public class IssueMemberCardRequestDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    private Integer masterType;

    /**
     * 归属值
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 客户编号（客人转会员时传入）
     */
    private String customerNo;

    /**
     * 会员号（已有会员升级时传入）
     */
    private String memberNo;

    /**
     * 是否发放默认卡
     * 0-否 1-是
     */
    private Integer defaultCard;

    /**
     * 会员卡ID（默认卡type为0，则需要传入发放的会员卡id）
     */
    private Long cardId;

    /**
     * 需要发放的会员卡等级
     */
    @NotNull(message = "需要发放的会员卡等级不能为空")
    private Integer cardLevel;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空")
    private String bizType;

    /**
     * 业务id(必传)
     */
    @NotBlank(message = "业务ID不能为空")
    private String bizNo;

    /**
     * 发放原因
     */
    private String reason;
}
