package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;

import java.io.Serializable;

/**
 * 会员联系方式信息
 *
 * @TableName member_contact_info
 */
@Data
public class MemberContactInfoDto implements Serializable {

    /**
     * 邮箱
     */
    private String email;

    /**
     * QQ号
     */
    private String qq;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 国家id
     */
    private Integer countryId;

    /**
     * 国家
     */
    private String country;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域id
     */
    private Integer districtId;

    /**
     * 区域
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;

}