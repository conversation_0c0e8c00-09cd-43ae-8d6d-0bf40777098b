package com.ly.titc.pms.member.mediator.converter;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.hotel.SelectHotelResp;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.usageRule.*;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberPointUsageRuleResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberStoreUsageRuleResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberUsageRuleSaveResultResp;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 14:38
 */
@Mapper(componentModel = "spring")

public interface MemberUsageMedConverter {

    QueryMemberMasterUsageReq convert(QueryMemberMasterUsageDto dto);



    default MemberPointUsageRuleDto convert(MemberPointUsageRuleResp resp, Map<Long, SelectHotelResp> hotelRespMap ){
        MemberPointUsageRuleDto ruleDto = convertBase(resp);
         List<CodeDto> scopePlatformChannels = new ArrayList<>();
        List<CodeDto>  scopeHotelCodes = new ArrayList<>();
        List<CodeDto> usageHotelCodes = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(resp.getScopePlatformChannels())) {
            resp.getScopePlatformChannels().forEach(s -> {
                PlatformChannelEnum channelEnum = PlatformChannelEnum.getByPlatformChannel(s);
                if(channelEnum!=null) {
                    CodeDto codeDto = new CodeDto();
                    codeDto.setCode(s);
                    codeDto.setName(channelEnum.getPlatformChannelDesc());
                    codeDto.setParentCode(channelEnum.getPlatform());
                    codeDto.setParentName(channelEnum.getPlatformDesc());
                    scopePlatformChannels.add(codeDto);
                }
            });
        }
        if(CollectionUtils.isNotEmpty(resp.getScopeHotelCodes())){
            resp.getScopeHotelCodes().forEach(s -> {
                CodeDto codeDto = new CodeDto();
                codeDto.setCode(s);
                codeDto.setName(hotelRespMap.get(Long.valueOf(s)).getHotelName());
                scopeHotelCodes.add(codeDto);
            });
        }
        if(CollectionUtils.isNotEmpty(resp.getUsageHotelCodes())){
            resp.getUsageHotelCodes().forEach(s -> {
                CodeDto codeDto = new CodeDto();
                codeDto.setCode(s);
                codeDto.setName(hotelRespMap.get(Long.valueOf(s)).getHotelName());
                usageHotelCodes.add(codeDto);
            });
        }
        ruleDto.setScopePlatformChannels(scopePlatformChannels);
        ruleDto.setScopeHotelCodes(scopeHotelCodes);
        ruleDto.setUsageHotelCodes(usageHotelCodes);
        return ruleDto;
    }

    default MemberStoreUsageRuleDto convert(MemberStoreUsageRuleResp resp, Map<Long,SelectHotelResp> hotelRespMap ){
        MemberStoreUsageRuleDto ruleDto = convertBase(resp);
        List<CodeDto> scopePlatformChannels = new ArrayList<>();
        List<CodeDto>  scopeHotelCodes = new ArrayList<>();
        List<CodeDto> usageHotelCodes = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(resp.getScopePlatformChannels())) {
            resp.getScopePlatformChannels().forEach(s -> {
                PlatformChannelEnum channelEnum = PlatformChannelEnum.getByPlatformChannel(s);
                if(channelEnum!=null) {
                    CodeDto codeDto = new CodeDto();
                    codeDto.setCode(s);
                    codeDto.setName(channelEnum.getPlatformChannelDesc());
                    codeDto.setParentCode(channelEnum.getPlatform());
                    codeDto.setParentName(channelEnum.getPlatformDesc());
                    scopePlatformChannels.add(codeDto);
                }
            });
        }
        if(CollectionUtils.isNotEmpty(resp.getScopeHotelCodes())){
            resp.getScopeHotelCodes().forEach(s -> {
                CodeDto codeDto = new CodeDto();
                codeDto.setCode(s);
                codeDto.setName(hotelRespMap.get(Long.valueOf(s)).getHotelName());
                scopeHotelCodes.add(codeDto);
            });
        }
        if(CollectionUtils.isNotEmpty(resp.getUsageHotelCodes())){
            resp.getUsageHotelCodes().forEach(s -> {
                CodeDto codeDto = new CodeDto();
                codeDto.setCode(s);
                codeDto.setName(hotelRespMap.get(Long.valueOf(s)).getHotelName());
                usageHotelCodes.add(codeDto);
            });
        }
        ruleDto.setScopePlatformChannels(scopePlatformChannels);
        ruleDto.setScopeHotelCodes(scopeHotelCodes);
        ruleDto.setUsageHotelCodes(usageHotelCodes);
        return ruleDto;
    }

    @Mappings({
            @Mapping(target = "scopePlatformChannels", source = "scopePlatformChannels",ignore = true),
            @Mapping(target = "scopeHotelCodes", source = "scopeHotelCodes",ignore = true),
            @Mapping(target = "usageHotelCodes", source = "usageHotelCodes",ignore = true),

    })
    MemberStoreUsageRuleDto convertBase(MemberStoreUsageRuleResp resp);
    @Mappings({
            @Mapping(target = "scopePlatformChannels", source = "scopePlatformChannels",ignore = true),
            @Mapping(target = "scopeHotelCodes", source = "scopeHotelCodes",ignore = true),
            @Mapping(target = "usageHotelCodes", source = "usageHotelCodes",ignore = true),

    })
    MemberPointUsageRuleDto convertBase(MemberPointUsageRuleResp resp);

    MemberPointUsageRuleConfigSaveReq convert(MemberPointUsageRuleConfigSaveDto dto);

    default List<MemberUsageRuleSaveResultDto> convert(List<MemberUsageRuleSaveResultResp>  resps,Map<Long, HotelBaseInfoResp> hotelRespMap){
        List<MemberUsageRuleSaveResultDto> resultDtos = new ArrayList<>();
        resps.forEach(r->{
            MemberUsageRuleSaveResultDto resultDto = convertBase(r);
            resultDto.setScopePlatformChannelName(PlatformChannelEnum.getByPlatformChannel(r.getScopePlatformChannel()).getPlatformChannelDesc());
            if(StringUtils.isNotEmpty(r.getScopeHotelCode())) {
                resultDto.setScopeHotelName(hotelRespMap.get(Long.valueOf(r.getScopeHotelCode())).getHotelName());
            }
            resultDtos.add(resultDto);
        });
        return resultDtos;
    }

   default MemberUsageRuleSaveResultDto convert(MemberUsageRuleSaveResultResp resp,String hotelName){
        MemberUsageRuleSaveResultDto resultDto = convertBase(resp);
       resultDto.setScopePlatformChannelName(PlatformChannelEnum.getByPlatformChannel(resp.getScopePlatformChannel()).getPlatformChannelDesc());
        resultDto.setScopeHotelName(hotelName);
        return resultDto;

    }
    MemberUsageRuleSaveResultDto convertBase(MemberUsageRuleSaveResultResp resp);

    UpdateMemberUsageStateReq convert(UpdateMemberUsageStateDto dto);

    DeleteMemberUsageReq  convert(DeleteMemberUsageDto dto);

    MemberStoreUsageRuleConfigSaveReq convert(MemberStoreUsageRuleConfigSaveDto dto);

    QueryMemberBlocScopeUsageReq convert(BaseDto dto);

   default List<MemberUsageRuleSaveResultDto> convertConfig(List<SelectHotelResp> hotelResps){
       List<MemberUsageRuleSaveResultDto> resultDtos = new ArrayList<>();
       PlatformChannelEnum.listAllPlatformChannel().forEach(s -> {
           //集团维度
           MemberUsageRuleSaveResultDto resultDto = new MemberUsageRuleSaveResultDto();
           resultDto.setScopePlatformChannel(s);
           resultDto.setScopePlatformChannelName(PlatformChannelEnum.getByPlatformChannel(s).getPlatformChannelDesc());
           resultDto.setScopeSource(ScopeSourceEnum.BLOC.getCode());
           resultDtos.add(resultDto);
           //酒店维度
           hotelResps.forEach(resp -> {
               MemberUsageRuleSaveResultDto hotelDto = new MemberUsageRuleSaveResultDto();
               hotelDto.setScopeSource(ScopeSourceEnum.HOTEL.getCode());
               hotelDto.setScopeHotelCode(resp.getHotelVid().toString());
               hotelDto.setScopeHotelName(resp.getHotelName());
               hotelDto.setScopePlatformChannel(s);
               hotelDto.setScopePlatformChannelName(PlatformChannelEnum.getByPlatformChannel(s).getPlatformChannelDesc());
               resultDtos.add(hotelDto);
           });

       });
       return resultDtos;
   }
}
