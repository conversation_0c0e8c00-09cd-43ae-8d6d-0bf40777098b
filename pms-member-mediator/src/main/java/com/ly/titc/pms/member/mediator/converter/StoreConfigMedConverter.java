package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.member.dal.entity.po.StoreConfigInfo;
import com.ly.titc.pms.member.mediator.entity.dto.store.SaveStoreConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.store.StoreConfigDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @date 2025/6/26 10:43
 */
@Mapper(componentModel = "spring")
public interface StoreConfigMedConverter {

    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator"),
    })
    StoreConfigInfo convertDtoToPo(SaveStoreConfigDto dto);

    StoreConfigDto convertPoToDto(StoreConfigInfo blocConfig);
}
