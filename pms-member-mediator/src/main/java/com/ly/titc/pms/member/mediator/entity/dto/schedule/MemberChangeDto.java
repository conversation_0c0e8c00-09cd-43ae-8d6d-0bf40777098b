package com.ly.titc.pms.member.mediator.entity.dto.schedule;

import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：RelegationFailDto
 * @Date：2024-11-21 20:56
 * @Filename：RelegationFailDto
 */
@Data
public class MemberChangeDto {

    private List<String> memberNo;

    private Integer sourceLevel;

    private Integer targetLevel;

    private Long cardId;

    // 1 升级 2 降级 3 删除标签 4 新增标签
    private Integer type;

    private Long tagId;
}
