package com.ly.titc.pms.member.mediator.rpc.dsf.chm;

import com.ly.titc.chm.api.ChannelInfoService;
import com.ly.titc.chm.entity.request.BaseBlocReq;
import com.ly.titc.chm.entity.request.UpdateHotelChannelReq;
import com.ly.titc.chm.entity.response.BlocChannelResp;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.api.BlocService;
import com.ly.titc.pms.member.mediator.rpc.dsf.AbstractDsfServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * 渠道
 *
 * <AUTHOR>
 * @date 2024/12/30 10:03
 */
@Slf4j
@Component
public class ChmChannelDecorator extends AbstractDsfServiceProxy {

//
//    @DubboReference(protocol = "dsf", providedBy = "${chm-dsf-group}", version = "${chm-dsf-service-version}")
//    private ChannelInfoService channelInfoService;

    private static final String S_NAME = "channel";



    public List<BlocChannelResp> listBlocChannel(String blocCode){
        BaseBlocReq req = new BaseBlocReq();
        req.setBlocCode(blocCode);
        req.setTrackingId(UUID.randomUUID().toString());
        ChannelInfoService channelInfoService = getProxy(ChannelInfoService.class, CHM_DSF_GS_NAME, S_NAME, chmVersion);
        return Response.getValidateData(channelInfoService.listBlocChannel(req));
    }

    public Boolean update(List<UpdateHotelChannelReq> req) {
        ChannelInfoService channelInfoService = getProxy(ChannelInfoService.class, CHM_DSF_GS_NAME, S_NAME, chmVersion);
        return Response.getValidateData(channelInfoService.update(req));
    }
}
