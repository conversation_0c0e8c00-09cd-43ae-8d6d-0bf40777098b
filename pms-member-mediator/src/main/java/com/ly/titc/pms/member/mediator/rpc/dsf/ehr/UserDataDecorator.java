package com.ly.titc.pms.member.mediator.rpc.dsf.ehr;

import com.alibaba.fastjson.JSON;
import com.ly.spat.dsf.annoation.RequestBody;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.trace.TraceIdGenerator;
import com.ly.titc.ehr.api.UserDataService;
import com.ly.titc.ehr.entity.request.user.ListUserByIdsReq;
import com.ly.titc.ehr.entity.request.user.ListUserByRoleNameReq;
import com.ly.titc.ehr.entity.request.user.ListUserDataReq;
import com.ly.titc.ehr.entity.request.user.listUserDivisionReq;
import com.ly.titc.ehr.entity.response.UserDetailResp;
import com.ly.titc.ehr.entity.response.UserDivisionResp;
import com.ly.titc.pms.member.mediator.rpc.dsf.AbstractDsfServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：UserDataDecorator
 * @Date：2024-9-4 19:34
 * @Filename：UserDataDecorator
 */
@Slf4j
@Component
public class UserDataDecorator extends AbstractDsfServiceProxy {

    private static final String S_NAME = "userData";

    /**
     * 根据用户id查询用户信息
     * @param tenantCode 租户code
     * @param blocCode 集团code
     * @param userIdList 用户id列表
     * @return 用户信息列表
     */
    public List<UserDetailResp> listUser(String tenantCode, String blocCode, List<Long> userIdList) {
        ListUserByIdsReq req = new ListUserByIdsReq();
        req.setBlocCode(blocCode);
        req.setUserIds(userIdList);
        req.setTenantCode(tenantCode);
        req.setTrackingId(TraceIdGenerator.getTraceId());
        UserDataService userDataService =  getProxy(UserDataService.class, EHR_DSF_GS_NAME, S_NAME, ehrVersion);
        return Response.getValidateData(userDataService.listUserByIds(req));
    }

    /**
     * 根据角色名称查询用户信息
     */
    public List<UserDetailResp> listUserByRoleName(ListUserByRoleNameReq var1) {
        UserDataService userDataService =  getProxy(UserDataService.class, EHR_DSF_GS_NAME, S_NAME, ehrVersion);
        return Response.getValidateData(userDataService.listUserByRoleName(var1));
    }


    /**
     * 获取权限内门店集合
     *
     * @param blocCode 集团编号
     * @param trackingId
     * @param tenantCode
     * @param userId
     * @return
     */
    public List<String> listHotelIdsByUserId(String blocCode, String tenantCode, Long userId, String trackingId) {
        ListUserDataReq req = new ListUserDataReq();
        req.setUserId(userId);
        req.setBlocCode(blocCode);
        req.setTrackingId(trackingId);
        req.setTenantCode(tenantCode);
        UserDataService userDataService =  getProxy(UserDataService.class, EHR_DSF_GS_NAME, S_NAME, ehrVersion);
        Response<List<Long>> userBlocManagerList = userDataService.listHotelIdsByUserId(req);
        List<Long> hotelVids = Response.getValidateData(userBlocManagerList);
        return hotelVids.stream().map(String::valueOf).collect(Collectors.toList());
    }


    public UserDivisionResp listDivisionByUserId(String blocCode, String tenantCode, Long userId, String trackingId){
        listUserDivisionReq req = new listUserDivisionReq();
        req.setBlocCode(blocCode);
        req.setUserId(userId);
        req.setTenantCode(tenantCode);
        req.setTrackingId(trackingId);
        UserDataService userDataService =  getProxy(UserDataService.class, EHR_DSF_GS_NAME, S_NAME, ehrVersion);
        Response<UserDivisionResp> userDivisionRespResponse = userDataService.listDivisionByUserId(req);
        log.info("===listDivisionByUserId===请求参数:{},返回值:{}",JSON.toJSONString(req),JSON.toJSONString(userDivisionRespResponse));
        return Response.getValidateData(userDivisionRespResponse);
    }
}
