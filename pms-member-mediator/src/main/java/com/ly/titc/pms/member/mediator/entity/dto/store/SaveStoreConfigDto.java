package com.ly.titc.pms.member.mediator.entity.dto.store;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 保存储值配置
 *
 * <AUTHOR>
 * @date 2025/5/9
 */
@Data
@Accessors(chain = true)
public class SaveStoreConfigDto {

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 集团组code  集团code 门店code
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 储值支付是否需要验证 (0-否, 1-是)
     */
    private Integer isPayVerifyRequired;

    /**
     * 是否允许酒店修改验证设置 (仅集团层级有效)
     */
    private Integer allowPayVerifyModify;

    /**
     * 是否支持他卡付款 (0-否, 1-是)
     */
    private Integer isSupportOtherCard;

    /**
     * 是否允许酒店修改他卡付款设置 (仅集团层级有效)
     */
    private Integer allowOtherCardModify;

    /**
     * 操作人
     */
    private String operator;
}
