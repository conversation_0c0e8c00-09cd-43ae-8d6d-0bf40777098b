package com.ly.titc.pms.member.mediator.rpc.dsf.chm;

import com.ly.titc.chm.api.RatePlanInfoService;
import com.ly.titc.chm.entity.request.BlocRatePlanListByRentModeReq;
import com.ly.titc.chm.entity.request.ListBlocRatePlanReq;
import com.ly.titc.chm.entity.response.BlocRatePlanInfoResp;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.mediator.rpc.dsf.AbstractDsfServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2025-2-7
 */
@Slf4j
@Component
public class ChmRatePlanDecorator extends AbstractDsfServiceProxy {

    private static final String S_NAME = "ratePlan";

    public List<BlocRatePlanInfoResp> listBlocRatePlanByRentMode(String blocCode, Integer rentMode){
        BlocRatePlanListByRentModeReq req = new BlocRatePlanListByRentModeReq();
        req.setBlocCode(blocCode);
        req.setRentMode(rentMode);
        req.setTrackingId(UUID.randomUUID().toString());
        RatePlanInfoService ratePlanInfoService = getProxy(RatePlanInfoService.class, CHM_DSF_GS_NAME, S_NAME, chmVersion);
        return Response.getValidateData(ratePlanInfoService.listBlocPlanByRentMode(req));
    }


    public List<BlocRatePlanInfoResp> listBlocRatePlan(String blocCode, List<String> ratePlanCodes){
        ListBlocRatePlanReq req = new ListBlocRatePlanReq();
        req.setBlocCode(blocCode);
        req.setRatePlanCodes(ratePlanCodes);
        RatePlanInfoService ratePlanInfoService = getProxy(RatePlanInfoService.class, CHM_DSF_GS_NAME, S_NAME, chmVersion);
        return Response.getValidateData(ratePlanInfoService.listBlocPlan(req));
    }
}
