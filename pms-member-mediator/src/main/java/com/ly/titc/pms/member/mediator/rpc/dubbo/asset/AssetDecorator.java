package com.ly.titc.pms.member.mediator.rpc.dubbo.asset;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.PageMemberPointsFlowMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.ListRechargeConsumeRecordMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.PageMemberStoreConsumeMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberTotalPointResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeRecordResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTotalAmountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTradeConsumeRecordResp;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberPointsDubboService;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberStoreDubboService;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberStoreRechargeConsumeDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author：rui
 * @name：AssetDecoretor
 * @Date：2024-12-9 9:45
 * @Filename：AssetDecoretor
 */
@Slf4j
@Service
public class AssetDecorator {

    @DubboReference(group = "${asset-dsf-dubbo-group}", timeout = 30000)
    private MemberPointsDubboService memberPointsDubboService;

    @DubboReference(group = "${asset-dsf-dubbo-group}", timeout = 30000)
    private MemberStoreDubboService memberStoreDubboService;

    @DubboReference(group = "${asset-dsf-dubbo-group}", timeout = 30000)
    private MemberStoreRechargeConsumeDubboService memberStoreRechargeConsumeDubboService;

    /**
     * 分页查询会员消费、冻结记录
     */
    public Pageable<MemberStoreConsumeRecordResp> pageConsumeRecord(PageMemberStoreConsumeMemberReq req) {
        req.setTrackingId(TraceNoUtil.getTraceNo());
        return Response.getValidateData(memberStoreRechargeConsumeDubboService.page(req));
    }

    /**
     * 翻页查询会员积分记录
     *
     * @param req
     * @return
     */
    public Pageable<MemberPointsFlowInfoResp> pagePointRecord(PageMemberPointsFlowMemberReq req) {
        req.setTrackingId(TraceNoUtil.getTraceNo());
        return Response.getValidateData(memberPointsDubboService.pageMemberPointsFlow(req));
    }

    /**
     * 查询充值单消费记录
     */
    public List<MemberTradeConsumeRecordResp> listRechargeConsumeRecord(String memberNo, List<String> tradeNoList, String trackingId) {
        ListRechargeConsumeRecordMemberReq req = new ListRechargeConsumeRecordMemberReq();
        req.setTrackingId(trackingId);
        req.setTradeNoList(tradeNoList);
        req.setMemberNo(memberNo);
        return Response.getValidateData(memberStoreRechargeConsumeDubboService.listRechargeConsumeRecord(req));
    }

    /**
     * 查询充值单消费记录
     */
    public List<MemberTradeConsumeRecordResp> listRechargeConsumeRecord(ListRechargeConsumeRecordMemberReq req) {
        req.setTrackingId(TraceNoUtil.getTraceNo());
        return Response.getValidateData(memberStoreRechargeConsumeDubboService.listRechargeConsumeRecord(req));
    }

    /**
     * 查询单个会员积分统计
     *
     * @param memberNo
     * @return
     */
    public MemberTotalPointResp getTotalAccountPoints(String memberNo) {
        BaseMemberReq req = new BaseMemberReq();
        req.setTrackingId(TraceNoUtil.getTraceNo());
        req.setMemberNo(memberNo);
        return Response.getValidateData(memberPointsDubboService.getTotalAccountPoints(req));
    }

    /**
     * 查询单个会员积分统计
     *
     * @param req
     * @return
     */
    public MemberTotalPointResp getTotalAccountPoints(BaseMemberReq req) {
        req.setTrackingId(TraceNoUtil.getTraceNo());
        return Response.getValidateData(memberPointsDubboService.getTotalAccountPoints(req));
    }

    /**
     * 查询单个会员储值统计
     *
     * @param memberNo
     * @return
     */
    public MemberTotalAmountResp getTotalAccountAmount(String memberNo) {
        BaseMemberReq req = new BaseMemberReq();
        req.setTrackingId(TraceNoUtil.getTraceNo());
        req.setMemberNo(memberNo);
        return Response.getValidateData(memberStoreDubboService.getTotalAccountAmount(req));
    }

    /**
     * 查询单个会员储值统计
     *
     * @param req
     * @return
     */
    public MemberTotalAmountResp getTotalAccountAmount(BaseMemberReq req) {
        req.setTrackingId(TraceNoUtil.getTraceNo());
        return Response.getValidateData(memberStoreDubboService.getTotalAccountAmount(req));
    }

    /**
     * 查询会员可用积分
     *
     * @param req
     * @return
     */
    public MemberPointAccountResp getPointUsableMasterAccount(GetMemberUsableReq req) {
        req.setTrackingId(TraceNoUtil.getTraceNo());
        return Response.getValidateData(memberPointsDubboService.getUsableMasterAccount(req));
    }


    /**
     * 查询会员可用储值
     *
     * @param req
     * @return
     */
    public MemberStoreAccountResp getStoreUsableMasterAccount(GetMemberUsableReq req) {
        req.setTrackingId(TraceNoUtil.getTraceNo());
        return Response.getValidateData(memberStoreDubboService.getUsableMasterAccount(req));
    }
}
