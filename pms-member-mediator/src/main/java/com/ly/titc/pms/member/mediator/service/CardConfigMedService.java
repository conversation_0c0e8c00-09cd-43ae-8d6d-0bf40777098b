package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.mediator.entity.dto.card.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 卡配置
 *
 * <AUTHOR>
 * @date 2025/6/25 13:42
 */
public interface CardConfigMedService {

    /**
     * 保存会员卡模板信息
     *
     * @return id
     */
    Long saveCardConfig(SaveCardConfigDto dto);

    /**
     * 查询默认卡
     *
     * @param masterType
     * @param masterCode
     * @return
     */
    CardConfigDto getDefaultCard(Integer masterType, String masterCode);

    /**
     * 查询卡列表
     *
     * @param cardIds
     * @return
     */
    List<CardConfigDto> listCardConfig(List<Long> cardIds);

    /**
     * 保存会员卡升级规则
     *
     * @return id
     */
    Long saveCardLevelUpgradeRule(SaveCardLevelUpgradeRuleDto dto);

    /**
     * 保存会员卡降级规则
     *
     * @return id
     */
    Long saveCardLevelRelegationRule(SaveCardLevelRelegationRuleDto dto);

    /**
     * 保存会员卡等级
     *
     * @return id
     */
    Long saveCardLevelConfig(SaveCardLevelConfigDto dto);

    /**
     * 删除会员卡
     *
     * @param cardId
     * @param operator
     * @return
     */
    void deleteCardConfig(Long cardId, String operator);

    /**
     * 删除会员卡等级
     *
     * @param cardId
     * @param cardLevel
     * @param operator
     * @return
     */
    void deleteCardLevelConfig(Long cardId, Integer cardLevel, String operator);

    /**
     * 查询卡等级配置
     *
     * @param dto
     * @return
     */
    List<CardLevelConfigDto> listCardLevelConfig(ListCardLevelConfigDto dto);

    /**
     * 分页查询会员等级
     *
     * @param dto
     * @return
     */
    Pageable<CardLevelConfigDto> pageCardLevelConfig(PageCardLevelConfigDto dto);

    /**
     * 查询会员卡等级详情
     *
     * @param levelId
     * @return
     */
    CardLevelConfigDto getCardLevelConfig(Long levelId);

    /**
     * 查询会员卡等级列表
     *
     * @param cardIds
     * @return 会员卡等级列表
     */
    List<CardLevelConfigDto> listMemberCardLevel(List<Long> cardIds);

    /**
     * 启停操作会员卡等级
     *
     * @param cardId
     * @param cardLevel
     * @param state
     * @param operator
     * @return
     */
    void actionCardLevel(Long cardId, Integer cardLevel, Integer state, String operator);

    /**
     * 根据归属查询会员卡模板
     *
     * @param masterType
     * @param masterCode
     * @param cardId
     * @return
     */
    List<CardConfigDto> listCardConfig(Integer masterType, String masterCode, Long cardId);

    /**
     * 查询卡详情
     *
     * @param cardId
     * @return
     */
    CardConfigDto getCardConfig(Long cardId);

    /**
     * 根据卡和等级查询升级规则
     *
     * @param dto
     * @return 升级规则
     */
    CardLevelUpgradeRuleDto getCardLevelUpgradeRule(GetCardLevelUpgradeRuleDto dto);

    /**
     * 批量查询卡升级规则
     *
     * @param cardIds
     * @param state
     * @return
     */
    List<CardLevelUpgradeRuleDto> listCardLevelUpgradeRule(List<Long> cardIds, Integer state);

    /**
     * 分页查询会员卡升级规则
     *
     * @param dto
     * @return 分页结果
     */
    Pageable<CardLevelUpgradeRuleDto> pageUpgradeRule(@Valid PageCardLevelUpgradeRuleDto dto);

    /**
     * 升级规则详情
     *
     * @param id
     * @return
     */
    CardLevelUpgradeRuleDto getUpgradeRule(Long id);

    /**
     * 根据卡和等级查询降级规则
     *
     * @param dto
     * @return 降级规则
     */
    CardLevelRelegationRuleDto getCardLevelRelegationRule(GetCardLevelRelegationRuleDto dto);

    /**
     * 批量查询卡保级规则
     *
     * @param cardIds
     * @param state
     * @return
     */
    List<CardLevelRelegationRuleDto> listCardLevelRelegationRule(List<Long> cardIds, Integer state);

    /**
     * 分页查询会员卡降级规则
     *
     * @param dto
     * @return 分页结果
     */
    Pageable<CardLevelRelegationRuleDto> pageRelegationRule(PageCardLevelRelegationRuleDto dto);

    /**
     * 保级规则详情
     *
     * @param id
     * @return
     */
    CardLevelRelegationRuleDto getRelegationRule(Long id);


    /**
     * 删除升级规则
     *
     * @param ruleId
     * @param operator
     * @return
     */
    void deleteCardLevelUpgradeRule(Long ruleId, String operator);

    /**
     * 启用停用升级规则
     *
     * @param ruleId
     * @param state
     * @param operator
     * @return
     */
    void actionCardLevelUpgradeRule(Long ruleId, Integer state, String operator);

    /**
     * 删除保级规则
     *
     * @param ruleId
     * @param operator
     * @return
     */
    void deleteCardLevelRelegationRule(Long ruleId, String operator);

    /**
     * 启停保级规则
     *
     * @param ruleId
     * @param state
     * @param operator
     * @return
     */
    void actionCardLevelRelegationRule(Long ruleId, Integer state, String operator);

}
