package com.ly.titc.pms.member.mediator.rpc.dubbo.message;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.unified.message.dubbo.api.SendService;
import com.ly.titc.unified.message.dubbo.api.SmsAccountDubboService;
import com.ly.titc.unified.message.dubbo.entity.request.send.MessageParam;
import com.ly.titc.unified.message.dubbo.entity.request.send.SendRequest;
import com.ly.titc.unified.message.dubbo.entity.request.sms.QuerySmsAccountReq;
import com.ly.titc.unified.message.dubbo.entity.response.send.SendResponse;
import com.ly.titc.unified.message.dubbo.entity.response.sms.SmsAccountResp;
import com.ly.titc.unified.message.dubbo.enums.SystemCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author：rui
 * @name：NotificationDubboInterface
 * @Date：2024-11-20 17:36
 * @Filename：NotificationDubboInterface
 */
@Slf4j
@Component
public class MessageDecorator {

    private static final String code = "send";

    private static final String projectCode = "MEMBER_CRM";

    private static final String sceneCode = "*********************";

    @DubboReference(group = "${message-dsf-group}", check = false)
    private SendService sendService;

    @DubboReference(group = "${message-dsf-group}", check = false)
    private SmsAccountDubboService smsAccountDubboService;

    public SendResponse sendText(Integer masterType, String masterCode, String phone, String verificationCode) {
        SendRequest sendRequest = new SendRequest();
        sendRequest.setCode(code);
        sendRequest.setSystemCode(SystemCodeEnum.MIDDLEBACKEND);
        sendRequest.setSceneCode(sceneCode);
        sendRequest.setSenderProjectCode(projectCode);
        sendRequest.setReceiverProjectCode(projectCode);
        MessageParam messageParam = new MessageParam();
        messageParam.setCReceiver(phone);
        Map<String, String> variables = new HashMap<>();
        variables.put("verificationCode", verificationCode);
        messageParam.setVariables(variables);
        // 2 短信 4 微信公众号
        messageParam.setTunnelType(2);
        if (MasterTypeEnum.BLOC.getType().equals(masterType)) {
            messageParam.setBlocCode(masterCode);
        }
        sendRequest.setMessageParam(messageParam);
        SendResponse sendResponse = Response.getValidateData(sendService.send(sendRequest));
        return sendResponse;
    }

    /**
     * 查询集团剩余短信数量
     */
    public Integer querySmsBalance(String masterCode) {
        QuerySmsAccountReq querySmsAccountReq = new QuerySmsAccountReq();
        querySmsAccountReq.setUserName(masterCode);
        SmsAccountResp smsAccountResp = Response.getValidateData(smsAccountDubboService.findByUserName(querySmsAccountReq));
        return smsAccountResp.getRechargeBalance();
    }

}
