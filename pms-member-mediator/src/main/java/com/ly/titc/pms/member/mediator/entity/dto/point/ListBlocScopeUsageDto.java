package com.ly.titc.pms.member.mediator.entity.dto.point;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:10
 */
@Data
@Accessors(chain = true)
public class ListBlocScopeUsageDto {

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 适用来源
     */
    private List<String> scopeSources;

    /**
     * 适用来源code
     */
    private List<String> scopeSourceCodes;

    /**
     * 适用平台渠道
     */
    private List<String> scopePlatformChannels;

}
