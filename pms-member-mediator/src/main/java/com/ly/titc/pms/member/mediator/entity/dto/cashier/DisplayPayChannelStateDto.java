package com.ly.titc.pms.member.mediator.entity.dto.cashier;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-4 9:54
 */
@Data
@Accessors(chain = true)
public class DisplayPayChannelStateDto {
    /**
     * 外显支付渠道
     */
    private String displayPayChannel;

    /**
     * 外显支付渠道描述
     */
    private String displayPayChannelDesc;

    /**
     * 开通状态 1:开通 0:未开通
     */
    private Integer state;

    /**
     * 是否支持POS机支付
     * 0 否
     * 1 是
     */
    private Integer isUsePos=0;
}
