package com.ly.titc.pms.member.mediator.entity.dto.profile;

import lombok.Data;

import java.util.List;

/**
 * 批量添加单个会员标签
 *
 * <AUTHOR>
 * @date 2024/11/19 17:55
 */
@Data
public class BatchAddSingleMemberTagDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 打标分类: 1:手动标记 2:系统标记
     */
    private Integer markType;

    /**
     * 标签
     */
    private List<TagBaseDto> tags;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 平台渠道
     */
    private String platformChannel;
}
