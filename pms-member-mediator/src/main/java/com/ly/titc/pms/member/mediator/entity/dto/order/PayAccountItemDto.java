package com.ly.titc.pms.member.mediator.entity.dto.order;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 17:31
 */
@Data
@Accessors(chain = true)
public class PayAccountItemDto {

    /**
     * 账户号
     */
    private String accountNo;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private String accountType;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 入账渠道
     */
    private Integer channel;
    /**
     * 线下支付可以自定义结算项code
     */
    private String itemCode;


    /**
     * 结算项code
     */
    private String itemName;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 原始金额
     */
    private BigDecimal originAmount;

    /**
     * 原明细号 （冲减或者退款需要）
     */
    private String accountItemNo;

}
