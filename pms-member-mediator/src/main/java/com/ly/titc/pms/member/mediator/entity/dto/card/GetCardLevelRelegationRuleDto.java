package com.ly.titc.pms.member.mediator.entity.dto.card;

import com.ly.titc.pms.ecrm.dubbo.entity.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 根据卡和等级查询降级规则入参
 *
 * @Author：rui
 * @name：GetRelegationRuleByCardTemplateAndLevelReq
 * @Date：2024-10-30 11:24
 * @Filename：GetRelegationRuleByCardTemplateAndLevelReq
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetCardLevelRelegationRuleDto extends BaseReq {

    private Integer masterType;

    private String masterCode;

    private Integer memberCardLevel;

    private Long cardId;
}
