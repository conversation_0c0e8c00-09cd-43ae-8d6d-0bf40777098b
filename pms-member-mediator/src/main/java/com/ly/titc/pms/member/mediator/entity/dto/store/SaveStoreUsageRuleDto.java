package com.ly.titc.pms.member.mediator.entity.dto.store;

import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-29 10:01
 */
@Data
@Accessors(chain = true)
public class SaveStoreUsageRuleDto extends SaveUsageRuleDto {

    /**
     * 规则模式：SINGLE ：唯一  MULTI：混合
     */
    private String ruleMode;

    /**
     * 可用场景
     */
    private List<String> scenes;

    /**
     * 储值扣款模式 1 优先本金 2 优先礼金 3 比例扣减
     */
    private Integer deductionType;

    /**
     * 礼金扣减比例
     */
    private String deductionRatio;

}
