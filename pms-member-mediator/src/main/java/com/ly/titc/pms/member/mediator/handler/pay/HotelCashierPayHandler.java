package com.ly.titc.pms.member.mediator.handler.pay;


import com.ly.titc.cashier.dubbo.entity.request.trade.CashierAccountSettleDeductTradeReq;
import com.ly.titc.cashier.dubbo.entity.request.trade.CashierAccountSettlePaymentGetStateReq;
import com.ly.titc.cashier.dubbo.entity.request.trade.CashierAccountSettleRefundGetStateReq;
import com.ly.titc.cashier.dubbo.entity.response.trade.CashierAccountSettleDeductResp;
import com.ly.titc.cashier.dubbo.entity.response.trade.CashierAccountSettlePaymentResp;
import com.ly.titc.pms.account.dubbo.entity.response.pay.AccountPayTradeResp;
import com.ly.titc.pms.member.com.enums.PayRouteEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.entity.dto.order.GetPayStateDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.OrderRefundResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PayOrderResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;



/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-9-11 19:47
 */
@Component
@Slf4j
public class HotelCashierPayHandler extends AbstractPayHandler {

    /**
     * 查询单条支付结果
     */
    @Override
    public PayOrderResultDto getPayState(GetPayStateDto payStateDto){
        //1.参数转换
        CashierAccountSettlePaymentGetStateReq req = orderMedConverter.convertGetDetail(payStateDto);
        //2.调用收银台查询支付结果
        CashierAccountSettlePaymentResp paymentResp= paymentTradeDecorator.getTradeState(req);
        log.info("调用收银台查询支付结果:{}", paymentResp);;
        AccountPayTradeResp tradeResp = orderMedConverter.convertTradeResp(paymentResp,paymentResp.getPaymentInfoDto());
        PayOrderResultDto resultDto = orderMedConverter.convertResult(tradeResp);
        resultDto.setOnlinePayNo(paymentResp.getSettlePaymentTradeNo());
        if(paymentResp.getSettlePaymentTradeNo() == null) {
            resultDto.setPayedTime(paymentResp.getPaymentInfoDto().getTradeCompleteTime());
        }
        return resultDto;
    }

    @Override
    public OrderRefundResultDto refund(MemberOrderRefundInfo processRefundOrder, MemberOrderPayInfo memberOrderPayInfo){
        CashierAccountSettleDeductTradeReq req = refundOrderMedConverter.convertDeductReq(processRefundOrder, memberOrderPayInfo);
        CashierAccountSettleDeductResp resp = paymentTradeDecorator.createDeductTrade(req);
        return refundOrderMedConverter.convertResult(resp);
    }

    @Override
    public OrderRefundResultDto getRefundState(MemberOrderRefundInfo memberOrderRefundInfo, String operator) {
        CashierAccountSettleRefundGetStateReq req = refundOrderMedConverter.convertGetRefundState(memberOrderRefundInfo, operator);
        CashierAccountSettleDeductResp resp = paymentTradeDecorator.getDeductTradeState(req);
        return refundOrderMedConverter.convertResult(resp);
    }

    @Override
    public String getRoute() {
        return PayRouteEnum.Hotel_Cashier.getRoute();
    }
}
