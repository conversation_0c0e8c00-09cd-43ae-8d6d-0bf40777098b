package com.ly.titc.pms.member.mediator.entity.dto.point;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:49
 */
@Data
@Accessors(chain = true)
public class PointUsageRuleDetailDto {

    private Long id;

    /**
     * 主体类型（创建） 1:ELONg 2:集团 3:门店
     */
    private Integer masterType;

    /**
     * 主体类型编码（创建） ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 酒馆组编码（冗余存储）
     */
    private String clubCode;

    /**
     * 集团编码（冗余存储）
     */
    private String blocCode;

    /**
     * 酒店编码（冗余存储）
     */
    private Integer hotelCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 适用渠道，逗号隔开 使用渠道 线下酒店：PMS、CRM  微订房：微订房公众号、微订房小程序
     */
    private List<String> scopePlatformChannels;

    /**
     * 配置的适用来源 PUB(集团组) BLOC (集团)，HOTEL(门店)
     */
    private List<String> scopeSources;

    /**
     * 配置的门店范围 1 全部门店 2 指定门店
     */
    private Integer scopeHotelRange;

    /**
     * 是否可用 1可使用 0不可使用
     */
    private Integer isCanUse;

    /**
     * 积分使用模式 1.使用指定门店积分，2.使用本店积分，3.使用全部积分
     */
    private Integer usageMode;

    /**
     * 使用是否需要密码 1：需要 0 不需要
     */
    private Integer isUsePassword;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 创建人
     */
    private String createUser;
    /**
     * 适用门店范围
     */
    private List<String> scopeHotelCodes;

    /**
     * 储值使用模式指定门店范围
     */
    private List<String> usageHotelCodes;
}
