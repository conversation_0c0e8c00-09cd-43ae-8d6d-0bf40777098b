package com.ly.titc.pms.member.mediator.entity.dto.event;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：MemberEventHappenInfo
 * @Date：2024-12-14 23:10
 * @Filename：MemberEventHappenInfo
 */
@Data
public class MemberEventHappenInfoDto {

    /**
     * 事件号
     */
    @PrimaryKey(column = "event_no", value = 1)
    private String eventNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 事件
     */
    private String event;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
