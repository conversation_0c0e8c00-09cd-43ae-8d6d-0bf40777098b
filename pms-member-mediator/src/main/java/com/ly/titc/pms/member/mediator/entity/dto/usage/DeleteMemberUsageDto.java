package com.ly.titc.pms.member.mediator.entity.dto.usage;

import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:10
 */
@Data
@Accessors(chain = true)
public class DeleteMemberUsageDto extends BaseDto {

    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空")
    private Long ruleId;



}
