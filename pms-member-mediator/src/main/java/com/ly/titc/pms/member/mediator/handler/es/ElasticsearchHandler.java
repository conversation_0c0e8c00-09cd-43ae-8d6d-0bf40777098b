package com.ly.titc.pms.member.mediator.handler.es;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.pms.member.mediator.config.ElasticsearchConfig;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.MemberDocumentDto;
import com.ly.titc.springboot.elasticsearch.client.ElasticsearchClient;
import com.ly.titc.springboot.elasticsearch.entity.request.DeleteRequest;
import com.ly.titc.springboot.elasticsearch.entity.request.InsertRequest;
import com.ly.titc.springboot.elasticsearch.entity.request.SearchRequest;
import com.ly.titc.springboot.elasticsearch.entity.request.StartScrollRequest;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;


/**
 * <AUTHOR>
 * @classname ElasticsearchHandler
 * @descrition
 * @since 2021/6/28 下午5:54
 */
@Configuration
@EnableConfigurationProperties({ElasticsearchConfig.class})
public class ElasticsearchHandler {

    private final ElasticsearchConfig config;

    public ElasticsearchHandler(ElasticsearchConfig config) {
        this.config = config;
    }

    private static final String TERM = "term";
    private static final String TERMS = "terms";
    private static final String RANGE = "range";
    private static final String MATCH_PHASE = "match_phrase";

    /**
     * 获取es 请求体
     *
     * @param templateName
     * @param data
     * @return
     */
    public SearchRequest<MemberDocumentDto> getSearchRequest(String templateName, String data) {
        SearchRequest.SearchRequestBuilder<MemberDocumentDto, ?, ?> searchRequestBuilder = SearchRequest.newBuilder();

        searchRequestBuilder.template(templateName)
                .data(data)
                .clazz(MemberDocumentDto.class)
                .host(config.getHost())
                .token(config.getKey().getToken())
                .index(config.getKey().getIndex());
        return searchRequestBuilder.build();
    }

    /**
     * 滚动查询
     *
     * @param templateName
     * @param data
     * @return
     */
    public StartScrollRequest<MemberDocumentDto> getScrollRequest(String templateName, String data){
        StartScrollRequest.StartScrollRequestBuilder<MemberDocumentDto, ?, ?> searchRequestBuilder = StartScrollRequest.newBuilder();

        searchRequestBuilder.template(templateName)
                .data(data)
                .clazz(MemberDocumentDto.class)
                .host(config.getHost())
                .token(config.getKey().getToken())
                .index(config.getKey().getIndex());
        return searchRequestBuilder.build();
    }

    /**
     * es index insert data
     *
     * @param data
     */
    public Integer insert(String data) {
        InsertRequest.InsertRequestBuilder<?, ?> insertRequestBuilder = InsertRequest.newBuilder();

        insertRequestBuilder.data(data)
                .host(config.getHost())
                .token(config.getKey().getToken())
                .index(config.getKey().getIndex());
        return ElasticsearchClient.insert(insertRequestBuilder.build());
    }

    /**
     * es index delete data
     *
     * @param docId
     * @return
     */
    public Integer delete(String docId) {
        DeleteRequest.DeleteRequestBuilder<?, ?> deleteRequestBuilder = DeleteRequest.newBuilder();

        deleteRequestBuilder.docId(docId)
                .host(config.getHost())
                .token(config.getKey().getToken())
                .index(config.getKey().getIndex());
        return ElasticsearchClient.delete(deleteRequestBuilder.build());
    }


    /**
     * build term
     *
     * @param key
     * @param value
     * @return
     */
    public static JSONObject buildTerm(String key, Object value) {

        JSONObject term = new JSONObject();
        term.put(TERM, new JSONObject());
        term.getJSONObject(TERM).put(key, value);
        return term;
    }

    /**
     * build term
     *
     * @param key
     * @param value
     * @return
     */
    public static JSONObject buildTerms(String key, List<Long> value) {

        JSONObject term = new JSONObject();
        term.put(TERMS, new JSONObject());
        term.getJSONObject(TERMS).put(key, value);
        return term;
    }

    /**
     * build match phase
     *
     * @param key
     * @param value
     * @return
     */
    public static JSONObject buildMatchPhase(String key, Object value) {

        JSONObject matchPhase = new JSONObject();
        matchPhase.put(MATCH_PHASE, new JSONObject());
        matchPhase.getJSONObject(MATCH_PHASE).put(key, value);
        return matchPhase;
    }

    /**
     * build range
     *
     * @param key
     * @param symbol
     * @param value
     * @return
     */
    public static JSONObject buildRange(String key, String symbol, Object value) {
        JSONObject range = new JSONObject();
        range.put(RANGE, new JSONObject());
        range.getJSONObject(RANGE).put(key, new JSONObject());
        range.getJSONObject(RANGE).getJSONObject(key).put(symbol, value);
        return range;
    }

}
