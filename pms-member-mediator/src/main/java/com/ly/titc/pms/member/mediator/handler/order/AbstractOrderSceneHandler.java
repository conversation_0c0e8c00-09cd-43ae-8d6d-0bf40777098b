package com.ly.titc.pms.member.mediator.handler.order;

import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.entity.dto.order.CreateOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.OrderPostResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PurchaseCardDto;
import com.ly.titc.pms.member.mediator.manager.OrderSceneManager;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-26 19:33
 */
@Slf4j
public abstract class AbstractOrderSceneHandler<T> {

    public AbstractOrderSceneHandler(){
        OrderSceneManager.putInstance(this);
    }

    /**
     * 前置校验
     * @param dto
     */
    public abstract CreateOrderDto<T> doPreCheck(CreateOrderDto<T> dto);

    /**
     * 获取加锁key
     */
    public abstract String doGetLockKey(T dto);

    /**
     * 保存业务场景订单
     * @param dto
     */
    public abstract  void saveSceneOrder(CreateOrderDto<T> dto);

    /**
     * 后置处理
     */
    public abstract OrderPostResultDto postHandle(MemberOrderInfo orderInfo);

    /**
     * 退款业务回退
     */
    public abstract void refundHandle(MemberOrderRefundInfo orderInfo);

    /**
     * 获取场景
     * @return
     */
    public abstract String getScene();

}
