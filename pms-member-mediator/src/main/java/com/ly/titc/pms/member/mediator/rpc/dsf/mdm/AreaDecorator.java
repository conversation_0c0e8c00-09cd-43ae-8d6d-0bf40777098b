package com.ly.titc.pms.member.mediator.rpc.dsf.mdm;

import com.ly.spat.dsf.annoation.DSFAction;
import com.ly.spat.dsf.annoation.RequestBody;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.api.AreaService;
import com.ly.titc.mdm.entity.request.area.ListAreaTreesReq;
import com.ly.titc.mdm.entity.request.area.ListAreasReq;
import com.ly.titc.mdm.entity.response.area.AreaResp;
import com.ly.titc.mdm.entity.response.area.AreaTreeResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-5
 */
@Slf4j
@Component
public class AreaDecorator {

    @DubboReference(protocol = "dsf", providedBy = "${mdm-dsf-group}", version = "${mdm-dsf-service-version}")
    private AreaService areaService;


    public List<AreaTreeResp> listAreaTree(String blocCode, Long pid, Integer state) {
        ListAreaTreesReq req = new ListAreaTreesReq();
        req.setBlocCode(blocCode)
                .setPid(pid)
                .setState(state);
        Response<List<AreaTreeResp>> resp = areaService.listTrees(req);
        return Response.getValidateData(resp);
    }

    public List<AreaResp> listAreas(String blocCode,Integer state,String areaName){
        ListAreasReq req = new ListAreasReq();
        req.setAreaName(areaName);
        req.setBlocCode(blocCode);
        req.setState(state);
        Response<List<AreaResp>> listResponse = areaService.listAreas(req);
        return Response.getValidateData(listResponse);
    }

}
