package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 保存卡信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Getter
@Setter
@Accessors(chain = true)
public class SaveCardConfigDto {

    /**
     * 卡模版主键ID
     */
    private Long id;

    /**
     * 集团编码（ELONG为空）
     */
    private String blocCode;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 卡代码
     */
    private String cardCode;

    /**
     * 卡类型 1: 基础卡 2 企业卡
     */
    private Integer cardType;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 适用类型 1 集团  2 品牌 3 门店
     */
    private Integer applicationType;

    /**
     * 适用范围 0 默认 1 全部
     */
    private Integer applicationScope;

    /**
     * 适用范围
     */
    private List<SaveCardApplicableDataMappingDto> applicableDataMappings;

    /**
     * 手机号是否必填 0 否 1 是
     */
    private Integer mobileInput;

    /**
     * 姓名是否必填 0 否 1 是
     */
    private Integer nameInput;

    /**
     * 证件是否必填 0 否 1 是
     */
    private Integer certificateInput;

    /**
     * 0 否 1是
     */
    private Integer isDefault;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 卡号规则
     */
    private SaveCardNoRuleDto cardNoRule;

}
