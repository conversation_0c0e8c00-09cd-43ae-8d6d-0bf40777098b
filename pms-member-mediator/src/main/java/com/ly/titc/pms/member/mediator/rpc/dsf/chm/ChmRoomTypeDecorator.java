package com.ly.titc.pms.member.mediator.rpc.dsf.chm;

import com.ly.titc.chm.api.RoomTypeService;
import com.ly.titc.chm.entity.request.BaseBlocReq;
import com.ly.titc.chm.entity.response.BlocRoomTypeResp;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.mediator.rpc.dsf.AbstractDsfServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2025-2-7
 */
@Slf4j
@Component
public class ChmRoomTypeDecorator extends AbstractDsfServiceProxy {

    private static final String S_NAME = "roomType";

    public List<BlocRoomTypeResp> listBlocRoomType(String blocCode){
        BaseBlocReq req = new BaseBlocReq();
        req.setBlocCode(blocCode);
        req.setTrackingId(UUID.randomUUID().toString());
        RoomTypeService roomTypeService = getProxy(RoomTypeService.class, CHM_DSF_GS_NAME, S_NAME, chmVersion);
        return Response.getValidateData(roomTypeService.listBlocRoomType(req));
    }
}
