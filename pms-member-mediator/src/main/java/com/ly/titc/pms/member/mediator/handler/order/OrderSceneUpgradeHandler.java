package com.ly.titc.pms.member.mediator.handler.order;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.biz.MemberOrderDetailInfoBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.converter.MemberCardMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpgradeMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.ActivityOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.CreateOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.OrderPostResultDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.activity.MemberActivityDecorator;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 会员升级
 *
 * <AUTHOR>
 * @date 2024/12/10 17:31
 */
@Slf4j
@Component
public class OrderSceneUpgradeHandler extends AbstractOrderSceneHandler<UpgradeMemberDto>{

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private MemberOrderDetailInfoBiz detailInfoBiz;

    @Resource
    private MemberCardMedConverter memberCardMedConverter;
    @Resource
    private MemberActivityDecorator memberActivityDecorator;

    @Override
    public CreateOrderDto<UpgradeMemberDto> doPreCheck(CreateOrderDto<UpgradeMemberDto> dto) {
        UpgradeMemberDto upgradeMemberDto = dto.getMemberSceneNoteDto();
        if(!Objects.isNull(dto.getActivityOrderDto())){
            ActivityOrderDto activityOrderDto = dto.getActivityOrderDto();
            boolean flag = memberActivityDecorator.judgeAvailableMemberActivity(dto.getBlocCode(),activityOrderDto.getActivityCode(),
                    activityOrderDto.getGearCode(),upgradeMemberDto.getMemberNo(),upgradeMemberDto.getCardId(),upgradeMemberDto.getPreLevel());
            if (!flag) {
                throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00017);
            }
        }
        UpdateCardLevelDto updateCardLevelDto = memberCardMedConverter.convertDtoToDto(upgradeMemberDto);
        memberCardMedService.checkUpdateCardLevel(updateCardLevelDto);
        return dto;
    }

    @Override
    public String doGetLockKey(UpgradeMemberDto dto) {
        return CommonConstant.CREATE_ORDER_LOCK_KEY_PREFIX + String.format("%s_%s_%s", getScene(), dto.getMemberNo(), dto.getCardId());
    }

    @Override
    public void saveSceneOrder(CreateOrderDto<UpgradeMemberDto> dto) {

    }

    @Override
    public OrderPostResultDto postHandle(MemberOrderInfo orderInfo) {
        MemberOrderDetailInfo detailInfo =  detailInfoBiz.getByOrderNo(orderInfo.getMemberOrderNo());
        UpgradeMemberDto upgradeMemberDto = JSONObject.parseObject(detailInfo.getMemberSceneNote(), UpgradeMemberDto.class);
        UpdateCardLevelDto updateCardLevelDto = memberCardMedConverter.convertDtoToDto(upgradeMemberDto);
        memberCardMedService.updateCardLevel(updateCardLevelDto);
        OrderPostResultDto resultDto = new OrderPostResultDto();
        resultDto.setMemberNo(updateCardLevelDto.getMemberNo());
        resultDto.setMemberOrderNo(orderInfo.getMemberOrderNo());
        return resultDto;
    }

    @Override
    public void refundHandle(MemberOrderRefundInfo orderInfo) {

    }

    @Override
    public String getScene() {
        return MemberSceneEnum.UPGRADE.getScene();
    }
}
