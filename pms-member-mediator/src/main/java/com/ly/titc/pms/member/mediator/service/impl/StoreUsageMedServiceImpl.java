package com.ly.titc.pms.member.mediator.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MasterObject;
import com.ly.titc.pms.member.biz.AssetUsageModeScopeMappingBiz;
import com.ly.titc.pms.member.biz.AssetUsageRuleScopeMappingBiz;
import com.ly.titc.pms.member.biz.StoreUsageRuleBiz;
import com.ly.titc.pms.member.com.enums.AssetTypeEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.enums.StoreUsageModeEnum;
import com.ly.titc.pms.member.com.enums.StateEnum;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageModeScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageRuleScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.StoreUsageRuleInfo;
import com.ly.titc.pms.member.entity.bo.PageUsageRuleScopeMappingBo;
import com.ly.titc.pms.member.entity.wrapper.StoreUsageRuleWrapper;
import com.ly.titc.pms.member.mediator.converter.StoreUsageMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.point.BlocScopeUsageDto;
import com.ly.titc.pms.member.mediator.entity.dto.point.ListBlocScopeUsageDto;
import com.ly.titc.pms.member.mediator.entity.dto.store.*;
import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleResultDto;
import com.ly.titc.pms.member.mediator.help.UsageRuleCheckParamHelper;
import com.ly.titc.pms.member.mediator.service.StoreUsageMedService;
import com.ly.titc.pms.member.service.UsageRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 储值使用规则
 *
 * <AUTHOR>
 * @date 2025/6/26 11:03
 */
@Slf4j
@Service
public class StoreUsageMedServiceImpl implements StoreUsageMedService {

    @Resource
    private StoreUsageRuleBiz storeUsageRuleBiz;
    @Resource
    private AssetUsageRuleScopeMappingBiz usageRuleScopeMappingBiz;
    @Resource
    private AssetUsageModeScopeMappingBiz usageModeScopeMappingBiz;
    @Resource(type = StoreUsageMedConverter.class)
    private StoreUsageMedConverter storeUsageMedConverter;
    @Resource
    private UsageRuleCheckParamHelper usageRuleCheckParamHelper;
    @Resource
    private UsageRuleService usageRuleService;

    @Override
    public List<StoreUsageRuleDto> listStoreRule(ListStoreRuleDto dto) {
        String scopeSource = dto.getScopeSource();
        String platformChannel = dto.getPlatformChannel();
        String scopeSourceCode = dto.getScopeSourceCode();
        //1.查询使用的主体方的适用的规则
        List<AssetUsageRuleScopeMapping> usageRuleScopeMappings = usageRuleScopeMappingBiz.listByScope(scopeSource, scopeSourceCode, AssetTypeEnum.STORE.getType(), platformChannel);
        if (CollectionUtils.isEmpty(usageRuleScopeMappings)) {
            log.error("没有配置规则:scopeSource={},scopeMasterCode={}", scopeSource, scopeSourceCode);
            return Collections.emptyList();
        }
        //2.查询规则
        List<Long> ruleIds = usageRuleScopeMappings.stream().map(AssetUsageRuleScopeMapping::getRuleId).distinct().collect(Collectors.toList());
        List<StoreUsageRuleInfo> ruleInfos = storeUsageRuleBiz.listByIds(ruleIds);
        if (CollectionUtils.isEmpty(ruleInfos)) {
            log.error("没有查询到规则:ruleIds={}", ruleIds);
            return Collections.emptyList();
        }
        //过滤usageMode为指定指定可用的规则
        List<StoreUsageRuleInfo> usageRuleInfos = ruleInfos.stream().filter(rule -> rule.getUsageMode().equals(StoreUsageModeEnum.ALLOCATE.getValue())).collect(Collectors.toList());
        //如果是为指定指定可用的使用规则，查询指定可用的酒店范围 （指定可用如果想用本店充值的，配置的时候一定要包含本店才能使用）
        List<AssetUsageModeScopeMapping> usageModeScopeMappings = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(usageRuleInfos)) {
            usageModeScopeMappings = usageModeScopeMappingBiz.listByRuleIds(usageRuleInfos.stream().map(StoreUsageRuleInfo::getId).collect(Collectors.toList()));
        }
        Map<Long, StoreUsageRuleInfo> ruleInfoMap = ruleInfos.stream().collect(Collectors.toMap(StoreUsageRuleInfo::getId, rule -> rule));
        Map<Long, List<AssetUsageModeScopeMapping>> usageModeScopeMappingMap = usageModeScopeMappings.stream().collect(Collectors.groupingBy(AssetUsageModeScopeMapping::getRuleId));
        List<StoreUsageRuleDto> ruleDtos = new ArrayList<>();
        //组装响应体
        usageRuleScopeMappings.forEach(usageRuleScopeMapping -> {
            Long ruleId = usageRuleScopeMapping.getRuleId();
            StoreUsageRuleInfo ruleInfo = ruleInfoMap.get(ruleId);
            if (ruleInfo.getState().equals(StateEnum.CLOSE.getState())) {
                log.info("规则状态已停用：ruleId={}", ruleId);
                return;
            }
            StoreUsageRuleDto usageRuleDto = storeUsageMedConverter.convertPoToDto(ruleInfo);
            if (ruleInfo.getUsageMode().equals(StoreUsageModeEnum.ALLOCATE.getValue())) {
                List<AssetUsageModeScopeMapping> ruleModeScopeMapping = usageModeScopeMappingMap.get(ruleId);
                List<MasterObject> usageModeScopes = ruleModeScopeMapping.stream().map(storeUsageMedConverter::convertPoToDto).collect(Collectors.toList());
                usageRuleDto.setUsageModeScopes(usageModeScopes);
            }
            ruleDtos.add(usageRuleDto);

        });
        return ruleDtos;
    }

    @Override
    public Pageable<StoreUsageRuleDetailDto> pageStoreUsageRule(PageStoreUsageDto dto) {
        PageUsageRuleScopeMappingBo pageBo = storeUsageMedConverter.convertDtoToBo(dto);
        List<AssetUsageRuleScopeMapping> scopeMappings = usageRuleScopeMappingBiz.listByScopeSource(AssetTypeEnum.STORE.getType(), pageBo);
        if (CollectionUtils.isNotEmpty(pageBo.getScopeSources()) || CollectionUtils.isNotEmpty(pageBo.getScopePlatformChannels()) || CollectionUtils.isNotEmpty(pageBo.getScopeHotelCodes())) {
            if (CollectionUtils.isEmpty(scopeMappings)) {
                return Pageable.empty();
            }
        }
        //获取ruleIds
        List<Long> scopeRuleIds = scopeMappings.stream().map(AssetUsageRuleScopeMapping::getRuleId).distinct().collect(Collectors.toList());
        pageBo.setRuleIds(scopeRuleIds);
        Page<StoreUsageRuleInfo> ruleInfoPage = storeUsageRuleBiz.pageStoreRule(pageBo);
        List<StoreUsageRuleInfo> records = ruleInfoPage.getRecords();
        List<Long> ruleIds = records.stream().map(StoreUsageRuleInfo::getId).collect(Collectors.toList());
        List<AssetUsageModeScopeMapping> usageModeScopeMappings  = usageModeScopeMappingBiz.listByRuleIds(ruleIds);
        List<StoreUsageRuleDetailDto>  detailDtoList = storeUsageMedConverter.convertStoreDetail(records, usageModeScopeMappings ,scopeMappings);
        return PageableUtil.convert(ruleInfoPage, detailDtoList);
    }

    @Override
    public SaveStoreUsageRuleResultDto saveStoreUsageRule(SaveStoreUsageRuleDto dto) {
        //1.校验入参
        usageRuleCheckParamHelper.checkArgUsage(dto);
        //2.校验唯一性
        SaveStoreUsageRuleResultDto saveDto = new SaveStoreUsageRuleResultDto();
        List<SaveUsageRuleResultDto> resultList=  usageRuleCheckParamHelper.checkRepeat(dto,AssetTypeEnum.STORE.getType());
        if(CollectionUtils.isNotEmpty(resultList)){
            saveDto.setRepeatedList(resultList);
            return saveDto;
        }
        StoreUsageRuleWrapper wrapper = storeUsageMedConverter.convertWrapper( dto);
        Long ruleId = usageRuleService.saveStoreUsageRule(wrapper);
        saveDto.setRuleId(ruleId);
        saveDto.setRepeatedList(Collections.emptyList());
        return saveDto;
    }

    @Override
    public Boolean updateState(UpdateStoreUsageStateDto dto) {
        StoreUsageRuleInfo storeUsageRuleInfo = storeUsageRuleBiz.getById(dto.getRuleId());
        if (storeUsageRuleInfo == null || (!storeUsageRuleInfo.getMasterType().equals(dto.getMasterType())
                || !storeUsageRuleInfo.getMasterCode().equals(dto.getMasterCode()))) {
            throw new ServiceException(RespCodeEnum.STORE_40000);
        }
        storeUsageRuleBiz.updateState(dto.getRuleId(), dto.getState(), dto.getOperator());
        return true;
    }

    @Override
    public void deleteStoreUsageRule(DeleteStoreUsageDto dto) {
        StoreUsageRuleInfo storeUsageRuleInfo = storeUsageRuleBiz.getById(dto.getRuleId());
        if (storeUsageRuleInfo == null || (!storeUsageRuleInfo.getMasterType().equals(dto.getMasterType())
                || !storeUsageRuleInfo.getMasterCode().equals(dto.getMasterCode()))) {
            throw new ServiceException(RespCodeEnum.STORE_40000);
        }
        storeUsageRuleBiz.deleteById(dto.getRuleId(), dto.getOperator());
    }

    @Override
    public List<BlocScopeUsageDto> listBlocScopeUsageRule(ListBlocScopeUsageDto dto) {
        PageUsageRuleScopeMappingBo query = storeUsageMedConverter.convertDtoToBo(dto);
        List<AssetUsageRuleScopeMapping> scopeMappings = usageRuleScopeMappingBiz.listByScopeSource(AssetTypeEnum.STORE.getType(), query);
        return scopeMappings.stream().map(storeUsageMedConverter::convertPoToBlocDto).collect(Collectors.toList());
    }
}
