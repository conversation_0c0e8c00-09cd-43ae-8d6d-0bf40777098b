package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.pms.member.mediator.entity.dto.asset.ConsumeMemberPointDto;
import com.ly.titc.pms.member.mediator.entity.dto.asset.MemberRecordOPResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.asset.ReceiveMemberPointDto;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-29 11:32
 */
public interface MemberPointAssetOpMedService {


    /**
     * 获得积分
     * @param dto
     */
    MemberRecordOPResultDto receive(ReceiveMemberPointDto dto);

    /**
     * 消费积分
     */
    MemberRecordOPResultDto consume(ConsumeMemberPointDto dto);

}
