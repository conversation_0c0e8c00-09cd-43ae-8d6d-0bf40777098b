package com.ly.titc.pms.member.mediator.entity.dto.es.member;

import lombok.Data;

/**
 * 会员卡信息
 *
 * <AUTHOR>
 * @date 2024/11/13 11:36
 */
@Data
public class MemberCardDocumentDto {

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 会员卡类型
     */
    private Integer cardType;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 会员卡生效时间
     */
    private Long effectBeginDate;

    /**
     * 会员卡失效时间
     */
    private Long effectEndDate;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;
}
