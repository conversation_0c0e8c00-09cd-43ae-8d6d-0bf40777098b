package com.ly.titc.pms.member.mediator.entity.dto.store;

import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleResultDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 19:51
 */
@Data
@Accessors(chain = true)
public class SaveStoreUsageRuleResultDto {

    /**
     * 配置主键ID
     */
    private Long ruleId;

    /**
     * 重复数据
     */
    private List<SaveUsageRuleResultDto> repeatedList;


}
