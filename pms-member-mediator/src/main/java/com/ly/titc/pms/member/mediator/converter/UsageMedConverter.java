package com.ly.titc.pms.member.mediator.converter;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.titc.common.enums.MasterTypeEnum;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.pms.ecrm.dubbo.enums.MemberStoreUsageRuleModeEnum;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageModeScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageRuleScopeMapping;
import com.ly.titc.pms.member.entity.bo.PageUsageRuleScopeMappingBo;
import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleResultDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/26 11:36
 */
@Mapper(componentModel = "spring")
public interface UsageMedConverter {

    default PageUsageRuleScopeMappingBo convertDtoToBo(List<String> scopeSourceCodes, List<String> scopePlatformChannels) {
        PageUsageRuleScopeMappingBo query = new PageUsageRuleScopeMappingBo();
        query.setScopeSourceCodes(scopeSourceCodes);
        query.setScopeSources(Collections.singletonList(ScopeSourceEnum.HOTEL.getCode()));
        query.setScopePlatformChannels(scopePlatformChannels);
        return query;
    }

    default PageUsageRuleScopeMappingBo convertDtoToBo(String blocCode, List<String> scopePlatformChannels) {
        PageUsageRuleScopeMappingBo query = new PageUsageRuleScopeMappingBo();
        query.setScopeSourceCodes(Collections.singletonList(blocCode));
        query.setScopeSources(Collections.singletonList(ScopeSourceEnum.BLOC.getCode()));
        query.setScopePlatformChannels(scopePlatformChannels);
        return query;
    }

    List<SaveUsageRuleResultDto> convertResult(List<AssetUsageRuleScopeMapping> scopeMappings);

    @Mappings({
            @Mapping(target = "scopeHotelCode", source = "hotelCode")
    })
    SaveUsageRuleResultDto convertPoToDto(AssetUsageRuleScopeMapping mapping);

    default List<AssetUsageModeScopeMapping> convertUsage(SaveUsageRuleDto dto, String assetType) {
        List<AssetUsageModeScopeMapping> usageModeScopeMappings = new ArrayList<>();
        List<String> usageHotelCodes = dto.getUsageHotelCodes();
        if (usageHotelCodes == null || CollectionUtils.isEmpty(usageHotelCodes)) {
            return Collections.emptyList();
        }
        usageHotelCodes.forEach(s -> {
            AssetUsageModeScopeMapping modeScopeMapping = new AssetUsageModeScopeMapping();
            modeScopeMapping.setUsageMasterType(MasterTypeEnum.HOTEL.getType());
            modeScopeMapping.setUsageMasterCode(s);
            modeScopeMapping.setBlocCode(dto.getBlocCode());
            modeScopeMapping.setHotelCode(s);
            modeScopeMapping.setRuleMode(MemberStoreUsageRuleModeEnum.SINGLE_STORE.getCode());
            modeScopeMapping.setAssetType(assetType);
            usageModeScopeMappings.add(modeScopeMapping);
        });
        return usageModeScopeMappings;
    }

    default List<AssetUsageRuleScopeMapping> convertMapping(SaveUsageRuleDto dto, String assetType) {
        List<AssetUsageRuleScopeMapping> scopeMappings = new ArrayList<>();
        List<String> scopePlatformChannels = dto.getScopePlatformChannels();
        List<String> scopeSources = dto.getScopeSources();
        scopePlatformChannels.forEach(sp -> {
            scopeSources.forEach(s -> {
                if (s.equals(ScopeSourceEnum.BLOC.getCode())) {
                    AssetUsageRuleScopeMapping scopeMapping = convertMapping(dto);
                    scopeMapping.setScopeSourceCode(dto.getBlocCode());
                    scopeMapping.setBlocCode(dto.getBlocCode());
                    scopeMapping.setScopeSource(s);
                    scopeMapping.setScopePlatformChannel(sp);
                    scopeMapping.setAssetType(assetType);
                    scopeMappings.add(scopeMapping);
                } else if (s.equals(ScopeSourceEnum.HOTEL.getCode())) {
                    dto.getScopeHotelCodes().forEach(hotelCode -> {
                        AssetUsageRuleScopeMapping scopeMapping = convertMapping(dto);
                        scopeMapping.setScopeSourceCode(hotelCode);
                        scopeMapping.setBlocCode(dto.getBlocCode());
                        scopeMapping.setHotelCode(hotelCode);
                        scopeMapping.setScopeSource(s);
                        scopeMapping.setScopePlatformChannel(sp);
                        scopeMapping.setAssetType(assetType);
                        scopeMappings.add(scopeMapping);
                    });
                }
            });
        });
        return scopeMappings;
    }

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")

    })
    AssetUsageRuleScopeMapping convertMapping(SaveUsageRuleDto dto);
}
