package com.ly.titc.pms.member.mediator.rpc.dubbo.coupon;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.*;
import com.ly.titc.pms.spm.dubbo.interfaces.CouponDubboService;
import com.ly.titc.pms.spm.dubbo.interfaces.CouponGrantDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description 优惠券
 * <AUTHOR>
 * @Date 2024-11-4 14:28
 */
@Slf4j
@Component
public class CouponDecorator {

    @DubboReference(group = "${spm-dsf-dubbo-group}",check = false)
    private CouponDubboService couponDubboService;
    @DubboReference(group = "${spm-dsf-dubbo-group}",check = false)
    private CouponGrantDubboService couponGrantDubboService;

    public Pageable<CouponListResp> pageInfos(GetCouponListReq req) {
        Response<Pageable<CouponListResp>> response = couponDubboService.pageInfos(req);
        return Response.getValidateData(response);
    }

    public void grantCouponsByBind(GrantCouponBatchReq req) {
        Response<Void> response = couponGrantDubboService.batchGrant(req);
        Response.getValidateData(response);
    }

    public List<String> grantCouponsByExport(GrantCouponBatchReq req) {
        Response<List<String>> listResponse = couponGrantDubboService.batchExport(req);
        return Response.getValidateData(listResponse);
    }

    public CouponInfoResp getCouponInfo(GetCouponInfoReq req) {
        Response<CouponInfoResp> couponIssueDetail = couponDubboService.getCouponInfo(req);
        return Response.getValidateData(couponIssueDetail);
    }

    public void invalidCoupon(GetByCouponCodeReq req) {
        Response<String> response = couponDubboService.invalidCoupon(req);
        Response.getValidateData(response);
    }

    public void invalidBatch(GetByCouponBatchCodeReq req) {
        Response<Void> response = couponGrantDubboService.invalidCouponBatch(req);
        Response.getValidateData(response);
    }

    public void deleteBatch(GetByCouponBatchCodeReq req) {
        Response<Void> response = couponGrantDubboService.deleteCouponBatch(req);
        Response.getValidateData(response);
    }

    public void deleteCoupon(GetByCouponCodeReq req) {
        Response<String> response = couponDubboService.deleteCoupon(req);
        Response.getValidateData(response);
    }

    public Pageable<CouponBatchListResp> batchPageInfos(QueryCouponBatchListReq req) {
        Response<Pageable<CouponBatchListResp>> response = couponGrantDubboService.batchPageInfos(req);
        return Response.getValidateData(response);
    }

    public Pageable<CouponBatchDetailResp> pageCouponBatchDetails(QueryCouponBatchDetailsReq req) {
        Response<Pageable<CouponBatchDetailResp>> response = couponGrantDubboService.pageCouponBatchDetails(req);
        return Response.getValidateData(response);
    }

    public CouponRedeemInfoResp getCouponRedeemInfo(GetCouponInfoReq apiReq) {
        Response<CouponRedeemInfoResp> response = couponDubboService.getCouponRedeemInfo(apiReq);
        return Response.getValidateData(response);
    }

    public CouponStatisticsResp statisticsCoupon(StatisticsCouponReq apiReq) {
        Response<CouponStatisticsResp> response = couponDubboService.statisticsCoupon(apiReq);
        return Response.getValidateData(response);
    }

    public ApplicableHotelResp getApplicableHotel(GetCouponInfoReq req) {
        Response<ApplicableHotelResp> response = couponDubboService.getApplicableHotel(req);
        return Response.getValidateData(response);
    }
}
