package com.ly.titc.pms.member.mediator.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.ReceiveMemberPointReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberRecordOPResultResp;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.mediator.converter.MemberAssetMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.asset.ConsumeMemberPointDto;
import com.ly.titc.pms.member.mediator.entity.dto.asset.MemberRecordOPResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.asset.ReceiveMemberPointDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.MemberPointOpDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberSysConfigDecorator;
import com.ly.titc.pms.member.mediator.service.MemberPointAssetOpMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-29 11:33
 */
@Slf4j
@Service
public class MemberPointAssetOpMedServiceImpl implements MemberPointAssetOpMedService {
    @Resource
    private MemberSysConfigDecorator sysConfigDecorator;
    @Resource
    private MemberPointOpDecorator pointOpDecorator;
    @Resource
    private MemberAssetMedConverter assetConverter;
    @Override
    public MemberRecordOPResultDto receive(ReceiveMemberPointDto dto) {
        //todo 校验会员的有效性
        if(dto.getScore() <=0){
            throw new ServiceException("积分数必须大于0", RespCodeEnum.CODE_400.getCode());
        }
        ReceiveMemberPointReq req =  assetConverter.convertReceive(dto);
        if(StringUtils.isEmpty(dto.getExpireDate())) {
            String expireDate = sysConfigDecorator.getPointExpireDate(assetConverter.convert(dto));
            req.setExpireDate(expireDate);
        }
        MemberRecordOPResultResp resultResp = pointOpDecorator.receive(req);
        return assetConverter.convert(resultResp);
    }

    @Override
    public MemberRecordOPResultDto consume(ConsumeMemberPointDto dto) {
        MemberRecordOPResultResp resultResp = pointOpDecorator.consume(assetConverter.convertConsume(dto));
        return assetConverter.convert(resultResp);
    }
}
