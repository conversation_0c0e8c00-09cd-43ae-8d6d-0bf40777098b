package com.ly.titc.pms.member.mediator.entity.dto.schedule;

import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberRelegationRuleDto
 * @Date：2024-11-21 14:46
 * @Filename：MemberRelegationRuleDto
 */
@Data
public class MemberRelegationRuleDto extends ScheduleDataDto {

    /**
     * id
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 会员保级等级
     */
    private Integer sourceLevel;

    /**
     * 保级成功执行类型：ALL-全部条件;ANY-满足任一个条件
     */
    private String relegationSuccessfulPerformType;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 保级失败降至指定等级
     */
    private Integer targetLevel;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 无效 1 正常
     */
    private Integer state;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    private List<MemberRelegationRuleDetailDto> details;
}
