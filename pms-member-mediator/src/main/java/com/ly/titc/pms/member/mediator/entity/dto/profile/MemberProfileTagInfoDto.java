package com.ly.titc.pms.member.mediator.entity.dto.profile;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 会员标签
 *
 * <AUTHOR>
 * @date 2024/11/19 17:55
 */
@Data
public class MemberProfileTagInfoDto {

    /**
     * 会员标签编号
     */
    private Long tagNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 标签分类
     */
    private Integer tagType;

    /**
     * 标签分类
     */
    private String tagTypeDesc;

    /**
     * 打标分类: 1:手动标记 2:系统标记
     */
    private Integer markType;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
