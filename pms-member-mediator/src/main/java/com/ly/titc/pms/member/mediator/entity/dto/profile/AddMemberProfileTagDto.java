package com.ly.titc.pms.member.mediator.entity.dto.profile;

import lombok.Data;

/**
 * 会员标签
 *
 * <AUTHOR>
 * @date 2024/11/19 17:55
 */
@Data
public class AddMemberProfileTagDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员标签编号
     */
    private Long tagNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 标签分类
     */
    private Integer tagType;

    /**
     * 打标分类: 1:手动标记 2:系统标记
     */
    private Integer markType;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 平台渠道
     */
    private String platformChannel;
}
