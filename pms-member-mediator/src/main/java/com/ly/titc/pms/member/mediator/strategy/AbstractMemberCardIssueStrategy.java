package com.ly.titc.pms.member.mediator.strategy;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dubbo.enums.LevelChangeTypeEnum;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardRequestDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardResultDto;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.titc.pms.member.biz.MemberCardInfoBiz;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.common.exceptions.ServiceException;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 会员卡发放策略抽象类
 * 提供通用的模板方法和公共逻辑
 *
 * <AUTHOR>
 * @date 2025年06月26日
 */
@Slf4j
public abstract class AbstractMemberCardIssueStrategy implements MemberCardIssueStrategy {

    @Autowired
    protected MemberCardInfoDecorator memberCardInfoDecorator;

    @Autowired
    protected MemberCardMedService memberCardMedService;

    @Autowired
    protected MemberCardInfoBiz memberCardInfoBiz;

    @Override
    public final IssueMemberCardResultDto execute(IssueMemberCardRequestDto dto) {
        try {
            // 1. 参数校验
            validateRequest(dto);
            // 2. 业务前置处理
            preProcess(dto);
            // 3. 执行具体业务逻辑
            return doExecute(dto);
        } catch (ServiceException e) {
            log.error("发放会员卡业务异常: {}", e.getMessage(), e);
            throw e; // 重新抛出业务异常
        } catch (Exception e) {
            log.error("发放会员卡系统异常: {}", e.getMessage(), e);
            throw new ServiceException(RespCodeEnum.CODE_500);
        }
    }

    /**
     * 参数校验
     *
     * @param dto 请求参数
     */
    protected void validateRequest(IssueMemberCardRequestDto dto) {
        // 校验卡信息
        if (dto.getDefaultCard() == null || dto.getDefaultCard() == 0) {
            // 非默认卡需要传入cardId
            if (dto.getCardId() == null) {
                throw new ServiceException(RespCodeEnum.MEMBER_10013);
            }
        }
    }

    /**
     * 业务前置处理
     *
     * @param dto 请求参数
     */
    protected void preProcess(IssueMemberCardRequestDto dto) {
        // 默认无需前置处理
    }

    /**
     * 执行具体的业务逻辑（由子类实现）
     *
     * @param dto 请求参数
     * @return 执行结果
     */
    protected abstract IssueMemberCardResultDto doExecute(IssueMemberCardRequestDto dto);

    /**
     * 获取目标卡ID
     *
     * @param dto 请求参数
     * @return 卡ID
     */
    protected Long getTargetCardId(IssueMemberCardRequestDto dto) {
        if (dto.getDefaultCard() != null && dto.getDefaultCard() == 1) {
            // 发放默认卡，获取默认卡配置
            MemberCardConfigResp defaultCardConfig = memberCardInfoDecorator
                    .getDefaultMemberCardConfigInfo(dto.getMasterCode(), dto.getMasterType());
            if (defaultCardConfig == null) {
                throw new RuntimeException("未找到默认卡配置");
            }
            return defaultCardConfig.getId();
        } else {
            // 非默认卡，直接使用传入的cardId
            return dto.getCardId();
        }
    }

    /**
     * 获取会员卡等级名称
     *
     * @param dto 请求参数
     * @return 等级名称
     */
    protected String getCardLevelName(IssueMemberCardRequestDto dto) {
        try {
            MemberCardConfigResp memberCardConfig = memberCardInfoDecorator
                    .getMemberCardConfigInfo(dto.getMasterType(), dto.getMasterCode(), dto.getCardId());
            if (memberCardConfig != null && !CollectionUtils.isEmpty(memberCardConfig.getCardLevelConfigs())) {
                return memberCardConfig.getCardLevelConfigs().stream()
                        .filter(config -> config.getCardLevel().equals(dto.getCardLevel()))
                        .map(MemberCardLevelConfigInfoResp::getCardLevelName)
                        .findFirst()
                        .orElse("未知等级");
            }
        } catch (Exception e) {
            log.warn("获取会员卡等级名称失败", e);
        }
        return "未知等级";
    }

    /**
     * 获取当前卡等级名称
     *
     * @param masterType 归属类型
     * @param masterCode 归属值
     * @param cardId 卡ID
     * @param cardLevel 卡等级
     * @return 等级名称
     */
    protected String getCurrentCardLevelName(Integer masterType, String masterCode, Long cardId, Integer cardLevel) {
        try {
            MemberCardConfigResp memberCardConfig = memberCardInfoDecorator
                    .getMemberCardConfigInfo(masterType, masterCode, cardId);
            if (memberCardConfig != null && !CollectionUtils.isEmpty(memberCardConfig.getCardLevelConfigs())) {
                return memberCardConfig.getCardLevelConfigs().stream()
                        .filter(config -> config.getCardLevel().equals(cardLevel))
                        .map(MemberCardLevelConfigInfoResp::getCardLevelName)
                        .findFirst()
                        .orElse("未知等级");
            }
        } catch (Exception e) {
            log.warn("获取当前会员卡等级名称失败", e);
        }
        return "未知等级";
    }

    /**
     * 升级会员卡的通用方法
     *
     * @param dto 请求参数
     * @param memberNo 会员号
     * @param currentCard 当前卡信息（可为null，表示新发卡）
     * @return 处理结果
     */
    protected IssueMemberCardResultDto upgradeMemberCard(IssueMemberCardRequestDto dto, String memberNo, MemberCardInfo currentCard) {
        try {
            // 构建升级DTO
            UpdateCardLevelDto updateDto = buildUpdateCardLevelDto(dto, memberNo, currentCard);
            // 执行升级
            memberCardMedService.updateCardLevel(updateDto);
            // 获取等级名称
            String cardLevelName = getCardLevelName(dto);
            return IssueMemberCardResultDto.success(
                    memberNo,
                    currentCard != null ? currentCard.getMemberCardNo() : "",
                    dto.getCardLevel(),
                    cardLevelName,
                    "UPGRADE"
            );
        } catch (Exception e) {
            log.error("会员升级失败: memberNo={}, error={}", memberNo, e.getMessage(), e);
            return IssueMemberCardResultDto.failure("会员升级失败: " + e.getMessage());
        }
    }

    /**
     * 构建升级DTO的通用方法
     *
     * @param dto 请求参数
     * @param memberNo 会员号
     * @param currentCard 当前卡信息（可为null）
     * @return 升级DTO
     */
    protected UpdateCardLevelDto buildUpdateCardLevelDto(IssueMemberCardRequestDto dto, String memberNo, MemberCardInfo currentCard) {
        UpdateCardLevelDto updateDto = new UpdateCardLevelDto();
        updateDto.setMemberNo(memberNo);
        if (currentCard != null) {
            updateDto.setMemberCardNo(currentCard.getMemberCardNo());
            updateDto.setPreLevel(currentCard.getCardLevel());
            // 获取当前等级名称
            String preLevelName = getCurrentCardLevelName(dto.getMasterType(), dto.getMasterCode(),
                    currentCard.getCardId(), currentCard.getCardLevel());
            updateDto.setPreLevelName(preLevelName);
        }
        updateDto.setAfterLevel(dto.getCardLevel());
        // 获取目标等级名称
        String afterLevelName = getCardLevelName(dto);
        updateDto.setAfterLevelName(afterLevelName);
        updateDto.setReason(StringUtils.isNotBlank(dto.getReason()) ? dto.getReason() : "升级会员卡");
        updateDto.setOperator(dto.getBizType());
        updateDto.setChangeType(LevelChangeTypeEnum.UPGRADE.getType());
        updateDto.setCardId(dto.getCardId());
        updateDto.setBizType(dto.getBizType());
        updateDto.setBizNo(dto.getBizNo());
        // 设置卡片有效期
        setUpdateCardValidityPeriod(updateDto, dto);
        return updateDto;
    }

    /**
     * 设置升级卡片有效期
     */
    private void setUpdateCardValidityPeriod(UpdateCardLevelDto updateDto, IssueMemberCardRequestDto dto) {
        List<MemberCardLevelConfigInfoResp> cardLevelList = memberCardInfoDecorator
                .listMemberCardLevel(Arrays.asList(dto.getCardId()));

        Optional<MemberCardLevelConfigInfoResp> optionCardLevel = cardLevelList.stream()
                .filter(e -> e.getCardLevel().equals(dto.getCardLevel()))
                .findFirst();

        if (!optionCardLevel.isPresent()) {
            throw new ServiceException(RespCodeEnum.MEMBER_10018);
        }

        MemberCardLevelConfigInfoResp carLevelConfig = optionCardLevel.get();
        updateDto.setIsLongTerm(carLevelConfig.getIsLongTerm());

        if (carLevelConfig.getIsLongTerm() == 0 && carLevelConfig.getValidPeriod() > 0) {
            // 非长期有效
            Integer validPeriod = carLevelConfig.getValidPeriod();
            updateDto.setEffectBeginDate(DateUtil.formatDate(new Date()));
            updateDto.setEffectEndDate(DateUtil.offset(new Date(), DateField.DAY_OF_YEAR, validPeriod).toDateStr());
        }
    }

    /**
     * 构建会员卡信息的通用方法
     *
     * @param dto 请求参数
     * @return 会员卡信息
     */
    protected IssueMemberCardDto buildMemberCardInfo(IssueMemberCardRequestDto dto) {
        IssueMemberCardDto memberCardInfo = new IssueMemberCardDto();
        // 设置卡ID
        if (dto.getDefaultCard() != null && dto.getDefaultCard() == 1) {
            // 发放默认卡逻辑
            MemberCardConfigResp defaultMemberCard = memberCardInfoDecorator.getDefaultMemberCardConfigInfo(dto.getMasterCode(), dto.getMasterType());
            memberCardInfo.setCardId(defaultMemberCard.getId());
        } else {
            memberCardInfo.setCardId(dto.getCardId());
        }
        memberCardInfo.setCardLevel(dto.getCardLevel());
        // 查询卡配置信息
        MemberCardConfigResp memberCardConfig = memberCardInfoDecorator.getMemberCardConfigInfo(
                dto.getMasterType(), dto.getMasterCode(), memberCardInfo.getCardId());
        if (memberCardConfig == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10013);
        }
        if (!CollectionUtils.isEmpty(memberCardConfig.getCardLevelConfigs())) {
            // 获取等级名称
            Optional<String> optionalCardLevelName = memberCardConfig.getCardLevelConfigs().stream()
                    .filter(config -> config.getCardLevel().equals(dto.getCardLevel()))
                    .map(MemberCardLevelConfigInfoResp::getCardLevelName)
                    .findFirst();

            if (!optionalCardLevelName.isPresent()) {
                // 卡片等级不存在
                throw new ServiceException(RespCodeEnum.MEMBER_10005);
            }
            memberCardInfo.setCardLevelName(optionalCardLevelName.get());
        }
        memberCardInfo.setCardType(memberCardConfig.getCardType());
        // 生成卡号
        memberCardInfo.setMemberCardNo(memberCardMedService.generateCardNo(
                dto.getMasterType(), dto.getMasterCode(), memberCardInfo.getCardId()));
        // 设置发卡酒店信息
        if (StringUtils.isNotBlank(dto.getHotelCode())) {
            memberCardInfo.setIssueHotelType("HOTEL");
            memberCardInfo.setIssueHotel(dto.getHotelCode());
        } else {
            memberCardInfo.setIssueHotelType("BLOC");
        }
        memberCardInfo.setChangeType(ChangeTypeEnum.ISSUE.getType());
        memberCardInfo.setIssueUser(dto.getBizType());
        // 设置卡片有效期
        setCardValidityPeriod(memberCardInfo, dto);
        // 业务
        memberCardInfo.setBizType(dto.getBizType());
        memberCardInfo.setBizNo(dto.getBizNo());
        return memberCardInfo;
    }

    /**
     * 设置卡片有效期
     */
    private void setCardValidityPeriod(IssueMemberCardDto memberCardInfo, IssueMemberCardRequestDto dto) {
        List<MemberCardLevelConfigInfoResp> cardLevelList = memberCardInfoDecorator
                .listMemberCardLevel(Arrays.asList(memberCardInfo.getCardId()));

        Optional<MemberCardLevelConfigInfoResp> optionCardLevel = cardLevelList.stream()
                .filter(e -> e.getCardLevel().equals(dto.getCardLevel()))
                .findFirst();

        if (!optionCardLevel.isPresent()) {
            throw new ServiceException(RespCodeEnum.MEMBER_10018);
        }

        MemberCardLevelConfigInfoResp carLevelConfig = optionCardLevel.get();
        memberCardInfo.setIsLongTerm(carLevelConfig.getIsLongTerm());

        if (carLevelConfig.getIsLongTerm() == 0 && carLevelConfig.getValidPeriod() > 0) {
            // 非长期有效
            Integer validPeriod = carLevelConfig.getValidPeriod();
            memberCardInfo.setEffectBeginDate(DateUtil.formatDate(new Date()));
            memberCardInfo.setEffectEndDate(DateUtil.offset(new Date(), DateField.DAY_OF_YEAR, validPeriod).toDateStr());
        }
    }

    /**
     * 给已有会员发放新的会员卡
     */
    protected IssueMemberCardResultDto issueNewCardForExistingMember(IssueMemberCardRequestDto dto, String memberNo, Long targetCardId) {
        // 构建会员卡信息
        IssueMemberCardDto memberCardInfo = buildMemberCardInfo(dto);
        memberCardInfo.setMemberNo(memberNo);
        memberCardInfo.setCardId(targetCardId);
        // 执行发卡
        String memberCardNo = memberCardMedService.issueCard(memberCardInfo);
        // 获取等级名称
        String cardLevelName = memberCardInfo.getCardLevelName();
        return IssueMemberCardResultDto.success(
                memberNo,
                memberCardNo,
                dto.getCardLevel(),
                cardLevelName,
                "ISSUE_NEW_CARD"
        );
    }

    /**
     * 处理已存在会员的升级或发卡逻辑（通用方法）
     */
    protected IssueMemberCardResultDto handleExistingMemberCardOperation(IssueMemberCardRequestDto dto, String memberNo) {
        // 获取会员当前所有卡信息
        List<MemberCardInfo> memberCards = memberCardInfoBiz.listByMemberNo(dto.getMasterType(), dto.getMasterCode(), memberNo);
        // 确定目标卡ID
        Long targetCardId = getTargetCardId(dto);
        dto.setCardId(targetCardId);
        // 查找指定cardId的卡
        MemberCardInfo existingCard = findExistingCard(memberCards, targetCardId);

        if (existingCard != null) {
            // 已有该类型卡，走升级逻辑（等级必须更高）
            if (dto.getCardLevel() <= existingCard.getCardLevel()) {
                throw new ServiceException(RespCodeEnum.MEMBER_10015);
            }
            log.info("会员{}升级卡{}从等级{}到等级{}", memberNo, targetCardId, existingCard.getCardLevel(), dto.getCardLevel());
            return upgradeMemberCard(dto, memberNo, existingCard);
        } else {
            // 无该类型卡，新发放该类型卡
            log.info("会员{}新发放卡{}等级{}", memberNo, targetCardId, dto.getCardLevel());
            return issueNewCardForExistingMember(dto, memberNo, targetCardId);
        }
    }

    /**
     * 查找已存在的卡
     */
    protected MemberCardInfo findExistingCard(List<MemberCardInfo> memberCards, Long targetCardId) {
        if (CollectionUtils.isEmpty(memberCards)) {
            return null;
        }
        return memberCards.stream()
                .filter(card -> card.getState().equals(1)) // 有效状态
                .filter(card -> card.getCardId().equals(targetCardId)) // 目标卡ID
                .findFirst()
                .orElse(null);
    }
}
