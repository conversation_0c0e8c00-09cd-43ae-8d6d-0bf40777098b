package com.ly.titc.pms.member.mediator.rpc.dubbo.cashier;


import com.ly.titc.cashier.dubbo.entity.request.SelectReq;
import com.ly.titc.cashier.dubbo.entity.request.config.*;
import com.ly.titc.cashier.dubbo.entity.response.SelectResp;
import com.ly.titc.cashier.dubbo.entity.response.config.PayCashierConfigResp;
import com.ly.titc.cashier.dubbo.entity.response.config.PayCashierConfigSaveResultResp;
import com.ly.titc.cashier.dubbo.interfaces.config.PayCashierConfigDubboService;
import com.ly.titc.common.entity.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-2 14:35
 */
@Slf4j
@Component
public class PayCashierConfigDecorator {

    @DubboReference(group = "${cashier-dsf-dubbo-group}")
    private PayCashierConfigDubboService cashierConfigDubboService;

    /**
     * 根据来源和来源编码支付产品获取收银设置
     */
    public PayCashierConfigResp getByBlocSourceCode(PayCashierSourceGetReq req){
        Response<PayCashierConfigResp> response =  cashierConfigDubboService.getByBlocSourceCode(req);
        return Response.getValidateData(response);

    }

    /**
     * 收银设置保存
     */
    public List<PayCashierConfigSaveResultResp> save(PayCashierConfigSaveReq req){
        Response<List<PayCashierConfigSaveResultResp>> response = cashierConfigDubboService.save(req);
        return Response.getValidateData(response);
    }

    /**
     * 分页查询设置
     */

    public List<PayCashierConfigResp> listConfig(PayCashierConfigReq req){
        Response<List<PayCashierConfigResp>> response = cashierConfigDubboService.listConfig(req);
        return Response.getValidateData(response);
    }


    /**
     * 获取详情
     */
    public PayCashierConfigResp detail(GetByIdReq req) {
        Response<PayCashierConfigResp> response = cashierConfigDubboService.detail(req);
        return Response.getValidateData(response);
    }

    /**
     * 删除收银设置
     */
    public Boolean delete(PayCashierConfigDeleteReq req) {
        Response<Boolean> response = cashierConfigDubboService.delete(req);
        return Response.getValidateData(response);
    }
    /**
     * 查询集团和酒店已设置产品和场景
     * （未设置的消息提醒需要根据已设置的反向推）
     */
    public List<PayCashierConfigSaveResultResp> listBlocConfigMapping(PayCashierBlocConfigReq req){
        Response<List<PayCashierConfigSaveResultResp>> response = cashierConfigDubboService.listBlocConfigMapping(req);
        return Response.getValidateData(response);
    }

    /**
     * 收银产品下拉框
     */
    public List<SelectResp> selectCashierProduct(SelectReq req) {
        Response<List<SelectResp>> response = cashierConfigDubboService.selectCashierProduct(req);
        return Response.getValidateData(response);
    }

    /**
     *  收银场景下拉框
     */
    public  List<SelectResp> selectCashierScene(SelectReq req){
        Response<List<SelectResp>> response = cashierConfigDubboService.selectCashierScene(req);
        return Response.getValidateData(response);
    }

    /**
     * 显示收银付款方式下拉框
     */
    public List<SelectResp> selectDisplayCashierPayChannel(SelectReq req){
        Response<List<SelectResp>> response = cashierConfigDubboService.selectDisplayCashierPayChannel(req);
        return Response.getValidateData(response);
    }
}
