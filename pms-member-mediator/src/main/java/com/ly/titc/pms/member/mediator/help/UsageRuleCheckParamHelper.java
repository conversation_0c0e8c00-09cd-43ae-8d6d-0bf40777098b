package com.ly.titc.pms.member.mediator.help;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ly.titc.common.enums.MasterTypeEnum;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.biz.AssetUsageRuleScopeMappingBiz;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageRuleScopeMapping;
import com.ly.titc.pms.member.entity.bo.PageUsageRuleScopeMappingBo;
import com.ly.titc.pms.member.mediator.converter.UsageMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.SaveUsageRuleResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 使用规则参数校验帮助类
 *
 * <AUTHOR>
 * @date 2025/6/25 20:54
 */
@Slf4j
@Component
public class UsageRuleCheckParamHelper {

    @Resource(type = UsageMedConverter.class)
    @Qualifier("usageMedConverterImpl")
    private UsageMedConverter usageMedConverter;
    @Resource
    private AssetUsageRuleScopeMappingBiz scopeMappingBiz;

    public void checkArgUsage(SaveUsageRuleDto dto) {
        Integer masterType = dto.getMasterType();
        String blocCode = dto.getBlocCode();
        Integer hotelCode = dto.getHotelCode();
        String clubCode = dto.getClubCode();
        List<String> scopeSources = dto.getScopeSources();
        List<String> scopeHotelCodes = dto.getScopeHotelCodes();

        if (masterType.equals(MasterTypeEnum.BLOC.getType())) {
            if (ObjectUtils.isEmpty(blocCode)) {
                log.error("集团编码不能为空");
                throw new ServiceException("集团编码不能为空", RespCodeEnum.CODE_400.getCode());
            }
        }

        if (masterType.equals(MasterTypeEnum.HOTEL.getType())) {
            if (ObjectUtils.isEmpty(blocCode)) {
                log.error("集团编码不能为空");
                throw new ServiceException("集团编码不能为空", RespCodeEnum.CODE_400.getCode());
            }
            if (ObjectUtils.isEmpty(hotelCode)) {
                log.error("酒店编码不能为空");
                throw new ServiceException("酒店编码不能为空", RespCodeEnum.CODE_400.getCode());
            }
        }

        if (masterType.equals(MasterTypeEnum.CLUB.getType())) {
            if (ObjectUtils.isEmpty(clubCode)) {
                log.error("酒馆编码不能为空");
                throw new ServiceException("酒馆编码不能为空", RespCodeEnum.CODE_400.getCode());
            }
        }

        if (scopeSources.contains(ScopeSourceEnum.HOTEL.getCode())) {
            if (CollectionUtils.isEmpty(scopeHotelCodes)) {
                throw new ServiceException("适用来源为门店时必填", RespCodeEnum.CODE_400.getCode());

            }
        }
    }

    public List<SaveUsageRuleResultDto> checkRepeat(SaveUsageRuleDto dto, String assetType) {
        List<SaveUsageRuleResultDto> resultList = new ArrayList<>();
        //club bloc维度设置暂不考虑
        if (dto.getScopeSources().contains(ScopeSourceEnum.HOTEL.getCode())) {
            List<SaveUsageRuleResultDto> hotelRepeatList = checkRepeatHotel(dto, assetType);
            resultList.addAll(hotelRepeatList);
        }
        if (dto.getScopeSources().contains(ScopeSourceEnum.BLOC.getCode())) {
            List<SaveUsageRuleResultDto> blocRepeatList = checkRepeatBloc(dto, assetType);
            resultList.addAll(blocRepeatList);
        }
        //编辑的时候校验唯一性
        if (dto.getId() != null) {
            //剔除ruleId同id相同的list
            resultList.removeIf(result -> result.getRuleId().equals(dto.getId()));
        }
        return resultList;
    }

    /**
     * 来源是酒店判断重复
     */
    private List<SaveUsageRuleResultDto> checkRepeatHotel(SaveUsageRuleDto dto, String assetType) {
        PageUsageRuleScopeMappingBo query = usageMedConverter.convertDtoToBo(dto.getScopeHotelCodes(), dto.getScopePlatformChannels());
        List<AssetUsageRuleScopeMapping> scopeMappings = scopeMappingBiz.listByScopeSourceCode(assetType, query);
        return usageMedConverter.convertResult(scopeMappings);
    }


    /**
     * 来源是集团判断重复
     */
    private List<SaveUsageRuleResultDto> checkRepeatBloc(SaveUsageRuleDto dto, String assetType) {
        PageUsageRuleScopeMappingBo query = usageMedConverter.convertDtoToBo(dto.getBlocCode(), dto.getScopePlatformChannels());
        List<AssetUsageRuleScopeMapping> scopeMappings = scopeMappingBiz.listByScopeSourceCode(assetType, query);
        return usageMedConverter.convertResult(scopeMappings);
    }
}
