package com.ly.titc.pms.member.mediator.manager;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.mediator.handler.pay.AbstractPayHandler;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 10:37
 */
public class OrderPayManager {
    private static  final Map<String, AbstractPayHandler> instance = new HashMap<>();

    /**
     * 处理类
     * @param key
     * @param handler
     */
    public static void putInstance(String key ,AbstractPayHandler handler){
        instance.put(key,handler);
    }

    /**
     * 获取场景处理类
     */
    public static  AbstractPayHandler getInstance(String scene){
        AbstractPayHandler handler = instance.get(scene);
        if(handler ==null){
            throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00001);
        }
        return handler;
    }
}
