package com.ly.titc.pms.member.mediator.entity.dto.order;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-26 11:52
 */
@Data
@Accessors(chain = true)
public class CreateOrderDto<T> {

    /**
     * 会员业务场景
     */
    private String memberScene;

    /**
     * 会员业务场景描述
     */
    private String memberSceneDesc;

    /**
     * 会员业务场景的参数
     */
    private T memberSceneNoteDto;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    private String masterCode;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 集团组code
     */
    private String clubCode;

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 单价(不变)
     */
    private BigDecimal price;

    /**
     * 数量（新增）
     */
    private Integer num;

    /**
     * 金额类型
     * todo 看活动的定义
     */
    private String amountType;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 活动信息
     */
    private ActivityOrderDto activityOrderDto;

    /**
     * 操作人
     */
    private String operator;


    /**
     * 会员订单号（保存成功后会写）
     */
    private String memberOrderNo;
}
