package com.ly.titc.pms.member.job.controller;

import com.ly.titc.common.constants.Constant;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @classname CheckHealthController
 * @descrition
 * @since 2019/10/22 18:41
 */
@Controller
public class CheckHealthController {


    /**
     * 注意注意注意：
     * 需要运维在docker上手动加上移除负载命令；http://localhost:${PORT0}/member/health?load=off
     * 健康检测
     *
     * @param load on/off
     * @return
     */
    @RequestMapping("/health")
    public ResponseEntity<String> check(@RequestParam(value = "load", required = false) String load) {

        //发布需要下负载，依赖健康检测;off标识下负载；
        if (Constant.OFF.equalsIgnoreCase(load)) {
            return new ResponseEntity<>(Constant.FAIL, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return new ResponseEntity<>(Constant.OK, HttpStatus.OK);
    }
}
