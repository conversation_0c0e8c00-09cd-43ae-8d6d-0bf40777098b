package com.ly.titc.pms.member.job.controller;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.factory.RedisFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/redis")
@Slf4j
public class RedisController {

    @Autowired
    private RedisFactory redisFactory;

    @GetMapping("/get")
    public Response<String> get(@RequestParam String key) {
        return Response.success(redisFactory.getString(key));
    }

    @GetMapping("/delete")
    public Response<String> delete(@RequestParam String key) {
        redisFactory.del(key);
        return Response.success(null);
    }

    @GetMapping("/deleteAll")
    public Response<String> deleteAll() {
        log.info("delete all begin...");
        List<String> keys = redisFactory.getKeys("");
        if (CollectionUtils.isEmpty(keys)) {
            return Response.success(null);
        }
        keys.forEach(key -> redisFactory.del(key));
        return Response.success(null);
    }
}
