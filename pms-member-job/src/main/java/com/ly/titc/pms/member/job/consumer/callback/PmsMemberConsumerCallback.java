package com.ly.titc.pms.member.job.consumer.callback;

import com.alibaba.rocketmq.common.message.MessageExt;
import com.google.common.base.Stopwatch;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.member.job.consumer.TurbomqConsumerCallback;
import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTopicHandler;
import com.ly.titc.springboot.mq.manager.TurboMQTopicHandlerManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @classname PmsMemberConsumerCallback
 * @descrition
 * @since 2020/5/28 20:08
 */
@Slf4j
@Component
public class PmsMemberConsumerCallback implements TurbomqConsumerCallback {

    @Override
    public boolean receive(List<MessageExt> msgs) {

        TraceNoUtil.initTraceNo();
        if (CollectionUtils.isEmpty(msgs)) {
            log.warn("message is empty");
            return true;
        }
        //默认一条消息记录
        MessageExt msg = msgs.get(0);
        String topic = msg.getTopic();
        String messageId = msg.getMsgId();
        String tag = msg.getTags();
        String msgBody = new String(msg.getBody(), StandardCharsets.UTF_8);
        log.info("消费者收到消息,topic:{},tag:{},messageId:{},msg:{}", topic, tag, messageId, msgBody);
        AbstractTurboMQTopicHandler handler = TurboMQTopicHandlerManager.getInstance(topic);
        if (null == handler) {
            log.warn("this topic has not handler!!topic:{};messageId:{}", topic, messageId);
            return true;
        }
        Stopwatch stopwatch = Stopwatch.createStarted();
        boolean result = handler.execute(tag, messageId, msgBody);
        log.info("topic:{},tag:{},messageId:{},msg:{},消费耗时：{}s", topic, tag, messageId, msgBody, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return result;
    }
}
