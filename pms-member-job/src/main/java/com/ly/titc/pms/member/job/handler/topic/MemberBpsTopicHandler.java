package com.ly.titc.pms.member.job.handler.topic;

import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.constant.TurboMqTopicTag;
import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTagHandler;
import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTopicHandler;
import com.ly.titc.springboot.mq.manager.TurboMQTagHandlerManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 会员业务处理
 *
 * <AUTHOR>
 * @date 2024/11/11 11:04
 */
@Slf4j
@Component
public class MemberBpsTopicHandler extends AbstractTurboMQTopicHandler {

    @Override
    public boolean execute(String tag, String messageId, String msg) {
        AbstractTurboMQTagHandler handler = TurboMQTagHandlerManager.getInstance(tag);
        if (Objects.isNull(handler)) {
            log.warn("this tag has not handler to process!!tag:{};messageId:{}", tag, messageId);
            return true;
        }
        try {
            return handler.execute(messageId, msg);
        } catch (Exception e) {
            log.error("titc_pms_member_bps_topic error, tag:{}, messageId:{}, msg:{}", tag, messageId, msg, e);
            return false;
        }
    }

    @Override
    public String getTopic() {
        return TurboMqTopic.PMS_MEMBER_BPS_TOPIC;
    }
}
