package com.ly.titc.pms.member.job.listener;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.mediator.entity.dto.order.GetRefundStateDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.RefundResultDto;
import com.ly.titc.pms.member.mediator.entity.message.RefundSuccessInfoMsgExt;
import com.ly.titc.pms.member.mediator.service.MemberOrderMedService;
import com.ly.titc.springboot.mq.starter.annotation.TurboListeners;
import com.ly.titc.springboot.mq.starter.annotation.TurboMqListener;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-7 15:15
 */
@Slf4j
@TurboListeners(topic = "titc_cashier_refund_notify_topic",consumerGroup="titc_ihotel_cashier_refund_notify_group")
public class CashierRefundNotifyListener {

    @Resource
    private MemberOrderMedService memberOrderMedService;

    @TurboMqListener(messageClass = RefundSuccessInfoMsgExt.class,tag = "PMSPAY")
    public void consume(List<RefundSuccessInfoMsgExt> msgs){
        msgs.forEach(msg -> {
            try {
                GetRefundStateDto getRefundStateDto = new GetRefundStateDto();
                getRefundStateDto.setRefundPayNo(msg.getRefundPayNo());
                getRefundStateDto.setRefundTradeNo(msg.getRefundTradeNo());
                getRefundStateDto.setOperator("SYSTEM_USER");
                RefundResultDto refundState = memberOrderMedService.getRefundState(getRefundStateDto);
                log.info("refundState:{}", refundState);
            } catch (ServiceException e) {
                log.error("refundState error, {}", e.getMessage());
            } catch (Exception e) {
                log.error("refundState systemError, ", e);
            }
        });
    }
}
