package com.ly.titc.pms.member.job.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: shawn
 * @email: <EMAIL>
 * @date: 2024/3/4 17:58
 * @description:
 */
@Data
@ConfigurationProperties("turbomq.consumer.member")
public class ConsumerProperties {

	/**
	 * 消费组;job消费组使用同一个
	 */
	private String group;

	/**
	 * nameserver地址
	 */
	private String nameserver;

	/**
	 * 消费者订阅的topic，多个topic之间用英文分号隔开
	 */
	private String subscribeTopics;
}
