package com.ly.titc.pms.member.job.config;

import com.ly.titc.pms.member.job.consumer.PmsMemberConsumer;
import com.ly.titc.pms.member.job.consumer.callback.PmsMemberConsumerCallback;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * @author: shawn
 * @email: <EMAIL>
 * @date: 2024/3/4 17:58
 * @description:
 */
@Configuration
@EnableConfigurationProperties({ConsumerProperties.class})
public class TurboMqConsumerConfig {

	@Resource(type = PmsMemberConsumerCallback.class)
	private PmsMemberConsumerCallback callback;


	private final ConsumerProperties prop;

	public TurboMqConsumerConfig(ConsumerProperties prop) {
		this.prop = prop;
	}

	/**
	 * 初始化consumer
	 *
	 * @return
	 */
	@Bean(name = "memberMessageConsumer", initMethod = "start", destroyMethod = "shutdown")
	public PmsMemberConsumer memberMessageConsumer() {
		PmsMemberConsumer consumer = new PmsMemberConsumer();
		consumer.setGroup(prop.getGroup());
		consumer.setNameserver(prop.getNameserver());
		consumer.setSubscribeTopics(prop.getSubscribeTopics());
		consumer.setCallback(callback);
		return consumer;
	}

}
