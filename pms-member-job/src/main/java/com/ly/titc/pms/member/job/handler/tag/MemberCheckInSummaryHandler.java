package com.ly.titc.pms.member.job.handler.tag;

import com.alibaba.fastjson.JSON;
import com.ly.titc.pms.member.com.constant.TurboMqTopicTag;
import com.ly.titc.pms.member.mediator.entity.message.MemberCheckInSummaryMsgExt;
import com.ly.titc.pms.member.mediator.service.MemberDataRecordMedService;
import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTagHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 入住记录汇总
 *
 * <AUTHOR>
 * @date 2025/5/30 16:32
 */
@Component
@Slf4j
public class MemberCheckInSummaryHandler extends AbstractTurboMQTagHandler {

    @Resource
    private MemberDataRecordMedService memberDataRecordMedService;

    @Override
    public boolean execute(String messageId, String msg) {
        MemberCheckInSummaryMsgExt summaryMsg = JSON.parseObject(msg, MemberCheckInSummaryMsgExt.class);
        memberDataRecordMedService.summaryCheckInRecord(summaryMsg.getMemberNo());
        return true;
    }

    @Override
    public String getTag() {
        return TurboMqTopicTag.CHECK_IN_SUMMARY;
    }
}
