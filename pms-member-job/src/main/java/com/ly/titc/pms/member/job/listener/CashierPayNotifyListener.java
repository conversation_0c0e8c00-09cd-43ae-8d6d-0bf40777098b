package com.ly.titc.pms.member.job.listener;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.com.constant.SystemConstant;
import com.ly.titc.pms.member.mediator.entity.dto.order.GetPayStateDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PayOrderResultDto;
import com.ly.titc.pms.member.mediator.entity.message.PaySuccessInfoMsgExt;
import com.ly.titc.pms.member.mediator.service.MemberOrderMedService;
import com.ly.titc.springboot.mq.starter.annotation.TurboListeners;
import com.ly.titc.springboot.mq.starter.annotation.TurboMqListener;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-7 15:15
 */
@Slf4j
@TurboListeners(topic = "titc_cashier_pay_finish_notify_topic",consumerGroup="member_pay_notify_group")
public class CashierPayNotifyListener {

    @Resource
    private MemberOrderMedService memberOrderMedService;

    @TurboMqListener(messageClass = PaySuccessInfoMsgExt.class,tag = "PMSPAY|MEMBER")
    public void consume(List<PaySuccessInfoMsgExt> msgs){
        msgs.forEach(msg -> {
            try {
                GetPayStateDto getPayStateDto = new GetPayStateDto();
                getPayStateDto.setBlocCode(msg.getBlocCode());
                getPayStateDto.setHotelCode(msg.getHotelCode());
                getPayStateDto.setMemberOrderPayNo(msg.getBizPayNo());
                getPayStateDto.setOperator(msg.getOperator());
                PayOrderResultDto payState = memberOrderMedService.getPayState(getPayStateDto);
                log.info("payState:{}", payState);
            } catch (ServiceException e) {
                log.error("payState error, {}", e.getMessage());
            } catch (Exception e) {
                log.error("payState systemError, ", e);
            }
        });
    }
}
