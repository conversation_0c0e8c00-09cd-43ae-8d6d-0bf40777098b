package com.ly.titc.pms.member.job.consumer;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.common.consumer.ConsumeFromWhere;
import com.ly.titc.common.constants.Constant;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

/**
 * TurboMQ消费者组件<br>
 * <AUTHOR>
 */
@Data
@Slf4j
public class PmsMemberConsumer {

	/**
	 * 消费者组名
	 */
	private String group;

	/**
	 * nameserver地址
	 */
	private String nameserver;

	/**
	 * 消费者订阅的topic，多个topic之间用英文分号隔开
	 */
	private String subscribeTopics;

	/**
	 * 消费者
	 */
	private DefaultMQPushConsumer consumer;

	/**
	 * 消费回调接口
	 */
	private TurbomqConsumerCallback callback;

	/**
	 * 启动客户端
	 *
	 * @throws MQClientException
	 */
	public void start() throws MQClientException {
		start(callback);
	}

	/**
	 * 启动客户端
	 *
	 * @throws MQClientException
	 */
	public void start(TurbomqConsumerCallback callback) throws MQClientException {

		this.callback = callback;
		log.info("=======>  start    <==========");
		this.consumer = new DefaultMQPushConsumer(group);
		this.consumer.setNamesrvAddr(nameserver);
		log.info("=======> consumerGroup:{} <=======",group);

		// 设置订阅的消息topic
		if (ObjectUtils.isEmpty(subscribeTopics)) {
			log.warn("consumer topic is empty!!Don't need to start consumer!!!");
			return;
		}
		// 分号间隔
		for (String topic : this.subscribeTopics.split(Constant.STRING_SEMICOLON)) {
			this.consumer.subscribe(topic, "*");
			log.info("=======>   subscribe topic:{}  <==========", topic);
		}

		// 设置Consumer第一次启动是从队列头部开始消费还是队列尾部开始消费,如果非第一次启动，那么按照上次消费的位置继续消费
		consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET);

		// 设置consumeMessageBatchMaxSize参数来批量接收多条消息
		consumer.setConsumeMessageBatchMaxSize(1);

		// 设置回调
		consumer.registerMessageListener((MessageListenerConcurrently) (msgs, context) -> {
			boolean flag = this.callback.receive(msgs);
			if (flag) {
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			} else {
				return ConsumeConcurrentlyStatus.RECONSUME_LATER;
			}

		});

		this.consumer.start();
	}

	/**
	 * 关闭客户端
	 */
	public void shutdown() {
		this.consumer.shutdown();
	}
}
