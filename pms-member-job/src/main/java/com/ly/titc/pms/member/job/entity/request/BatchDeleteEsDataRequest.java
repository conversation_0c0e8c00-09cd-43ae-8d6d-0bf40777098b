package com.ly.titc.pms.member.job.entity.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量删除ES数据
 *
 * <AUTHOR>
 * @date 2024/12/3 10:13
 */
@Data
public class BatchDeleteEsDataRequest {

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String pwd;

    /**
     * 会员号
     */
    @NotEmpty(message = "会员号不能为空")
    private List<String> memberNos;
}
