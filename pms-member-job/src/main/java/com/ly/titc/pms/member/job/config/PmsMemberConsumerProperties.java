package com.ly.titc.pms.member.job.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @classname PmsMemberConsumerProperties
 * @descrition
 * @since 2020/5/28 17:55
 */
@Data
@ConfigurationProperties("turbo-mq.pms-member.bps.consumer")
public class PmsMemberConsumerProperties {

	/**
	 * 消费组;job消费组使用同一个
	 */
	private String group;

	/**
	 * nameserver地址
	 */
	private String nameserver;

	/**
	 * 消费者订阅的topic，多个topic之间用英文分号隔开
	 */
	private String subscribeTopics;
}
