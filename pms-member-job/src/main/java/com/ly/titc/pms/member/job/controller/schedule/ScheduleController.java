package com.ly.titc.pms.member.job.controller.schedule;

import com.ly.titc.pms.member.job.entity.request.ExecuteScheduleRequest;
import com.ly.titc.pms.member.mediator.handler.schedule.AbstractScheduleHandler;
import com.ly.titc.pms.member.mediator.handler.schedule.ScheduleHandlerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Author：rui
 * @name：ScheduleController
 * @Date：2024-11-22 16:39
 * @Filename：ScheduleController
 */
@RestController
@RequestMapping("/schedule")
public class ScheduleController {


    /**
     * 定时任务执行 升降机、标签打标
     *
     * @param request
     */
    @RequestMapping("/execute")
    public void execute(@RequestBody @Valid ExecuteScheduleRequest request) {
        AbstractScheduleHandler handler = ScheduleHandlerFactory.getHandler(request.getScheduleType());
        handler.doSchedule();
    }
}
