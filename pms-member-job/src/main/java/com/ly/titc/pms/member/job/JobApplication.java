package com.ly.titc.pms.member.job;


import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitScan;
import com.ly.titc.springboot.mq.starter.annotation.EnableTurboMq;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.*;
import org.springframework.http.converter.HttpMessageConverter;

/**
 * <AUTHOR>
 * @ClassName: JobApplication
 * @Description: 启动类
 * @date 2019年5月13日
 */
@SpringBootApplication
@EnableTurboMq
@ComponentScan(basePackages = {"com.ly.titc.pms.member", "com.ly.titc.springboot"})
@RetrofitScan("com.ly.titc.pms.member.facade")
@PropertySource("classpath:dsf_application.properties")
@EnableAspectJAutoProxy(exposeProxy = true)
public class JobApplication {

    /**
     * run
     *
     * @param args
     */
    public static void main(String[] args) {
        SpringApplication.run(JobApplication.class, args);
    }

    /**
     * fastjson converter
     *
     * @return
     */
    @Bean
    public HttpMessageConverters fastJsonHttpMessageConverters() {
        // 1.定义一个converters转换消息的对象
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();
        // 2.添加fastjson的配置信息，比如: 是否需要格式化返回的json数据
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setSerializerFeatures(
                SerializerFeature.DisableCircularReferenceDetect,
                SerializerFeature.WriteNullStringAsEmpty,
                SerializerFeature.WriteNullListAsEmpty,
                SerializerFeature.WriteNullNumberAsZero);
        // 3.在converter中添加配置信息
        fastConverter.setFastJsonConfig(fastJsonConfig);
        // 4.将converter赋值给HttpMessageConverter
        HttpMessageConverter<?> converter = fastConverter;
        // 5.返回HttpMessageConverters对象
        return new HttpMessageConverters(converter);
    }

}
