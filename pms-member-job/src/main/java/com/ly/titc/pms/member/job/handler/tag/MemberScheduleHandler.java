package com.ly.titc.pms.member.job.handler.tag;

import com.alibaba.fastjson.JSON;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberScheduleMqDto;
import com.ly.titc.pms.member.mediator.handler.schedule.AbstractScheduleHandler;
import com.ly.titc.pms.member.mediator.handler.schedule.ScheduleHandlerFactory;
import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTagHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_SCHEDULE;

/**
 * 定时任务处理--升降机、打标
 * @Author：rui
 * @name：MemberScheduleHandler
 * @Date：2024-12-15 0:53
 * @Filename：MemberScheduleHandler
 */
@Component
@Slf4j
public class MemberScheduleHandler extends AbstractTurboMQTagHandler {

    @Override
    public boolean execute(String messageId, String msg) {
        try {

            log.error("收到会员定时任务消息，消息ID:{}, 消息:{}", messageId, msg);
            MemberScheduleMqDto memberEventMsg = JSON.parseObject(msg, MemberScheduleMqDto.class);
            String memberNo = memberEventMsg.getMemberNo();
            Integer masterType = memberEventMsg.getMasterType();
            String masterCode = memberEventMsg.getMasterCode();
            Integer action = memberEventMsg.getAction();
            AbstractScheduleHandler scheduleHandler = ScheduleHandlerFactory.getHandler(action);
            if (scheduleHandler == null) {
                log.error("收到会员定时任务消息，消息ID:{}, 消息:{}, 未找到对应的处理类", messageId, msg);
                return true;
            }
            scheduleHandler.process(masterType, masterCode, memberNo);
            return false;
        } catch (Exception e) {
            log.error("收到会员定时任务消息，消息ID:{}, 消息:{}, 处理失败", messageId, msg, e);
            return false;
        }
    }

    @Override
    public String getTag() {
        return MEMBER_SCHEDULE;
    }
}
