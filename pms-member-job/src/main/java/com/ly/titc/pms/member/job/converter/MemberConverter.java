package com.ly.titc.pms.member.job.converter;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.convert.BaseConverter;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.MemberCardDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.MemberDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.MemberTagDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberCardInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileTagInfoDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/13 14:11
 */
@Mapper(componentModel = "spring")
public interface MemberConverter extends BaseConverter {

    @Mappings({
            @Mapping(target = "id", source = "memberNo"),
            @Mapping(target = "registerDate", source = "gmtCreate", qualifiedByName = "localDateTimeToLong"),
    })
    MemberDocumentDto convertDtoToDto(MemberDetailDto memberInfo);

    List<MemberCardDocumentDto> convertDtoToDto(List<MemberCardInfoDto> memberCardInfos);

    @Mappings({
            @Mapping(target = "effectBeginDate", source = "effectBeginDate", qualifiedByName = "effectBeginDate"),
            @Mapping(target = "effectEndDate", source = "effectEndDate", qualifiedByName = "effectEndDateToLong"),
    })
    MemberCardDocumentDto convertDtoToDto(MemberCardInfoDto memberCardInfo);

    MemberTagDocumentDto convertPoToDto(MemberProfileTagInfoDto info);

    @Named("localDateTimeToLong")
    default Long localDateTimeToLong(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Timestamp.valueOf(localDateTime).getTime();
    }

    @Named("effectBeginDate")
    default Long effectBeginDate(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        LocalDate localDate = LocalDateUtil.parseByNormalDate(date);
        return Timestamp.valueOf(localDate.atTime(0, 0)).getTime();
    }

    @Named("effectEndDateToLong")
    default Long effectEndDateToLong(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        LocalDate localDate = LocalDateUtil.parseByNormalDate(date);
        return Timestamp.valueOf(localDate.atTime(23, 59, 59)).getTime();
    }


}
