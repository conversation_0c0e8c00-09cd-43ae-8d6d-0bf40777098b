package com.ly.titc.pms.member.service;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.pms.member.biz.AssetUsageModeScopeMappingBiz;
import com.ly.titc.pms.member.biz.AssetUsageRuleScopeMappingBiz;
import com.ly.titc.pms.member.biz.PointUsageRuleBiz;
import com.ly.titc.pms.member.biz.StoreUsageRuleBiz;
import com.ly.titc.pms.member.com.enums.StoreUsageRuleModeEnum;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageModeScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageRuleScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.PointUsageRuleInfo;
import com.ly.titc.pms.member.dal.entity.po.StoreUsageRuleInfo;
import com.ly.titc.pms.member.entity.wrapper.PointUsageRuleWrapper;
import com.ly.titc.pms.member.entity.wrapper.StoreUsageRuleWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-15 14:09
 */
@Slf4j
@Service
public class UsageRuleService {
    @Resource
    private PointUsageRuleBiz pointRuleInfoBiz;
    @Resource
    private StoreUsageRuleBiz storeUsageRuleBiz;
    @Resource
    private AssetUsageRuleScopeMappingBiz usageRuleScopeMappingBiz;
    @Resource
    private AssetUsageModeScopeMappingBiz usageModeScopeMappingBiz;

    @Transactional(rollbackFor = Exception.class)
    public void savePointUsageRule(PointUsageRuleWrapper wrapper) {
        PointUsageRuleInfo ruleInfo = wrapper.getRuleInfo();
        List<AssetUsageRuleScopeMapping> scopeMappings = wrapper.getScopeMappings();
        List<AssetUsageModeScopeMapping> usageModeScopeMappings = wrapper.getUsageModeScopeMappings();
        Long ruleId = ruleInfo.getId();
        if (ruleId != null) {
            PointUsageRuleInfo existInDBRule = pointRuleInfoBiz.getById(ruleId);
            ruleInfo.setId(existInDBRule.getId());
            ruleInfo.setCreateUser(existInDBRule.getCreateUser());
            ruleInfo.setGmtCreate(existInDBRule.getGmtCreate());
            pointRuleInfoBiz.updateById(ruleInfo);
            usageRuleScopeMappingBiz.deleteByRuleId(ruleId);
            usageModeScopeMappingBiz.deleteByRuleId(ruleId);
        } else {
            ruleInfo.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
            pointRuleInfoBiz.insert(ruleInfo);
        }
        scopeMappings.forEach(mapping -> {
            mapping.setRuleId(ruleInfo.getId());
            mapping.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
        });
        usageModeScopeMappings.forEach(mapping -> {
            mapping.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
            mapping.setRuleId(ruleInfo.getId());
        });
        usageRuleScopeMappingBiz.insertBatch(scopeMappings);
        usageModeScopeMappingBiz.insertBatch(usageModeScopeMappings);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long saveStoreUsageRule(StoreUsageRuleWrapper wrapper) {
        StoreUsageRuleInfo ruleInfo = wrapper.getRuleInfo();
        List<AssetUsageRuleScopeMapping> scopeMappings = wrapper.getScopeMappings();
        List<AssetUsageModeScopeMapping> usageModeScopeMappings = wrapper.getUsageModeScopeMappings();
        if(StringUtils.isEmpty(ruleInfo.getRuleMode())){
            ruleInfo.setRuleMode(StoreUsageRuleModeEnum.SINGLE_STORE.getCode());
        }
        Long ruleId = ruleInfo.getId();
        if(ruleId !=null){
            StoreUsageRuleInfo existInDBRule = storeUsageRuleBiz.getById(ruleId);
            ruleInfo.setId(existInDBRule.getId());
            ruleInfo.setRuleMode(existInDBRule.getRuleMode());
            ruleInfo.setCreateUser(existInDBRule.getCreateUser());
            ruleInfo.setGmtCreate(existInDBRule.getGmtCreate());
            storeUsageRuleBiz.updateById(ruleInfo);
            usageRuleScopeMappingBiz.deleteByRuleId(ruleId);
            usageModeScopeMappingBiz.deleteByRuleId(ruleId);
        }else{
            ruleInfo.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
            storeUsageRuleBiz.insert(ruleInfo);
        }
        scopeMappings.forEach(mapping -> {
            mapping.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
            mapping.setRuleId(ruleInfo.getId());
        });
        usageModeScopeMappings.forEach(mapping -> {
            mapping.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
            mapping.setRuleId(ruleInfo.getId());
        });
        usageRuleScopeMappingBiz.insertBatch(scopeMappings);
        usageModeScopeMappingBiz.insertBatch(usageModeScopeMappings);
        return ruleInfo.getId();

    }
}
