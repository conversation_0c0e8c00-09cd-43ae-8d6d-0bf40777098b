package com.ly.titc.pms.member.entity.wrapper;

import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 15:25
 */
@Data
@Accessors(chain = true)
public class MemberOrderPayWrapper {

    /**
     * 会员订单主表
     */
    private MemberOrderInfo orderInfo;
    /**
     * 会员支付单
     */
    private MemberOrderPayInfo  payInfo;
}
