package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.pms.member.dal.dao.MemberBlacklistApplicableDataMappingDao;
import com.ly.titc.pms.member.dal.entity.po.MemberBlacklistApplicableDataMapping;
import com.ly.titc.springboot.dcdb.dal.core.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：CustomerBlacklistApplicableDataMappingBiz
 * @Date：2024-12-10 15:32
 * @Filename：CustomerBlacklistApplicableDataMappingBiz
 */
@Slf4j
@Component
public class MemberBlacklistApplicableDataMappingBiz extends AbstractBiz<MemberBlacklistApplicableDataMapping> {

    @Resource
    private MemberBlacklistApplicableDataMappingDao memberBlacklistApplicableDataMappingDao;

    public void insertBatch(List<MemberBlacklistApplicableDataMapping> customerBlacklistApplicableDataMappings) {
        if (CollectionUtils.isNotEmpty(customerBlacklistApplicableDataMappings)) {
            memberBlacklistApplicableDataMappingDao.insertBatch(customerBlacklistApplicableDataMappings);
        }
    }

    public void updateBatch(List<MemberBlacklistApplicableDataMapping> customerBlacklistApplicableDataMappings) {
        if (CollectionUtils.isNotEmpty(customerBlacklistApplicableDataMappings)) {
            for (MemberBlacklistApplicableDataMapping customerBlacklistApplicableDataMapping : customerBlacklistApplicableDataMappings) {
                memberBlacklistApplicableDataMappingDao.updateByPrimaryKey(customerBlacklistApplicableDataMapping);
            }
        }
    }

    public void deleteBatch(List<MemberBlacklistApplicableDataMapping> customerBlacklistApplicableDataMappings, String operator) {
        if (CollectionUtils.isNotEmpty(customerBlacklistApplicableDataMappings)) {
            List<Long> ids = customerBlacklistApplicableDataMappings.stream().map(MemberBlacklistApplicableDataMapping::getId).collect(Collectors.toList());
            memberBlacklistApplicableDataMappingDao.update(null, new LambdaUpdateWrapper<MemberBlacklistApplicableDataMapping>()
                    .in(MemberBlacklistApplicableDataMapping::getId, ids)
                    .set(MemberBlacklistApplicableDataMapping::getIsDelete, 1));
        }
    }

    public void deleteByBlackListNo(String blackListNo) {
        memberBlacklistApplicableDataMappingDao.delete(new LambdaQueryWrapperX<MemberBlacklistApplicableDataMapping>()
                .eq(MemberBlacklistApplicableDataMapping::getBlacklistNo, blackListNo));
    }

    public List<MemberBlacklistApplicableDataMapping> listByBlackListNo(String blackListNo) {
       return memberBlacklistApplicableDataMappingDao.selectList(new LambdaQueryWrapperX<MemberBlacklistApplicableDataMapping>()
                .eq(MemberBlacklistApplicableDataMapping::getBlacklistNo, blackListNo));
    }

    public List<MemberBlacklistApplicableDataMapping> listByBlackListNos(List<String> blackListNos) {
        return memberBlacklistApplicableDataMappingDao.selectList(new LambdaQueryWrapperX<MemberBlacklistApplicableDataMapping>()
                .in(MemberBlacklistApplicableDataMapping::getBlacklistNo, blackListNos));
    }

    public List<MemberBlacklistApplicableDataMapping> listApplicableDataMapping(List<String> blacklistNos, Integer applicationType) {
        return memberBlacklistApplicableDataMappingDao.selectList(new LambdaQueryWrapperX<MemberBlacklistApplicableDataMapping>()
                .in(MemberBlacklistApplicableDataMapping::getBlacklistNo, blacklistNos)
                .eq(MemberBlacklistApplicableDataMapping::getApplicationType, applicationType));
    }
}
