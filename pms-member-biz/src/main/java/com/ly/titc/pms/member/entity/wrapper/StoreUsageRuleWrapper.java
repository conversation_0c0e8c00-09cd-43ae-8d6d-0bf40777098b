package com.ly.titc.pms.member.entity.wrapper;

import com.ly.titc.pms.member.dal.entity.po.AssetUsageModeScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageRuleScopeMapping;
import com.ly.titc.pms.member.dal.entity.po.StoreUsageRuleInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 16:06
 */
@Data
@Accessors(chain = true)
public class StoreUsageRuleWrapper {

    /**
     * 储值规则信息
     */
    private StoreUsageRuleInfo ruleInfo;

    /**
     * 适用范围mapping
     */
    private List<AssetUsageRuleScopeMapping> scopeMappings;

    /**
     * 适用模式mapping
     */
    private List<AssetUsageModeScopeMapping> usageModeScopeMappings;

}
