package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.dal.dao.PrivilegeApplicableDataMappingDao;
import com.ly.titc.pms.member.dal.dao.PrivilegeConfigInfoDao;
import com.ly.titc.pms.member.dal.entity.po.PrivilegeApplicableDataMapping;
import com.ly.titc.pms.member.dal.entity.po.PrivilegeConfigInfo;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 权益设置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:53
 */
@Component
public class PrivilegeConfigBiz {

    @Resource
    private PrivilegeConfigInfoDao privilegeConfigInfoDao;

    @Resource
    private PrivilegeApplicableDataMappingDao privilegeApplicableDataMappingDao;

    public List<PrivilegeConfigInfo> listByIds(List<Long> privilegeIds) {
        LambdaQueryWrapper<PrivilegeConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PrivilegeConfigInfo::getId, privilegeIds);
        return privilegeConfigInfoDao.selectList(wrapper);
    }

    public List<PrivilegeApplicableDataMapping> listMappingByIds(List<Long> privilegeIds) {
        LambdaQueryWrapper<PrivilegeApplicableDataMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PrivilegeApplicableDataMapping::getId, privilegeIds);
        return privilegeApplicableDataMappingDao.selectList(wrapper);
    }

    public void insert(PrivilegeConfigInfo privilegeConfigInfo, List<PrivilegeApplicableDataMapping> mappings) {
        privilegeConfigInfoDao.insert(privilegeConfigInfo);
        privilegeApplicableDataMappingDao.insertBatch(mappings);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(PrivilegeConfigInfo privilegeConfigInfo, List<PrivilegeApplicableDataMapping> mappings) {
        LambdaUpdateWrapper<PrivilegeConfigInfo> wrapper = new LambdaUpdateWrapper<PrivilegeConfigInfo>()
                .eq(PrivilegeConfigInfo::getId, privilegeConfigInfo.getId())
                .set(PrivilegeConfigInfo::getType, privilegeConfigInfo.getType())
                .set(PrivilegeConfigInfo::getClassification, privilegeConfigInfo.getClassification())
                .set(PrivilegeConfigInfo::getName, privilegeConfigInfo.getName())
                .set(PrivilegeConfigInfo::getUnit, privilegeConfigInfo.getUnit())
                .set(PrivilegeConfigInfo::getPic, privilegeConfigInfo.getPic())
                .set(PrivilegeConfigInfo::getInstruction, privilegeConfigInfo.getInstruction())
                .set(PrivilegeConfigInfo::getDescription, privilegeConfigInfo.getDescription())
                .set(PrivilegeConfigInfo::getSort, privilegeConfigInfo.getSort())
                .set(PrivilegeConfigInfo::getState, privilegeConfigInfo.getState())
                .set(PrivilegeConfigInfo::getModifyUser, privilegeConfigInfo.getModifyUser());
        privilegeConfigInfoDao.update(null, wrapper);
        privilegeApplicableDataMappingDao.delete(new LambdaQueryWrapper<PrivilegeApplicableDataMapping>()
                .eq(PrivilegeApplicableDataMapping::getPrivilegeId, privilegeConfigInfo.getId()));
        privilegeApplicableDataMappingDao.insertBatch(mappings);
    }

    public PrivilegeConfigInfo getByName(Integer masterType, String masterCode, String name) {
        LambdaQueryWrapper<PrivilegeConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PrivilegeConfigInfo::getMasterType, masterType)
                .eq(PrivilegeConfigInfo::getMasterCode, masterCode)
                .eq(PrivilegeConfigInfo::getName, name);
        return privilegeConfigInfoDao.selectOne(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByPrivilegeId(Long privilegeId, String operator) {
        LambdaUpdateWrapper<PrivilegeConfigInfo> wrapper = new LambdaUpdateWrapper<PrivilegeConfigInfo>()
                .eq(PrivilegeConfigInfo::getId, privilegeId)
                .set(PrivilegeConfigInfo::getIsDelete, Constant.ONE)
                .set(PrivilegeConfigInfo::getModifyUser, operator);
        privilegeConfigInfoDao.update(null, wrapper);
        privilegeApplicableDataMappingDao.delete(new LambdaQueryWrapper<PrivilegeApplicableDataMapping>()
                .eq(PrivilegeApplicableDataMapping::getPrivilegeId, privilegeId));
    }

    public PrivilegeConfigInfo getById(Long privilegeId) {
        return privilegeConfigInfoDao.selectById(privilegeId);
    }

    public void updateState(Long privilegeId, Integer state, String operator) {
        LambdaUpdateWrapper<PrivilegeConfigInfo> wrapper = new LambdaUpdateWrapper<PrivilegeConfigInfo>()
                .eq(PrivilegeConfigInfo::getId, privilegeId)
                .set(PrivilegeConfigInfo::getState, state)
                .set(PrivilegeConfigInfo::getModifyUser, operator);
        privilegeConfigInfoDao.update(null, wrapper);
    }
}
