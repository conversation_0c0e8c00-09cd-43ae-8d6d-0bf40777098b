package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ly.titc.pms.member.dal.dao.MemberOrderPayInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo;
import com.ly.titc.springboot.dcdb.dal.core.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 15:14
 */
@Slf4j
@Component
public class MemberOrderPayInfoBiz extends AbstractBiz<MemberOrderPayInfo>{
    @Resource
    private MemberOrderPayInfoDao dao;

    public void add(MemberOrderPayInfo info){
        dao.insert(info);
    }


    public List<MemberOrderPayInfo> listByOrderNo(String memberOrderNo){
        return dao.selectList(new LambdaQueryWrapperX<MemberOrderPayInfo>()
                .eq(MemberOrderPayInfo::getMemberOrderNo, memberOrderNo));
    }

    /**
     * 更新交易相关字段
     * @param info
     */

    public void updateTradeState(MemberOrderPayInfo info){
        dao.update(null, Wrappers.lambdaUpdate(MemberOrderPayInfo.class)
                .set(MemberOrderPayInfo::getOrderPayState,info.getOrderPayState())
                .set(MemberOrderPayInfo::getFailReason,info.getFailReason())
                .set(MemberOrderPayInfo::getOnlinePayNo,info.getOnlinePayNo())
                .set(MemberOrderPayInfo::getTransactionId,info.getTransactionId())
                .set(MemberOrderPayInfo::getItemCode,info.getItemCode())
                .set(MemberOrderPayInfo::getItemName,info.getItemName())
                .set(MemberOrderPayInfo::getPayVendor,info.getPayVendor())
                .set(MemberOrderPayInfo::getPayChannel,info.getPayChannel())
                .set(MemberOrderPayInfo::getPayType,info.getPayType())
                .set(MemberOrderPayInfo::getCardNo,info.getCardNo())
                .set(MemberOrderPayInfo::getTermId,info.getTermId())
                .set(MemberOrderPayInfo::getPayedTime,info.getPayedTime())
                .set(MemberOrderPayInfo::getAccountItemNo,info.getAccountItemNo())
                .eq(MemberOrderPayInfo::getMemberOrderPayNo,info.getMemberOrderPayNo())
        );
    }

    public MemberOrderPayInfo getByPayNo(String memberOrderNo, String memberOrderPayNo){
        return dao.selectOne(Wrappers.lambdaQuery(MemberOrderPayInfo.class)
                .eq(StringUtils.isNotBlank(memberOrderNo), MemberOrderPayInfo::getMemberOrderNo,memberOrderNo)
                .eq(MemberOrderPayInfo::getMemberOrderPayNo,memberOrderPayNo));

    }

    public List<MemberOrderPayInfo> listByOrderNoList(List<String> memberOrderNo){
        return dao.selectList(Wrappers.lambdaQuery(MemberOrderPayInfo.class)
                .in(MemberOrderPayInfo::getMemberOrderNo,memberOrderNo));
    }

}
