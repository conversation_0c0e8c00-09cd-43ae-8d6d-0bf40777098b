package com.ly.titc.pms.member.entity.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberCardLevelQuery
 * @Date：2024-10-25 17:16
 * @Filename：MemberCardLevelQuery
 */
@Data
@Accessors(chain = true)
public class PageCardLevelConfigBo {


    private Long cardId;

    private Integer state;

    private Integer level;

    private Integer pageIndex = 1;

    private Integer pageSize = 20;
}
