package com.ly.titc.pms.member.biz;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.common.enums.DeletedStatusEnum;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.dao.MemberProfileOccupantsInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileAddressInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileInvoiceHeaderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileOccupantsInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5 19:42
 */
@Slf4j
@Component
public class MemberProfileOccupantsInfoBiz extends AbstractBiz<MemberProfileOccupantsInfo>{

    @Resource(type = MemberProfileOccupantsInfoDao.class)
    private MemberProfileOccupantsInfoDao memberProfileOccupantsInfoDao;

    public void add(MemberProfileOccupantsInfo entity){
        entity.setOccupantsNo(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
        memberProfileOccupantsInfoDao.insert(entity);
    }

    public void update(MemberProfileOccupantsInfo info) {
        LambdaUpdateWrapper<MemberProfileOccupantsInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberProfileOccupantsInfo::getMemberNo, info.getMemberNo())
                .eq(MemberProfileOccupantsInfo::getOccupantsNo, info.getOccupantsNo());
        wrapper.set(MemberProfileOccupantsInfo::getName, info.getName())
                .set(MemberProfileOccupantsInfo::getMobile, info.getMobile())
                .set(MemberProfileOccupantsInfo::getIdType, info.getIdType())
                .set(MemberProfileOccupantsInfo::getIdNo, info.getIdNo())
                .set(MemberProfileOccupantsInfo::getSort, info.getSort())
                .set(MemberProfileOccupantsInfo::getModifyUser, info.getModifyUser())
                .set(MemberProfileOccupantsInfo::getGmtModified, LocalDateTime.now())
                .set(MemberProfileOccupantsInfo::getTag, info.getTag());
        ;
        memberProfileOccupantsInfoDao.update(null, wrapper);
    }

    public void delete(String memberNo, Long occupantsNo, String operator) {
        LambdaUpdateWrapper<MemberProfileOccupantsInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberProfileOccupantsInfo::getMemberNo, memberNo)
                .eq(MemberProfileOccupantsInfo::getOccupantsNo, occupantsNo);
        wrapper.set(MemberProfileOccupantsInfo::getIsDelete, DeletedStatusEnum.INVALID.getStatus())
                .set(MemberProfileOccupantsInfo::getModifyUser, operator)
                .set(MemberProfileOccupantsInfo::getGmtModified, LocalDateTime.now())
        ;
        memberProfileOccupantsInfoDao.update(null, wrapper);
    }

    public List<MemberProfileOccupantsInfo> listByMemberNo(String memberNo, String name) {
        LambdaQueryWrapper<MemberProfileOccupantsInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberProfileOccupantsInfo::getMemberNo, memberNo)
                .like(StringUtils.isNotBlank(name), MemberProfileOccupantsInfo::getName, name)
                .orderByDesc(MemberProfileOccupantsInfo::getSort)
                .orderByDesc(MemberProfileOccupantsInfo::getGmtCreate);
        return memberProfileOccupantsInfoDao.selectList(wrapper);
    }

    public List<MemberProfileOccupantsInfo> listByMemberNoAndName(String memberNo, String name) {
        LambdaQueryWrapper<MemberProfileOccupantsInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberProfileOccupantsInfo::getMemberNo, memberNo)
                .like(StringUtils.isNotEmpty(name), MemberProfileOccupantsInfo::getName, name)
                .orderByDesc(MemberProfileOccupantsInfo::getSort)
                .orderByDesc(MemberProfileOccupantsInfo::getGmtCreate);
        return memberProfileOccupantsInfoDao.selectList(wrapper);
    }

    public MemberProfileOccupantsInfo getByNo(String memberNo, Long occupantsNo) {
        LambdaQueryWrapper<MemberProfileOccupantsInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberProfileOccupantsInfo::getMemberNo, memberNo)
                .eq(MemberProfileOccupantsInfo::getOccupantsNo, occupantsNo);
        return memberProfileOccupantsInfoDao.selectOne(wrapper);
    }
}
