package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.dal.dao.MemberCheckInStatisticsDao;
import com.ly.titc.pms.member.dal.entity.po.MemberCheckInStatistics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberCheckInStatisticsBiz
 * @Date：2024-12-11 22:00
 * @Filename：MemberCheckInStatisticsBiz
 */
@Slf4j
@Component
public class MemberCheckInStatisticsBiz extends AbstractBiz<MemberCheckInStatistics> {

    @Resource
    private MemberCheckInStatisticsDao memberCheckInStatisticsDao;

    public MemberCheckInStatistics getBlocStatistics(String memberNo) {
        return memberCheckInStatisticsDao.selectOne(new LambdaQueryWrapper<MemberCheckInStatistics>()
                .eq(MemberCheckInStatistics::getMemberNo, memberNo)
                .eq(MemberCheckInStatistics::getHotelCode, String.valueOf(Constant.ZERO)));
    }

    public MemberCheckInStatistics getHotelStatistics(String memberNo, String hotelCode) {
        return memberCheckInStatisticsDao.selectOne(new LambdaQueryWrapper<MemberCheckInStatistics>()
                .eq(MemberCheckInStatistics::getMemberNo, memberNo)
                .eq(MemberCheckInStatistics::getHotelCode, hotelCode));
    }

    public List<MemberCheckInStatistics> listBlocStatistics(List<String> memberNos) {
        return memberCheckInStatisticsDao.selectList(new LambdaQueryWrapper<MemberCheckInStatistics>()
                .in(MemberCheckInStatistics::getMemberNo, memberNos)
                .eq(MemberCheckInStatistics::getHotelCode, String.valueOf(Constant.ZERO)));
    }


    public void save(MemberCheckInStatistics statisticsPo) {
        if (getHotelStatistics(statisticsPo.getMemberNo(), statisticsPo.getHotelCode()) == null) {
            memberCheckInStatisticsDao.insert(statisticsPo);
        } else {
            memberCheckInStatisticsDao.updateByPrimaryKey(statisticsPo);
        }
    }
}
