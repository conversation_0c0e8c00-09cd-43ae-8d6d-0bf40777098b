package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.pms.member.com.enums.OrderRefundStateEnum;
import com.ly.titc.pms.member.dal.dao.MemberOrderRefundInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/12/12 10:45
 */
@Slf4j
@Component
public class MemberOrderRefundInfoBiz {

    @Resource
    private MemberOrderRefundInfoDao memberOrderRefundInfoDao;

    public List<MemberOrderRefundInfo> listByMemberOrderNo(Integer masterType, String masterCode, String memberOrderNo) {
        LambdaQueryWrapper<MemberOrderRefundInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberOrderRefundInfo::getMasterType, masterType)
                .eq(MemberOrderRefundInfo::getMasterCode, masterCode)
                .eq(MemberOrderRefundInfo::getMemberOrderNo, memberOrderNo);
        return memberOrderRefundInfoDao.selectList(wrapper);
    }

    public List<MemberOrderRefundInfo> listRefundOrder(String memberOrderNo, String memberOrderPayNo) {
        LambdaQueryWrapper<MemberOrderRefundInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberOrderRefundInfo::getMemberOrderNo, memberOrderNo)
                .eq(MemberOrderRefundInfo::getMemberOrderPayNo, memberOrderPayNo);
        return memberOrderRefundInfoDao.selectList(wrapper);
    }

    public void add(MemberOrderRefundInfo entity) {
        memberOrderRefundInfoDao.insert(entity);
    }

    public void update(MemberOrderRefundInfo processRefundOrder) {
        memberOrderRefundInfoDao.updateByPrimaryKey(processRefundOrder);
    }

    public void updateRefundState(String memberRefundNo, Integer state, String failReason) {
        LambdaUpdateWrapper<MemberOrderRefundInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberOrderRefundInfo::getMemberRefundNo, memberRefundNo)
                .set(MemberOrderRefundInfo::getRefundState, state)
                .set(MemberOrderRefundInfo::getFailReason, Optional.ofNullable(failReason).orElse(""));
        memberOrderRefundInfoDao.update(null, wrapper);
    }

    public MemberOrderRefundInfo getByMemberRefundNo(String memberRefundNo) {
        LambdaQueryWrapper<MemberOrderRefundInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberOrderRefundInfo::getMemberRefundNo, memberRefundNo);
        return memberOrderRefundInfoDao.selectOne(wrapper);
    }

    public MemberOrderRefundInfo getByRefundPayNo(String refundPayNo) {
        LambdaQueryWrapper<MemberOrderRefundInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberOrderRefundInfo::getOnlineRefundPayNo, refundPayNo);
        return memberOrderRefundInfoDao.selectOne(wrapper);
    }
}
