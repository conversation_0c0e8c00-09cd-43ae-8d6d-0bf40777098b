package com.ly.titc.pms.member.biz;

import com.ly.titc.pms.member.dal.dao.BackDoorDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * 用于处理数据
 *
 * <AUTHOR>
 * @date 2024/5/7 13:46
 */
@Slf4j
@Repository
public class BackDoorBiz {

    @Resource
    private BackDoorDao backDoorDao;

    public void doUpdateSomething(String sql){
        backDoorDao.doUpdateSomething(sql);
    }

    public void doInsertSomething(String sql){
        backDoorDao.doInsertSomething(sql);
    }

    public void doDeleteSomething(String sql){
        backDoorDao.doDeleteSomething(sql);
    }


}
