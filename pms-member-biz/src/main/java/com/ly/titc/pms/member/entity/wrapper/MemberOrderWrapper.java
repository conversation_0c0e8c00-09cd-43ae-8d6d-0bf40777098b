package com.ly.titc.pms.member.entity.wrapper;

import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 15:25
 */
@Data
@Accessors(chain = true)
public class MemberOrderWrapper {

    /**
     * 会员订单主表
     */
    private MemberOrderInfo orderInfo;
    /**
     * 会员订单明细表
     */
    private MemberOrderDetailInfo detailInfo;
}
