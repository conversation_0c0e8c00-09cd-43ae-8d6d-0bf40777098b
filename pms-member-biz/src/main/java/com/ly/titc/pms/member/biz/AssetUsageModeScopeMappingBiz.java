package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.pms.member.dal.dao.AssetUsageModeScopeMappingDao;
import com.ly.titc.pms.member.dal.entity.po.AssetUsageModeScopeMapping;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 资产使用模式
 *
 * <AUTHOR>
 * @date 2025/6/25 11:44
 */
@Component
public class AssetUsageModeScopeMappingBiz {

    @Resource
    private AssetUsageModeScopeMappingDao assetUsageModeScopeMappingDao;

    public List<AssetUsageModeScopeMapping> listByRuleIds(List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AssetUsageModeScopeMapping> wrapper = Wrappers.lambdaQuery(AssetUsageModeScopeMapping.class).
                in(AssetUsageModeScopeMapping::getRuleId, ruleIds);
        return assetUsageModeScopeMappingDao.selectList(wrapper);
    }

    public void deleteByRuleId(Long ruleId) {
        LambdaUpdateWrapper<AssetUsageModeScopeMapping> wrapper = Wrappers.lambdaUpdate(AssetUsageModeScopeMapping.class)
                .set(AssetUsageModeScopeMapping::getIsDelete, StatusEnum.VALID.getStatus())
                .eq(AssetUsageModeScopeMapping::getRuleId, ruleId);
        assetUsageModeScopeMappingDao.update(null, wrapper);
    }

    public void insertBatch(List<AssetUsageModeScopeMapping> usageModeScopeMappings) {
        assetUsageModeScopeMappingDao.insertBatch(usageModeScopeMappings);
    }
}
