package com.ly.titc.pms.member.entity.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 会员等级保级规则信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Getter
@Setter
@Accessors(chain = true)
public class PageCardLevelRelegationRuleBo {

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    private Long cardId;

    private Integer sourceLevel;

    private List<Long> cardIdList;

    private Integer state;

    private String name;

    /**
     * 页码
     */
    private Integer pageIndex = 1;

    /**
     * pageSize
     */
    private Integer pageSize = 20;
}
