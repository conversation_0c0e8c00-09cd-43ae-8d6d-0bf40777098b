package com.ly.titc.pms.member.biz;

import com.ly.titc.pms.member.dal.dao.MemberOrderRechargeSceneInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRechargeSceneInfo;
import com.ly.titc.springboot.dcdb.dal.core.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 15:14
 */
@Slf4j
@Component
public class MemberOrderRechargeSceneInfoBiz extends AbstractBiz<MemberOrderRechargeSceneInfo>{

    @Resource
    private MemberOrderRechargeSceneInfoDao dao;

    public void add(MemberOrderRechargeSceneInfo info){
        dao.insert(info);
    }

    public MemberOrderRechargeSceneInfo getByOrderNo(String memberOrderNo) {
        LambdaQueryWrapperX<MemberOrderRechargeSceneInfo> queryWrapper = new LambdaQueryWrapperX<>();
        return dao.selectOne(queryWrapper.eq(MemberOrderRechargeSceneInfo::getMemberOrderNo, memberOrderNo));
    }

    public List<MemberOrderRechargeSceneInfo> listByOrderNos(List<String> memberOrderNos) {
        LambdaQueryWrapperX<MemberOrderRechargeSceneInfo> queryWrapper = new LambdaQueryWrapperX<>();
        return dao.selectList(queryWrapper.in(MemberOrderRechargeSceneInfo::getMemberOrderNo, memberOrderNos));
    }
}
