package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.dal.dao.CardNoRuleInfoDao;
import com.ly.titc.pms.member.dal.entity.po.CardConfigInfo;
import com.ly.titc.pms.member.dal.entity.po.CardNoRuleInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 卡号生成规则表
 *
 * <AUTHOR>
 * @date 2025/6/25 11:50
 */
@Component
public class CardNoRuleBiz {

    @Resource
    private CardNoRuleInfoDao cardNoRuleInfoDao;

    public void insert(CardNoRuleInfo info) {
        cardNoRuleInfoDao.insert(info);
    }

    public void update(CardNoRuleInfo cardNoRuleInfo) {
        LambdaUpdateWrapper<CardNoRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardNoRuleInfo::getCardId, cardNoRuleInfo.getCardId());
        wrapper.set(CardNoRuleInfo::getCardPrefix, cardNoRuleInfo.getCardPrefix())
                .set(CardNoRuleInfo::getCardLength, cardNoRuleInfo.getCardLength())
                .set(CardNoRuleInfo::getExcludeNumber, cardNoRuleInfo.getExcludeNumber())
                .set(CardNoRuleInfo::getModifyUser, cardNoRuleInfo.getModifyUser());
        cardNoRuleInfoDao.update(null, wrapper);
    }

    public void deleteByCardId(Long cardId, String operator) {
        LambdaUpdateWrapper<CardNoRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardNoRuleInfo::getCardId, cardId);
        wrapper.set(CardNoRuleInfo::getIsDelete, Constant.ONE)
                .set(CardNoRuleInfo::getModifyUser, operator);
        cardNoRuleInfoDao.update(null, wrapper);
    }

    public List<CardNoRuleInfo> listByCardIds(List<Long> cardIds) {
        LambdaUpdateWrapper<CardNoRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CardNoRuleInfo::getCardId, cardIds);
        return cardNoRuleInfoDao.selectList(wrapper);
    }

    public CardNoRuleInfo getByCardId(Long cardId) {
        LambdaUpdateWrapper<CardNoRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CardNoRuleInfo::getCardId, cardId);
        return cardNoRuleInfoDao.selectOne(wrapper);
    }
}
