package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dal.dao.CardLevelConfigInfoDao;
import com.ly.titc.pms.member.dal.entity.po.CardConfigInfo;
import com.ly.titc.pms.member.dal.entity.po.CardLevelConfigInfo;
import com.ly.titc.pms.member.entity.bo.ListCardLevelConfigBo;
import com.ly.titc.pms.member.entity.bo.PageCardLevelConfigBo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 卡等级配置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:45
 */
@Component
public class CardLevelConfigBiz {

    @Resource
    private CardLevelConfigInfoDao cardLevelConfigInfoDao;

    public List<CardLevelConfigInfo> listByCardIds(List<Long> cardIds) {
        LambdaQueryWrapper<CardLevelConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CardLevelConfigInfo::getCardId, cardIds);
        return cardLevelConfigInfoDao.selectList(wrapper);
    }

    public CardLevelConfigInfo getById(Long id) {
        LambdaQueryWrapper<CardLevelConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CardLevelConfigInfo::getId, id);
        return cardLevelConfigInfoDao.selectOne(wrapper);
    }

    public void insert(CardLevelConfigInfo cardLevelConfigInfo) {
        cardLevelConfigInfoDao.insert(cardLevelConfigInfo);
    }

    public void update(CardLevelConfigInfo cardLevelConfigInfo) {
        LambdaUpdateWrapper<CardLevelConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelConfigInfo::getId, cardLevelConfigInfo.getId());
        wrapper.set(CardLevelConfigInfo::getCardLevel, cardLevelConfigInfo.getCardLevel())
                .set(CardLevelConfigInfo::getCardLevelName, cardLevelConfigInfo.getCardLevelName())
                .set(CardLevelConfigInfo::getCardLevelDesc, cardLevelConfigInfo.getCardLevelDesc())
                .set(CardLevelConfigInfo::getValidPeriod, cardLevelConfigInfo.getValidPeriod())
                .set(CardLevelConfigInfo::getIsLongTerm, cardLevelConfigInfo.getIsLongTerm())
                .set(CardLevelConfigInfo::getCardPrice, cardLevelConfigInfo.getCardPrice())
                .set(CardLevelConfigInfo::getCardDiscount, cardLevelConfigInfo.getCardDiscount())
                .set(CardLevelConfigInfo::getRelegationType, cardLevelConfigInfo.getRelegationType())
                .set(CardLevelConfigInfo::getLevelImage, cardLevelConfigInfo.getLevelImage())
                .set(CardLevelConfigInfo::getState, cardLevelConfigInfo.getState())
                .set(CardLevelConfigInfo::getModifyUser, cardLevelConfigInfo.getModifyUser());
        cardLevelConfigInfoDao.update(null, wrapper);
    }

    public void deleteByCardId(Long cardId, String operator) {
        LambdaUpdateWrapper<CardLevelConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelConfigInfo::getCardId, cardId);
        wrapper.set(CardLevelConfigInfo::getIsDelete, Constant.ONE)
                .set(CardLevelConfigInfo::getModifyUser, operator);
        cardLevelConfigInfoDao.update(null, wrapper);
    }

    public void deleteByCardId(Long cardId, Integer cardLevel, String operator) {
        LambdaUpdateWrapper<CardLevelConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelConfigInfo::getCardId, cardId).eq(CardLevelConfigInfo::getCardLevel, cardLevel);
        wrapper.set(CardLevelConfigInfo::getIsDelete, Constant.ONE)
                .set(CardLevelConfigInfo::getModifyUser, operator);
        cardLevelConfigInfoDao.update(null, wrapper);
    }

    public CardLevelConfigInfo getByCardLevel(Long cardId, Integer cardLevel) {
        LambdaQueryWrapper<CardLevelConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CardLevelConfigInfo::getCardId, cardId)
                .eq(CardLevelConfigInfo::getCardLevel, cardLevel);
        return cardLevelConfigInfoDao.selectOne(wrapper);
    }

    public List<CardLevelConfigInfo> listCardLevelConfig(ListCardLevelConfigBo listBo) {
        LambdaQueryWrapper<CardLevelConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CardLevelConfigInfo::getMasterType, listBo.getMasterType())
                .eq(CardLevelConfigInfo::getMasterCode, listBo.getMasterCode())
                .eq(CardLevelConfigInfo::getCardLevelName, listBo.getName());
        return cardLevelConfigInfoDao.selectList(wrapper);
    }

    public IPage<CardLevelConfigInfo> pageCardLevelConfig(PageCardLevelConfigBo pageBo) {
        return cardLevelConfigInfoDao.selectPage(new Page<>(pageBo.getPageIndex(), pageBo.getPageSize()),
                new LambdaQueryWrapper<CardLevelConfigInfo>()
                        .eq(CardLevelConfigInfo::getCardId, pageBo.getCardId())
                        .eq(CardLevelConfigInfo::getCardLevel, pageBo.getLevel())
                        .eq(CardLevelConfigInfo::getState, pageBo.getState()));
    }

    public List<CardLevelConfigInfo> listByCardId(Long cardId) {
        LambdaQueryWrapper<CardLevelConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CardLevelConfigInfo::getCardId, cardId);
        return cardLevelConfigInfoDao.selectList(wrapper);
    }
}
