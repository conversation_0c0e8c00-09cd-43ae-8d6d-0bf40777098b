package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.pms.member.dal.dao.MemberContactInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberContactInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5 14:08
 */
@Slf4j
@Component
public class MemberContactInfoBiz extends AbstractBiz<MemberContactInfo> {

    @Resource(type = MemberContactInfoDao.class)
    private MemberContactInfoDao memberContactInfoDao;

    /**
     * 新增
     *
     * @param entity
     */
    public void add(MemberContactInfo entity) {
        memberContactInfoDao.insert(entity);
    }

    public MemberContactInfo getByMemberNo(String memberNo) {
        return memberContactInfoDao.selectOne(new LambdaQueryWrapper<MemberContactInfo>().eq(MemberContactInfo::getMemberNo, memberNo));
    }

    public List<MemberContactInfo> listByMemberNo(List<String> memberNos) {
        return memberContactInfoDao.selectList(new LambdaQueryWrapper<MemberContactInfo>().in(MemberContactInfo::getMemberNo, memberNos));
    }

    public void update(MemberContactInfo memberContactInfo) {
        LambdaUpdateWrapper<MemberContactInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberContactInfo::getMemberNo, memberContactInfo.getMemberNo())
                .set(MemberContactInfo::getEmail, memberContactInfo.getEmail())
                .set(MemberContactInfo::getQq, memberContactInfo.getQq())
                .set(MemberContactInfo::getWechat, memberContactInfo.getWechat())
                .set(MemberContactInfo::getCountryId, memberContactInfo.getCountryId())
                .set(MemberContactInfo::getCountry, memberContactInfo.getCountry())
                .set(MemberContactInfo::getProvinceId, memberContactInfo.getProvinceId())
                .set(MemberContactInfo::getProvince, memberContactInfo.getProvince())
                .set(MemberContactInfo::getCityId, memberContactInfo.getCityId())
                .set(MemberContactInfo::getCity, memberContactInfo.getCity())
                .set(MemberContactInfo::getDistrictId, memberContactInfo.getDistrictId())
                .set(MemberContactInfo::getDistrict, memberContactInfo.getDistrict())
                .set(MemberContactInfo::getAddress, memberContactInfo.getAddress())
                .set(MemberContactInfo::getPostalCode, memberContactInfo.getPostalCode());
        memberContactInfoDao.update(null, wrapper);
    }
}
