package com.ly.titc.pms.member.entity.wrapper;

import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberContactInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberExtendInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 会员信息保存
 *
 * <AUTHOR>
 * @date 2024/11/5 09:48
 */
@Data
@Accessors(chain = true)
public class MemberSaveWrapper {

    /**
     * 会员基础信息
     */
    private MemberInfo memberInfo;

    /**
     * 会员拓展信息
     */
    private MemberExtendInfo memberExtendInfo;

    /**
     * 会员联系信息
     */
    private MemberContactInfo memberContactInfo;

    /**
     * 会员卡信息
     */
    private MemberCardInfo memberCardInfo;


}
