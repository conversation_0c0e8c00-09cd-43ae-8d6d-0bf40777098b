package com.ly.titc.pms.member.biz;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.common.enums.DeletedStatusEnum;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.dao.MemberProfileAddressInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileAddressInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5 19:41
 */
@Slf4j
@Component
public class MemberProfileAddressInfoBiz extends AbstractBiz<MemberProfileAddressInfo>{

    @Resource(type = MemberProfileAddressInfoDao.class)
    private MemberProfileAddressInfoDao memberProfileAddressInfoDao;

    public void add(MemberProfileAddressInfo entity){
        entity.setAddressNo(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
        memberProfileAddressInfoDao.insert(entity);
    }

    public void update(MemberProfileAddressInfo info) {
        LambdaUpdateWrapper<MemberProfileAddressInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberProfileAddressInfo::getAddressNo, info.getAddressNo())
                .eq(MemberProfileAddressInfo::getMemberNo, info.getMemberNo())
                .set(MemberProfileAddressInfo::getName, info.getName())
                .set(MemberProfileAddressInfo::getMobile, info.getMobile())
                .set(MemberProfileAddressInfo::getCountryId, info.getCountryId())
                .set(MemberProfileAddressInfo::getCountry, info.getCountry())
                .set(MemberProfileAddressInfo::getProvinceId, info.getProvinceId())
                .set(MemberProfileAddressInfo::getProvince, info.getProvince())
                .set(MemberProfileAddressInfo::getCityId, info.getCityId())
                .set(MemberProfileAddressInfo::getCity, info.getCity())
                .set(MemberProfileAddressInfo::getDistrictId, info.getDistrictId())
                .set(MemberProfileAddressInfo::getDistrict, info.getDistrict())
                .set(MemberProfileAddressInfo::getAddress, info.getAddress())
                .set(MemberProfileAddressInfo::getPostalCode, info.getPostalCode())
                .set(MemberProfileAddressInfo::getSort, info.getSort())
                .set(MemberProfileAddressInfo::getTag, info.getTag())
                .set(MemberProfileAddressInfo::getModifyUser, info.getModifyUser())
                .set(MemberProfileAddressInfo::getGmtModified, LocalDateTime.now());
        memberProfileAddressInfoDao.update(null, wrapper);
    }

    public void delete(String memberNo, Long addressNo, String operator) {
        LambdaUpdateWrapper<MemberProfileAddressInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberProfileAddressInfo::getAddressNo, addressNo)
                .eq(MemberProfileAddressInfo::getMemberNo, memberNo);
        wrapper.set(MemberProfileAddressInfo::getIsDelete, DeletedStatusEnum.INVALID.getStatus())
                .set(MemberProfileAddressInfo::getModifyUser, operator);
        memberProfileAddressInfoDao.update(null, wrapper);
    }

    public List<MemberProfileAddressInfo> listByMemberNo(String memberNo, String name) {
        LambdaQueryWrapper<MemberProfileAddressInfo> wrapper = new LambdaQueryWrapper<MemberProfileAddressInfo>()
                .eq(MemberProfileAddressInfo::getMemberNo, memberNo)
                .like(StringUtils.isNotBlank(name), MemberProfileAddressInfo::getName, name)
                .orderByDesc(MemberProfileAddressInfo::getSort)
                .orderByDesc(MemberProfileAddressInfo::getGmtCreate);
        return memberProfileAddressInfoDao.selectList(wrapper);
    }

    public List<MemberProfileAddressInfo> listByMemberNoAndName(String memberNo, String name) {
        LambdaQueryWrapper<MemberProfileAddressInfo> wrapper = new LambdaQueryWrapper<MemberProfileAddressInfo>()
                .eq(MemberProfileAddressInfo::getMemberNo, memberNo)
                .like(StringUtils.isNotEmpty(name), MemberProfileAddressInfo::getName, name)
                .orderByDesc(MemberProfileAddressInfo::getSort)
                .orderByDesc(MemberProfileAddressInfo::getGmtCreate);
        return memberProfileAddressInfoDao.selectList(wrapper);
    }

    public MemberProfileAddressInfo getByNo(String memberNo, Long addressNo) {
        LambdaQueryWrapper<MemberProfileAddressInfo> wrapper = new LambdaQueryWrapper<MemberProfileAddressInfo>()
                .eq(MemberProfileAddressInfo::getAddressNo, addressNo)
                .eq(MemberProfileAddressInfo::getMemberNo, memberNo);
        return memberProfileAddressInfoDao.selectOne(wrapper);
    }
}
