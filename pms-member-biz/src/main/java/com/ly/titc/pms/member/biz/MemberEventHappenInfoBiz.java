package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ly.titc.pms.member.dal.dao.MemberEventHappenInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberEventHappenInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberEventHappenInfoBiz
 * @Date：2024-12-15 0:26
 * @Filename：MemberEventHappenInfoBiz
 */
@Slf4j
@Component
public class MemberEventHappenInfoBiz extends AbstractBiz<MemberEventHappenInfo> {


    @Resource
    private MemberEventHappenInfoDao memberEventHappenInfoDao;

    public List<MemberEventHappenInfo> listInYesterday(Integer masterType, String masterCode) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate yesterdayDate = now.toLocalDate().minusDays(1);
        // 设置昨天的开始时间为 00:00:00
        LocalDateTime startOfYesterday = yesterdayDate.atStartOfDay();
        // 设置昨天的结束时间为 23:59:59
        LocalDateTime endOfYesterday = yesterdayDate.atTime(LocalTime.MAX);
        return memberEventHappenInfoDao.selectList(Wrappers.lambdaQuery(MemberEventHappenInfo.class)
                .between(MemberEventHappenInfo::getGmtCreate, startOfYesterday, endOfYesterday)
                .eq(MemberEventHappenInfo::getMasterType, masterType)
                .eq(MemberEventHappenInfo::getMasterCode, masterCode));
    }

}
