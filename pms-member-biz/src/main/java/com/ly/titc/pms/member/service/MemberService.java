package com.ly.titc.pms.member.service;

import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.pms.member.biz.*;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.GeneratorMemberNoUtil;
import com.ly.titc.pms.member.com.utils.PasswordEncryptUtil;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.entity.wrapper.MemberIssueCardWrapper;
import com.ly.titc.pms.member.entity.wrapper.MemberRegisterWrapper;
import com.ly.titc.pms.member.entity.wrapper.MemberSaveWrapper;
import com.ly.titc.springboot.dcdb.dal.core.encrypt.AESEncryptService;
import com.ly.titc.springboot.dcdb.dal.core.encrypt.EncryptService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 会员服务实现
 *
 * <AUTHOR>
 * @date 2024/11/5 09:50
 */
@Service
public class MemberService {

    @Resource
    private MemberInfoBiz memberInfoBiz;
    @Resource
    private MemberExtendInfoBiz memberExtendInfoBiz;
    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;
    @Resource
    private MemberContactInfoBiz memberContactInfoBiz;
    @Resource
    private RedisFactory redisFactory;
    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;

    /**
     * 注册会员
     *
     * @param wrapper
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void register(MemberRegisterWrapper wrapper){
        MemberInfo memberInfo = wrapper.getMemberInfo();
        MemberExtendInfo memberExtendInfo = wrapper.getMemberExtendInfo();
        MemberContactInfo memberContactInfo = wrapper.getMemberContactInfo();
        // 生成密码
        memberInfo.setPassword(PasswordEncryptUtil.encrypt(memberInfo.getMobile()));
        memberInfoBiz.add(memberInfo);
        memberExtendInfoBiz.add(memberExtendInfo);
        memberContactInfoBiz.add(memberContactInfo);
    }

    /**
     * 发放会员卡
     *
     * @param wrapper
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String issueCard(MemberIssueCardWrapper wrapper){
        MemberCardInfo memberCardInfo = wrapper.getMemberCardInfo();
        MemberCardLevelChangeRecord memberCardLevelChangeRecord = wrapper.getMemberCardLevelChangeRecord();

        memberCardInfoBiz.add(memberCardInfo);
        memberCardLevelChangeRecordBiz.add(memberCardLevelChangeRecord);
        return memberCardInfo.getMemberCardNo();
    }

    /**
     * 更新会员信息
     *
     * @param wrapper
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMember(MemberSaveWrapper wrapper) {
        MemberInfo memberInfo = wrapper.getMemberInfo();
        MemberExtendInfo memberExtendInfo = wrapper.getMemberExtendInfo();
        MemberContactInfo memberContactInfo = wrapper.getMemberContactInfo();

        memberInfoBiz.update(memberInfo);
        memberExtendInfoBiz.update(memberExtendInfo);
        memberContactInfoBiz.update(memberContactInfo);
    }

    /**
     * 填充会员号和卡号
     *
     * @param memberInfo
     * @param memberExtendInfo
     */
    private void fillMemberNoAndCardNo(MemberInfo memberInfo, MemberExtendInfo memberExtendInfo, MemberContactInfo memberContactInfo) {
        String memberNo = getMemberNo(memberInfo.getMasterType(), memberInfo.getMasterCode());
        memberInfo.setMemberNo(memberNo);
        memberExtendInfo.setMemberNo(memberNo);
        memberContactInfo.setMemberNo(memberNo);
    }

    /**
     * 获取会员号
     *
     * @return
     */
    public String getMemberNo(Integer masterType, String masterCode) {

        String memberNo = GeneratorMemberNoUtil.generatorMemberNo();
        //redis 缓存一天数据，去重
        Boolean result = redisFactory.setNx(memberNo, Constant.SECONDS_BY_ONE_DAY, memberNo);
        if (!result) {
            return getMemberNo(masterType, masterCode);
        }
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(masterType, masterCode, memberNo);
        if (!Objects.isNull(memberInfo)) {
            return getMemberNo(masterType, masterCode);
        }
        return memberNo;
    }

}
