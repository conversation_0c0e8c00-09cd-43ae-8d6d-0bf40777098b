package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.pms.member.dal.dao.MemberBlacklistInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberBlacklistInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：rui
 * @name：CustomerBlacklistInfoBiz
 * @Date：2024-12-10 15:33
 * @Filename：CustomerBlacklistInfoBiz
 */
@Slf4j
@Component
public class MemberBlacklistInfoBiz extends AbstractBiz<MemberBlacklistInfo> {

    @Resource
    private MemberBlacklistInfoDao memberBlacklistInfoDao;

    public String insert(MemberBlacklistInfo customerBlacklistInfo) {
        memberBlacklistInfoDao.insert(customerBlacklistInfo);
        return customerBlacklistInfo.getBlacklistNo();
    }

    public void update(MemberBlacklistInfo customerBlacklistInfo) {
        memberBlacklistInfoDao.update(customerBlacklistInfo, new LambdaUpdateWrapper<>(MemberBlacklistInfo.class)
                .eq(MemberBlacklistInfo::getBlacklistNo, customerBlacklistInfo.getBlacklistNo()));
    }

    public void unBlacklisted(String blacklistNo, String operator) {
        memberBlacklistInfoDao.update(null, new LambdaUpdateWrapper<MemberBlacklistInfo>()
                .eq(MemberBlacklistInfo::getBlacklistNo, blacklistNo)
                .set(MemberBlacklistInfo::getState, 0)
                .set(MemberBlacklistInfo::getModifyUser, operator));
    }

    public List<MemberBlacklistInfo> listByMemberNo(String memberNo, Integer state) {
        return memberBlacklistInfoDao.selectList(new LambdaUpdateWrapper<>(MemberBlacklistInfo.class)
                .eq(MemberBlacklistInfo::getMemberNo, memberNo)
                .eq(MemberBlacklistInfo::getState, state)
                .orderByDesc(MemberBlacklistInfo::getGmtCreate));
    }

    public MemberBlacklistInfo getByBlacklistNo(String memberNo, String blacklistNo) {
        return memberBlacklistInfoDao.selectOne(new LambdaUpdateWrapper<>(MemberBlacklistInfo.class)
                .eq(MemberBlacklistInfo::getMemberNo, memberNo)
                .eq(MemberBlacklistInfo::getBlacklistNo, blacklistNo));
    }

    public void updateState(String memberNo, String blacklistNo, Integer state, String operator) {
        LambdaUpdateWrapper<MemberBlacklistInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberBlacklistInfo::getMemberNo, memberNo)
                .eq(MemberBlacklistInfo::getBlacklistNo, blacklistNo);
        wrapper.set(MemberBlacklistInfo::getState, state)
                .set(MemberBlacklistInfo::getModifyUser, operator);
        memberBlacklistInfoDao.update(null, wrapper);
    }

    public List<MemberBlacklistInfo> listBlacklist(String memberNo, Integer state) {
        return memberBlacklistInfoDao.selectList(new LambdaUpdateWrapper<>(MemberBlacklistInfo.class)
                .eq(MemberBlacklistInfo::getMemberNo, memberNo)
                .eq(MemberBlacklistInfo::getState, state)
                .orderByDesc(MemberBlacklistInfo::getGmtCreate));
    }

    public List<MemberBlacklistInfo> listBlacklist(List<String> memberNos, Integer state) {
        return memberBlacklistInfoDao.selectList(new LambdaUpdateWrapper<>(MemberBlacklistInfo.class)
                .in(MemberBlacklistInfo::getMemberNo, memberNos)
                .eq(MemberBlacklistInfo::getState, state)
                .orderByDesc(MemberBlacklistInfo::getGmtCreate));
    }
}
