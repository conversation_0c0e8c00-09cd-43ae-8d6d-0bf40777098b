package com.ly.titc.pms.member.entity.bo;

import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：PageMemberTagConfigInfoReq
 * @Date：2024-11-8 11:37
 * @Filename：PageMemberTagConfigInfoReq
 */
@Data
public class PageMemberTagConfigBo {

    private Integer masterType;

    private String masterCode;

    private Integer type;

    private List<Integer> typeList;

    private String name;

    private Integer markType;

    private List<Long> tagIdList;

    private Integer autoDelete;
    /**
     * 页码
     */
    private Integer pageIndex = 1;

    /**
     * pageSize
     */
    private Integer pageSize = 20;

}
