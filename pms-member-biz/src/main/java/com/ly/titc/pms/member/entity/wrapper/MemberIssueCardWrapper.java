package com.ly.titc.pms.member.entity.wrapper;

import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import lombok.Data;

/**
 * 会员发放会员卡
 *
 * <AUTHOR>
 * @date 2024/12/18 13:37
 */
@Data
public class MemberIssueCardWrapper {

    /**
     * 会员卡
     */
    private MemberCardInfo memberCardInfo;

    /**
     * 会员卡变更记录
     */
    private MemberCardLevelChangeRecord memberCardLevelChangeRecord;
}
