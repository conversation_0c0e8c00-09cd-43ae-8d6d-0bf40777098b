package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.pms.member.dal.dao.MemberCardNoGenerateRecordDao;
import com.ly.titc.pms.member.dal.entity.po.MemberCardNoGenerateRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 会员卡生成记录biz
 *
 * <AUTHOR>
 * @date 2024/11/8 11:00
 */
@Slf4j
@Component
public class MemberCardNoGenerateRecordBiz extends AbstractBiz<MemberCardNoGenerateRecord>{

    @Resource
    private MemberCardNoGenerateRecordDao memberCardNoGenerateRecordDao;

    public void add(MemberCardNoGenerateRecord record){
        memberCardNoGenerateRecordDao.insert(record);
    }

    public int update(Integer masterType, String masterCode, Long cardId, Long oldEndNum, Long newEndNum){
        LambdaUpdateWrapper<MemberCardNoGenerateRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberCardNoGenerateRecord::getMasterType, masterType)
                .eq(MemberCardNoGenerateRecord::getMasterCode, masterCode)
                .eq(MemberCardNoGenerateRecord::getCardId, cardId)
                .eq(MemberCardNoGenerateRecord::getEndNum, oldEndNum);

        wrapper.set(MemberCardNoGenerateRecord::getEndNum, newEndNum);
        return memberCardNoGenerateRecordDao.update(null, wrapper);
    }

    public MemberCardNoGenerateRecord getMemberCardNoGenerateRecord(Integer masterType, String masterCode, Long cardId) {
        LambdaUpdateWrapper<MemberCardNoGenerateRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberCardNoGenerateRecord::getMasterType, masterType)
                .eq(MemberCardNoGenerateRecord::getMasterCode, masterCode)
                .eq(MemberCardNoGenerateRecord::getCardId, cardId);
        return memberCardNoGenerateRecordDao.selectOne(wrapper);
    }
}
