package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.pms.member.dal.dao.StoreConfigInfoDao;
import com.ly.titc.pms.member.dal.entity.po.StoreConfigInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 储值设置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:53
 */
@Component
public class StoreConfigBiz {

    @Resource
    private StoreConfigInfoDao storeConfigInfoDao;

    public StoreConfigInfo getByMaster(Integer masterType, String masterCode) {
        LambdaQueryWrapper<StoreConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreConfigInfo::getMasterType, masterType)
                .eq(StoreConfigInfo::getMasterCode, masterCode);
        return storeConfigInfoDao.selectOne(wrapper);
    }

    public void updateById(StoreConfigInfo config) {
        storeConfigInfoDao.updateById(config);
    }

    public void updateByBloc(String blocCode, Integer masterType, Integer isPayVerifyRequired, Integer isSupportOtherCard, String operator) {
        LambdaUpdateWrapper<StoreConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StoreConfigInfo::getMasterType, masterType)
                .eq(StoreConfigInfo::getBlocCode, blocCode)
                .set(StoreConfigInfo::getIsPayVerifyRequired, isPayVerifyRequired)
                .set(StoreConfigInfo::getIsSupportOtherCard, isSupportOtherCard)
                .set(StoreConfigInfo::getModifyUser, operator);
        storeConfigInfoDao.update(null, wrapper);
    }

    public void insert(StoreConfigInfo config) {
        storeConfigInfoDao.insert(config);
    }
}
