package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.dal.dao.CardLevelUpgradeRuleInfoDao;
import com.ly.titc.pms.member.dal.dao.CardLevelUpgradeRuleDetailDao;
import com.ly.titc.pms.member.dal.entity.po.CardLevelRelegationRuleInfo;
import com.ly.titc.pms.member.dal.entity.po.CardLevelUpgradeRuleDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.CardLevelUpgradeRuleInfo;
import com.ly.titc.pms.member.dal.entity.po.CardNoRuleInfo;
import com.ly.titc.pms.member.entity.bo.PageCardLevelUpgradeRuleBo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 卡升级规则配置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:47
 */
@Component
public class CardLevelUpgradeRuleBiz {

    @Resource
    private CardLevelUpgradeRuleInfoDao cardLevelUpgradeRuleInfoDao;

    @Resource
    private CardLevelUpgradeRuleDetailDao cardLevelUpgradeRuleDetailDao;

    public List<CardLevelUpgradeRuleInfo> listByCardId(Long cardId) {
        LambdaQueryWrapper<CardLevelUpgradeRuleInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CardLevelUpgradeRuleInfo::getCardId, cardId);
        return cardLevelUpgradeRuleInfoDao.selectList(wrapper);
    }

    public CardLevelUpgradeRuleInfo getById(Long id) {
        return cardLevelUpgradeRuleInfoDao.selectById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insert(CardLevelUpgradeRuleInfo info, List<CardLevelUpgradeRuleDetailInfo> upgradeRuleDetails) {
        cardLevelUpgradeRuleInfoDao.insert(info);
        // 全删全插
        LambdaUpdateWrapper<CardLevelUpgradeRuleDetailInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelUpgradeRuleDetailInfo::getUpgradeRuleId, info.getId());
        cardLevelUpgradeRuleDetailDao.delete(wrapper);
        cardLevelUpgradeRuleDetailDao.insertBatch(upgradeRuleDetails);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(CardLevelUpgradeRuleInfo info, List<CardLevelUpgradeRuleDetailInfo> upgradeRuleDetails) {
        LambdaUpdateWrapper<CardLevelUpgradeRuleInfo> wrapper = new LambdaUpdateWrapper<CardLevelUpgradeRuleInfo>()
                .eq(CardLevelUpgradeRuleInfo::getId, info.getId())
                .set(CardLevelUpgradeRuleInfo::getName, info.getName())
                .set(CardLevelUpgradeRuleInfo::getSourceLevel, info.getSourceLevel())
                .set(CardLevelUpgradeRuleInfo::getTargetLevel, info.getTargetLevel())
                .set(CardLevelUpgradeRuleInfo::getUpgradeType, info.getUpgradeType())
                .set(CardLevelUpgradeRuleInfo::getUpgradeSuccessfulPerformType, info.getUpgradeSuccessfulPerformType())
                .set(CardLevelUpgradeRuleInfo::getCycleType, info.getCycleType())
                .set(CardLevelUpgradeRuleInfo::getDescription, info.getDescription())
                .set(CardLevelUpgradeRuleInfo::getState, info.getState())
                .set(CardLevelUpgradeRuleInfo::getSort, info.getSort());
        cardLevelUpgradeRuleInfoDao.update(null, wrapper);
        // 全删全插
        cardLevelUpgradeRuleDetailDao.delete(new LambdaUpdateWrapper<CardLevelUpgradeRuleDetailInfo>()
                .eq(CardLevelUpgradeRuleDetailInfo::getUpgradeRuleId, info.getId()));
        cardLevelUpgradeRuleDetailDao.insertBatch(upgradeRuleDetails);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByCardId(Long cardId, String operator) {
        LambdaUpdateWrapper<CardLevelUpgradeRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelUpgradeRuleInfo::getCardId, cardId);
        wrapper.set(CardLevelUpgradeRuleInfo::getIsDelete, Constant.ONE)
                .set(CardLevelUpgradeRuleInfo::getModifyUser, operator);
        cardLevelUpgradeRuleInfoDao.update(null, wrapper);

        LambdaUpdateWrapper<CardLevelUpgradeRuleDetailInfo> detailWrapper = new LambdaUpdateWrapper<>();
        detailWrapper.eq(CardLevelUpgradeRuleDetailInfo::getCardId, cardId);
        detailWrapper.set(CardLevelUpgradeRuleDetailInfo::getIsDelete, Constant.ONE)
                .set(CardLevelUpgradeRuleDetailInfo::getModifyUser, operator);
        cardLevelUpgradeRuleDetailDao.update(null, detailWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByCardId(Long cardId, Integer cardLevel, String operator) {
        LambdaQueryWrapper<CardLevelUpgradeRuleInfo> ruleWrapper = new LambdaQueryWrapper<>();
        ruleWrapper.eq(CardLevelUpgradeRuleInfo::getCardId, cardId)
                .and(w -> w.eq(CardLevelUpgradeRuleInfo::getSourceLevel, cardLevel).or().eq(CardLevelUpgradeRuleInfo::getTargetLevel, cardLevel));
        List<CardLevelUpgradeRuleInfo> ruleInfos = cardLevelUpgradeRuleInfoDao.selectList(ruleWrapper);
        if (CollectionUtils.isNotEmpty(ruleInfos)) {
            List<Long> ruleIds = ruleInfos.stream().map(CardLevelUpgradeRuleInfo::getId).collect(Collectors.toList());
            deleteByRuleId(ruleIds, operator);
        }
    }

    public void deleteByRuleId(List<Long> ruleIds, String operator) {
        LambdaUpdateWrapper<CardLevelUpgradeRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CardLevelUpgradeRuleInfo::getId, ruleIds);
        wrapper.set(CardLevelUpgradeRuleInfo::getIsDelete, Constant.ONE)
                .set(CardLevelUpgradeRuleInfo::getModifyUser, operator);
        cardLevelUpgradeRuleInfoDao.update(null, wrapper);

        LambdaUpdateWrapper<CardLevelUpgradeRuleDetailInfo> detailWrapper = new LambdaUpdateWrapper<>();
        detailWrapper.in(CardLevelUpgradeRuleDetailInfo::getUpgradeRuleId, ruleIds);
        detailWrapper.set(CardLevelUpgradeRuleDetailInfo::getIsDelete, Constant.ONE)
                .set(CardLevelUpgradeRuleDetailInfo::getModifyUser, operator);
        cardLevelUpgradeRuleDetailDao.update(null, detailWrapper);
    }

    public void updateState(Long ruleId, Integer state, String operator) {
        LambdaUpdateWrapper<CardLevelUpgradeRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelUpgradeRuleInfo::getId, ruleId);
        wrapper.set(CardLevelUpgradeRuleInfo::getState, state)
                .set(CardLevelUpgradeRuleInfo::getModifyUser, operator);
        cardLevelUpgradeRuleInfoDao.update(null, wrapper);
    }

    public CardLevelUpgradeRuleInfo getByCardLevel(Long cardId, Integer memberCardLevel) {
        LambdaQueryWrapper<CardLevelUpgradeRuleInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CardLevelUpgradeRuleInfo::getCardId, cardId)
                .eq(CardLevelUpgradeRuleInfo::getSourceLevel, memberCardLevel);
        return cardLevelUpgradeRuleInfoDao.selectOne(wrapper);
    }

    public List<CardLevelUpgradeRuleDetailInfo> listDetailByRuleId(Long ruleId) {
        LambdaQueryWrapper<CardLevelUpgradeRuleDetailInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CardLevelUpgradeRuleDetailInfo::getUpgradeRuleId, ruleId);
        return cardLevelUpgradeRuleDetailDao.selectList(wrapper);
    }

    public List<CardLevelUpgradeRuleInfo> listByCardIds(List<Long> cardIds, Integer state) {
        LambdaQueryWrapper<CardLevelUpgradeRuleInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CardLevelUpgradeRuleInfo::getCardId, cardIds)
                .eq(CardLevelUpgradeRuleInfo::getState, state);
        return cardLevelUpgradeRuleInfoDao.selectList(wrapper);
    }

    public List<CardLevelUpgradeRuleDetailInfo> listDetailByRuleIds(List<Long> ruleIds) {
        LambdaQueryWrapper<CardLevelUpgradeRuleDetailInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CardLevelUpgradeRuleDetailInfo::getUpgradeRuleId, ruleIds);
        return cardLevelUpgradeRuleDetailDao.selectList(wrapper);
    }

    public IPage<CardLevelUpgradeRuleInfo> pageUpgradeRule(PageCardLevelUpgradeRuleBo pageBo) {
        QueryWrapper<CardLevelUpgradeRuleInfo> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(pageBo.getCardId() != null, CardLevelUpgradeRuleInfo::getCardId, pageBo.getCardId())
                .eq(pageBo.getSourceLevel() != null, CardLevelUpgradeRuleInfo::getSourceLevel, pageBo.getSourceLevel())
                .eq(pageBo.getState() != null, CardLevelUpgradeRuleInfo::getState, pageBo.getState())
                .like(StringUtils.isNotEmpty(pageBo.getName()), CardLevelUpgradeRuleInfo::getName, pageBo.getName())
                .eq(StringUtils.isNotEmpty(pageBo.getMasterCode()), CardLevelUpgradeRuleInfo::getMasterCode, pageBo.getMasterCode())
                .eq(pageBo.getMasterType() != null, CardLevelUpgradeRuleInfo::getMasterType, pageBo.getMasterType())
                .orderByDesc(CardLevelUpgradeRuleInfo::getSort)
                .in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(pageBo.getCardIdList()), CardLevelUpgradeRuleInfo::getCardId, pageBo.getCardIdList())
                .orderByDesc(CardLevelUpgradeRuleInfo::getId);
        return cardLevelUpgradeRuleInfoDao.selectPage(new Page<>(pageBo.getPageIndex(), pageBo.getPageSize()), wrapper);
    }
}
