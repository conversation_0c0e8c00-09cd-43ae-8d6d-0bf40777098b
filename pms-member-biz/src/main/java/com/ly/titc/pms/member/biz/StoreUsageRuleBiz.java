package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.dal.dao.StoreUsageRuleInfoDao;
import com.ly.titc.pms.member.dal.entity.po.PointUsageRuleInfo;
import com.ly.titc.pms.member.dal.entity.po.StoreUsageRuleInfo;
import com.ly.titc.pms.member.entity.bo.PageUsageRuleScopeMappingBo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 储值使用规则
 *
 * <AUTHOR>
 * @date 2025/6/25 11:53
 */
@Component
public class StoreUsageRuleBiz {

    @Resource
    private StoreUsageRuleInfoDao storeUsageRuleInfoDao;

    public List<StoreUsageRuleInfo> listByIds(List<Long> ruleIds) {
        return storeUsageRuleInfoDao.selectList(new LambdaQueryWrapper<StoreUsageRuleInfo>().in(StoreUsageRuleInfo::getId, ruleIds));
    }

    public Page<StoreUsageRuleInfo> pageStoreRule(PageUsageRuleScopeMappingBo pageBo) {
        return storeUsageRuleInfoDao.selectPage(new Page<>(pageBo.getPageIndex(), pageBo.getPageSize()), Wrappers.lambdaQuery(StoreUsageRuleInfo.class)
                .eq(StoreUsageRuleInfo::getMasterType, pageBo.getMasterType())
                .eq(StoreUsageRuleInfo::getMasterCode, pageBo.getMasterCode())
                .in(CollectionUtils.isNotEmpty(pageBo.getRuleIds()), StoreUsageRuleInfo::getId, pageBo.getRuleIds())
                .like(StringUtils.isNotEmpty(pageBo.getRuleName()), StoreUsageRuleInfo::getRuleName, pageBo.getRuleName())
                .eq(pageBo.getState() != null, StoreUsageRuleInfo::getState, pageBo.getState()));
    }

    public StoreUsageRuleInfo getById(Long ruleId) {
        return storeUsageRuleInfoDao.selectById(ruleId);
    }

    public void updateById(StoreUsageRuleInfo ruleInfo) {
        storeUsageRuleInfoDao.updateByPrimaryKey(ruleInfo);
    }

    public void insert(StoreUsageRuleInfo ruleInfo) {
        storeUsageRuleInfoDao.insert(ruleInfo);
    }

    public void updateState(Long ruleId, Integer state, String operator) {
        Wrapper<StoreUsageRuleInfo> wrapper = new UpdateWrapper<StoreUsageRuleInfo>().lambda()
                .eq(StoreUsageRuleInfo::getId, ruleId)
                .set(StoreUsageRuleInfo::getState, state)
                .set(StoreUsageRuleInfo::getModifyUser, operator);
        storeUsageRuleInfoDao.update(null, wrapper);
    }

    public void deleteById(Long ruleId, String operator) {
        Wrapper<StoreUsageRuleInfo> wrapper = new UpdateWrapper<StoreUsageRuleInfo>().lambda()
                .eq(StoreUsageRuleInfo::getId, ruleId)
                .set(StoreUsageRuleInfo::getIsDelete, Constant.ONE)
                .set(StoreUsageRuleInfo::getModifyUser, operator);
        storeUsageRuleInfoDao.update(null, wrapper);
    }
}
