package com.ly.titc.pms.member.entity.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberTagConfigInfoQuery
 * @Date：2024-11-8 10:57
 * @Filename：MemberTagConfigInfoQuery
 */
@Getter
@Setter
@Accessors(chain = true)
public class ListMemberTagConfigBo {

    private Integer masterType;

    private String masterCode;

    private Integer type;

    private List<Integer> typeList;

    private String name;

    private Integer markType;

    private List<Long> tagIdList;

    private Integer autoDelete;
}
