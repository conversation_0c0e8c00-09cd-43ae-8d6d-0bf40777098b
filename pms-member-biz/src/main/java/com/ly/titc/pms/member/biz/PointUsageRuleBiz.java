package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.dal.dao.PointUsageRuleInfoDao;
import com.ly.titc.pms.member.dal.entity.po.PointUsageRuleInfo;
import com.ly.titc.pms.member.entity.bo.PageUsageRuleScopeMappingBo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 积分使用规则
 *
 * <AUTHOR>
 * @date 2025/6/25 11:52
 */
@Component
public class PointUsageRuleBiz {

    @Resource
    private PointUsageRuleInfoDao pointUsageRuleInfoDao;

    public List<PointUsageRuleInfo> listByIds(List<Long> ruleIds) {
        LambdaQueryWrapper<PointUsageRuleInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PointUsageRuleInfo::getId, ruleIds);
        return pointUsageRuleInfoDao.selectList(wrapper);
    }

    public Page<PointUsageRuleInfo> pagePointRule(PageUsageRuleScopeMappingBo pageBo) {
        return pointUsageRuleInfoDao.selectPage(new Page<>(pageBo.getPageIndex(), pageBo.getPageSize()), Wrappers.lambdaQuery(PointUsageRuleInfo.class)
                .eq(PointUsageRuleInfo::getMasterType, pageBo.getMasterType())
                .eq(PointUsageRuleInfo::getMasterCode, pageBo.getMasterCode())
                .in(CollectionUtils.isNotEmpty(pageBo.getRuleIds()), PointUsageRuleInfo::getId, pageBo.getRuleIds())
                .like(StringUtils.isNotEmpty(pageBo.getRuleName()), PointUsageRuleInfo::getRuleName, pageBo.getRuleName())
                .eq(pageBo.getState() != null, PointUsageRuleInfo::getState, pageBo.getState()));
    }

    public PointUsageRuleInfo getById(Long id) {
        return pointUsageRuleInfoDao.selectById(id);
    }

    public void updateById(PointUsageRuleInfo ruleInfo) {
        pointUsageRuleInfoDao.updateById(ruleInfo);
    }


    public void insert(PointUsageRuleInfo ruleInfo) {
        pointUsageRuleInfoDao.insert(ruleInfo);
    }

    public void updateState(Long ruleId, Integer state, String operator) {
        Wrapper<PointUsageRuleInfo> wrapper = new UpdateWrapper<PointUsageRuleInfo>().lambda()
                .eq(PointUsageRuleInfo::getId, ruleId)
                .set(PointUsageRuleInfo::getState, state)
                .set(PointUsageRuleInfo::getModifyUser, operator);
        pointUsageRuleInfoDao.update(null, wrapper);
    }

    public void deleteById(Long ruleId, String operator) {
        Wrapper<PointUsageRuleInfo> wrapper = new UpdateWrapper<PointUsageRuleInfo>().lambda()
                .eq(PointUsageRuleInfo::getId, ruleId)
                .set(PointUsageRuleInfo::getIsDelete, Constant.ONE)
                .set(PointUsageRuleInfo::getModifyUser, operator);
        pointUsageRuleInfoDao.update(null, wrapper);
    }
}
