package com.ly.titc.pms.member.entity.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：rui
 * @name：PageUpgradeRuleReq
 * @Date：2024-11-7 20:21
 * @Filename：PageUpgradeRuleReq
 */
@Data
@Accessors(chain = true)
public class PageCardLevelUpgradeRuleBo {

    private Integer masterType;

    private String masterCode;

    private String name;

    private Integer sourceLevel;

    private Integer state;

    private Long cardId;

    private List<Long> cardIdList;

    /**
     * 页码
     */
    private Integer pageIndex = 1;

    /**
     * pageSize
     */
    private Integer pageSize = 20;
}
