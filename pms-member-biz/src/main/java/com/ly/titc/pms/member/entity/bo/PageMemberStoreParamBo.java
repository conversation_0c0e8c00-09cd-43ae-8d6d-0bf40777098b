package com.ly.titc.pms.member.entity.bo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-2-7 17:21
 */
@Data
@Accessors(chain = true)
public class PageMemberStoreParamBo {
    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 消费类型：PAY:消费，FREEZE, REFUND 退款
     */
    private String consumeType;

    /**
     * 渠道
     */
    private String platformChannel;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 订单状态 1 未支付 2 支付成功  3 已退款
     */
    private Integer state;


    private  Integer pageIndex;

    private Integer pageSize;
}
