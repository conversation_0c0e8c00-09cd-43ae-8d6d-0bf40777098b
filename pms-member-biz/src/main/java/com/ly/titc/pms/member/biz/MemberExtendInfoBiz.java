package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ly.titc.pms.member.dal.dao.MemberExtendInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberExtendInfo;
import com.ly.titc.springboot.dcdb.dal.core.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname MemberExtendInfoBiz
 * @descrition 会员扩展信息业务处理类
 * @since 2023/8/3 17:32
 */
@Slf4j
@Component
public class MemberExtendInfoBiz extends AbstractBiz<MemberExtendInfo> {

    @Resource(type = MemberExtendInfoDao.class)
    protected MemberExtendInfoDao memberExtendInfoDao;

    /**
     * 新增
     *
     * @param entity
     */
    public void add(MemberExtendInfo entity) {
        memberExtendInfoDao.insert(entity);
    }

    /**
     * 获取会员扩展信息
     *
     * @param memberNo
     * @return
     */
    public MemberExtendInfo getByMemberNo(String memberNo) {
        return memberExtendInfoDao.selectOne(new LambdaQueryWrapperX<MemberExtendInfo>()
                .eq(MemberExtendInfo::getMemberNo, memberNo));
    }

    /**
     * 更新
     *
     * @param entity
     */
    public void update(MemberExtendInfo entity) {

        UpdateWrapper<MemberExtendInfo> wrapper = Wrappers.update();
        wrapper.lambda()
                .eq(MemberExtendInfo::getMemberNo, entity.getMemberNo())
                .set(MemberExtendInfo::getAvatar, entity.getAvatar())
                .set(MemberExtendInfo::getNationality, entity.getNationality())
                .set(MemberExtendInfo::getNation, entity.getNation())
                .set(MemberExtendInfo::getNativePlace, entity.getNativePlace())
                .set(MemberExtendInfo::getNumberPlate, entity.getNumberPlate())
                .set(MemberExtendInfo::getRemark, entity.getRemark())
                .set(MemberExtendInfo::getLanguage, entity.getLanguage())
        ;
        memberExtendInfoDao.update(entity, wrapper);
    }


    /**
     * 根据会员号删除会员
     *
     * @param memberNo
     */
    public void deleteByMemberNo(String memberNo) {
        memberExtendInfoDao.delete(new LambdaUpdateWrapper<MemberExtendInfo>().eq(MemberExtendInfo::getMemberNo, memberNo));
    }

    public List<MemberExtendInfo> listByMemberNos(List<String> memberNos) {
        return memberExtendInfoDao.selectList(new LambdaQueryWrapperX<MemberExtendInfo>().in(MemberExtendInfo::getMemberNo, memberNos));
    }
}
