package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.pms.member.dal.dao.MemberRelatedConfigInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberRelatedConfigInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 会员相关配置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:51
 */
@Component
public class MemberRelatedConfigBiz {

    @Resource
    private MemberRelatedConfigInfoDao memberRelatedConfigInfoDao;

    public void insert(MemberRelatedConfigInfo configInfo) {
        memberRelatedConfigInfoDao.insert(configInfo);
    }

    public void update(MemberRelatedConfigInfo configInfo) {
        LambdaUpdateWrapper<MemberRelatedConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberRelatedConfigInfo::getId, configInfo.getId());
        wrapper.set(MemberRelatedConfigInfo::getContent, configInfo.getContent())
                .set(MemberRelatedConfigInfo::getType, configInfo.getType())
                .set(MemberRelatedConfigInfo::getModifyUser, configInfo.getModifyUser());
        memberRelatedConfigInfoDao.update(null, wrapper);
    }

    public MemberRelatedConfigInfo getMemberRelatedConfig(Integer masterType, String masterCode, Integer type) {
        LambdaQueryWrapper<MemberRelatedConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberRelatedConfigInfo::getMasterType, masterType);
        wrapper.eq(MemberRelatedConfigInfo::getMasterCode, masterCode);
        wrapper.eq(MemberRelatedConfigInfo::getType, type);
        return memberRelatedConfigInfoDao.selectOne(wrapper);
    }
}
