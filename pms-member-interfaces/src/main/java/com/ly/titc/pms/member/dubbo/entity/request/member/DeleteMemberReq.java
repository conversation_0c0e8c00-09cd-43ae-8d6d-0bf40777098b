package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 删除会员
 *
 * <AUTHOR>
 * @classname DeleteByMemberNoReq
 * @descrition 根据会员编号请求体
 * @since 2023/6/7 13:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class DeleteMemberReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员编号
     */
    @NotEmpty(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;

}
