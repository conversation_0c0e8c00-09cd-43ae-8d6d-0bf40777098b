package com.ly.titc.pms.member.dubbo.entity.message;

import com.ly.titc.common.mq.msg.MsgExt;
import com.ly.titc.pms.member.dubbo.enums.MemberEventEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 会员事件消息
 *
 * <AUTHOR>
 * @date 2024/10/29 10:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class MemberEventMsg extends MsgExt {

    /**
     * 变动类型
     */
    private MemberEventEnum eventType;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员号
     */
    private String memberNo;

}
