package com.ly.titc.pms.member.dubbo.entity.request.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 13:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CreateStoreOrderReq extends CreateOrderBaseRequest {

    /**
     * 会员号
     */
    @NotEmpty(message = "会员号不能为空")
    private String memberNo;

    /**
     * 会员储值信息
     */
    @Valid
    private MemberRechargeReq rechargeRequest;


}
