package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/9 20:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListMemberTagCountReq extends BaseReq {

    /**
     * 标签ID列表
     */
    @NotEmpty(message = "标签列表不能为空")
    private List<Long> tagIds;
}
