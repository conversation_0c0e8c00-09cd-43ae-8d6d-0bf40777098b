package com.ly.titc.pms.member.dubbo.entity.request.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/2/12 21:14
 */
@Data
public class MemberRechargeReq {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 酒馆组
     */
    private String clubCode;

    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 门店编号
     */
    private String hotelCode;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 本金金额
     */
    private BigDecimal capitalAmount;

    /**
     * 礼金金额
     */
    private BigDecimal giftAmount;

    /**
     * 礼金金额过期时间
     */
    private String giftExpireDate;


    /**
     * 操作人
     */
    private String operator;

}
