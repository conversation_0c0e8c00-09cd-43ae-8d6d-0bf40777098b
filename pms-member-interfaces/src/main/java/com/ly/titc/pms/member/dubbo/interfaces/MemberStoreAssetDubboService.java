package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.*;
import com.ly.titc.pms.member.dubbo.entity.request.SelectBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.order.PageMemberStoreRecordReq;
import com.ly.titc.pms.member.dubbo.entity.request.order.RechargeRecordDetailReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRechargeDetailResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRechargeRecordResp;
import com.ly.titc.pms.member.dubbo.entity.response.SelectValueResp;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员积分资产
 * @since 2024-12-29 13:12
 */
public interface MemberStoreAssetDubboService {

    /**
     * 查询储值场景
     *
     * @param request
     * @return
     */
    Response<List<SelectValueResp>> selectStoreScene(@Valid SelectBaseReq request);

    /**
     * 储值充值
     *
     * @param req
     * @return
     */
    Response<MemberStoreRecordOPResultResp> recharge(@Valid MemberStoreRechargeReq req);

    /**
     * 充值后退款
     *
     * @param req
     * @return
     */
    Response<MemberStoreRecordOPResultResp> rechargeRollback(@Valid MemberStoreRechargeRollBackReq req);

    /**
     * 储值消费
     *
     * @param req
     * @return
     */
    Response<MemberStoreRecordOPResultResp> consumeStore(@Valid MemberStoreConsumeReq req);

    /**
     * 储值消费回滚
     *
     * @param req
     * @return
     */
    Response<MemberStoreConsumeRollBackResp> consumeRollback(@Valid MemberStoreConsumeRollBackReq req);



    /**
     * 消费金额预计算
     *
     * @param req
     * @return
     */
    Response<MemberStoreConsumeCalResp> consumeStoreCal(@Valid MemberStoreConsumePreCalReq req);

    /**
     * 冻结
     *
     * @param req
     * @return
     */
    Response<MemberStoreRecordOPResultResp> freeze(@Valid MemberStoreFreezeReq req);

    /**
     * 解冻
     *
     * @param req
     * @return
     */
    Response<MemberStoreRecordOPResultResp> unfreeze(@Valid UnfreezeConsumeRecordNoReq req);


    /**
     * 查询会员的总账户信息
     * 不区分平台和使用方
     * CRM端呈现
     */
    Response<MemberTotalAmountResp> getTotalAccountAmount(@Valid BaseMemberReq req);

    /**
     * 查询会员的指定平台和使用方的可用资产
     * 会同时返回总资产 暂不考虑冻结资产
     * PMS端使用
     */
    Response<MemberStoreAccountResp> getUsableMasterAccount(@Valid GetMemberUsableReq req);


    /**
     * 分页查询会员储值消费记录
     *
     * @param req
     * @return
     */
    Response<Pageable<MemberStoreConsumeRecordResp>> pageConsumeRecord(@Valid PageMemberStoreConsumeMemberReq req);

    /**
     * 查询会员充值记录
     *
     * @param req
     * @return
     */
    Response<List<MemberTradeConsumeRecordResp>> listRechargeConsumeRecord(@Valid ListRechargeConsumeRecordMemberReq req);

    /**
     * 分页查询储值充值记录
     *
     * @param request
     * @return
     */
    Response<Pageable<MemberRechargeRecordResp>> pageRechargeRecord(@Valid PageMemberStoreRecordReq request);

    /**
     * 查询储值充值明细
     *
     * @param request
     * @return
     */
    Response<MemberRechargeDetailResp> getRechargeDetail(@Valid RechargeRecordDetailReq request);
}
