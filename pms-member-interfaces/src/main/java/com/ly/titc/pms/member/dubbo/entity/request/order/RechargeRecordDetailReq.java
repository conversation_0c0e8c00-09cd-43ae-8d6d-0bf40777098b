package com.ly.titc.pms.member.dubbo.entity.request.order;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：RechargeRecordDetailRequest
 * @Date：2024-12-13 16:24
 * @Filename：RechargeRecordDetailRequest
 */
@Data
public class RechargeRecordDetailReq extends BaseReq {

    /**
     * 订单编号
     */
    @NotBlank(message = "订单编号不能为空")
    private String memberOderNo;
}
