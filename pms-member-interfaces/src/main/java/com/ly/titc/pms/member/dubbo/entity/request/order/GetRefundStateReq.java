package com.ly.titc.pms.member.dubbo.entity.request.order;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 查询退款状态
 *
 * <AUTHOR>
 * @date 2024/12/16 17:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetRefundStateReq extends BaseReq {

    /**
     * 收银台退款交易号
     */
    @NotBlank(message = "收银台退款交易号不能为空")
    private String memberOrderPayTradeNo;

    /**
     * 支付中台/储值卡退款单号
     */
    private String refundTradeNo;


}
