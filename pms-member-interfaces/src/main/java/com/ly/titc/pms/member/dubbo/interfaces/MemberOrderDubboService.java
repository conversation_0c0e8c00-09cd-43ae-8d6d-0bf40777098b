package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.order.*;
import com.ly.titc.pms.member.dubbo.entity.response.CreateOrderResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPayOrderResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPayUnifiedOrderResp;

import javax.validation.Valid;

/**
 * 会员订单Dubbo服务
 *
 * <AUTHOR>
 * @date 2025/2/8 15:32
 */
public interface MemberOrderDubboService {

    /**
     * 创建注册订单
     *
     * @param request
     * @return
     */
    Response<CreateOrderResp> createRegisterOrder(@Valid CreateRegisterOrderReq request);

    /**
     * 创建购卡订单
     *
     * @param request
     * @return
     */
    Response<CreateOrderResp> createPurchaseCardOrder(@Valid CreatePurchaseCardOrderReq request);

    /**
     * 创建升级订单
     *
     * @param request
     * @return
     */
    Response<CreateOrderResp> createUpgradeOrder(@Valid CreateUpgradeOrderReq request);

    /**
     * 创建储值订单
     *
     * @param request
     * @return
     */
    Response<CreateOrderResp> createStoreOrder(@Valid CreateStoreOrderReq request);

    /**
     * 支付订单
     *
     * @param request
     * @return
     */
    Response<MemberPayUnifiedOrderResp> payUnifiedOrder(@Valid MemberPayOrderReq request);

    /**
     * 获取支付状态
     *
     * @param request
     * @return
     */
    Response<MemberPayOrderResp> getPayState(@Valid GetPayStateReq request);

    /**
     * 创建退款订单
     *
     * @param request
     * @return
     */
    Response<CreateRefundOrderResp> refundOrder(@Valid RefundOrderReq request);

    /**
     * 获取退款状态
     *
     * @param request
     * @return
     */
    Response<CreateRefundOrderResp> getRefundState(@Valid GetRefundStateReq request);

}
