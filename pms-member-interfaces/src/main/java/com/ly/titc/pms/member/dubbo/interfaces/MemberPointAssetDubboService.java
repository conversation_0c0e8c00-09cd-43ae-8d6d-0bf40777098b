package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberRecordOPResultResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberTotalPointResp;
import com.ly.titc.pms.member.dubbo.entity.request.SelectBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.AdjustPointReq;
import com.ly.titc.pms.member.dubbo.entity.response.SelectValueResp;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员积分资产
 * @since 2024-12-29 13:12
 */
public interface MemberPointAssetDubboService {

    /**
     * 积分调整下拉
     *
     * @param request
     * @return
     */
    Response<List<SelectValueResp>> selectPointItem(@Valid SelectBaseReq request);

    /**
     * 调整会员积分(大于0增加积分，小于0扣减积分)
     *
     * @param request
     * @return
     */
    Response<String> adjustPoint(@Valid AdjustPointReq request);

    /**
     * 获得积分后撤回
     *
     * @param req
     * @return
     */
    Response<MemberRecordOPResultResp> receiveRollback(@Valid ReceiveRollBackMemberPointReq req);

    /**
     * 根据业务单号或者积分记录号查询积分消费记录
     *
     * @param req
     * @return
     */
    Response<List<MemberPointsFlowInfoResp>> listConsumeRecords(@Valid ListMemberPointConsumeForBusinessReq req);

    /**
     * 查询总积分
     *
     * @param req
     * @return
     */
    Response<MemberTotalPointResp> getTotalAccountPoints(@Valid BaseMemberReq req);

    /**
     * 查询会员可用积分
     *
     * @param req
     * @return
     */
    Response<MemberPointAccountResp> getUsableMasterAccount(@Valid GetMemberUsableReq req);

    /**
     * 分页查询会员积分流水
     *
     * @param request
     * @return
     */
    Response<Pageable<MemberPointsFlowInfoResp>> pageMemberPointsFlow(@Valid PageMemberPointsFlowMemberReq request);


}
