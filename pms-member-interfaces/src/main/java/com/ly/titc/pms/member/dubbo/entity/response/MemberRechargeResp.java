package com.ly.titc.pms.member.dubbo.entity.response;

import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @classname MemberRechargeResp
 * @descrition 会员充值信息返回体
 * <AUTHOR>
 * @since 2023/8/16 10:17
 */
@Data
@Accessors(chain = true)
public class MemberRechargeResp {

  /**
   * 会员编号
   */
  private String memberNo;

  /**
   * 会员归属编码(会员号-会员归属类别-会员归属值)
   */
  private String memberAffiliationCode;

  /**
   * 当前可用总金额
   */
  private BigDecimal totalAvailableAmount;

  /**
   * 充值总金额
   */
  private BigDecimal totalRechargeAmount;

  /**
   * 赠送总金额
   */
  private BigDecimal totalPresentAmount;

  /**
   * 过期总金额
   */
  private BigDecimal totalExpiredAmount;

  /**
   * 冻结总金额
   */
  private BigDecimal totalFrozenAmount;
}
