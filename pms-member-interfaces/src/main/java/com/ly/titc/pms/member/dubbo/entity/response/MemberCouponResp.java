package com.ly.titc.pms.member.dubbo.entity.response;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @classname MemberCouponResp
 * @descrition 会员优惠券信息返回体
 * <AUTHOR>
 * @since 2023/8/29 17:55
 */
@Data
@Accessors(chain = true)
public class MemberCouponResp {

  /**
   * 会员编号
   */
  private String memberNo;

  /**
   * 会员归属编号(会员号-会员归属编码)
   */
  private String memberAffiliationNo;

  /**
   * 优惠券集合
   */
  private List<CouponResp> coupons;
}
