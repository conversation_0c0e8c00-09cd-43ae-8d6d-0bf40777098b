package com.ly.titc.pms.member.dubbo.entity.request.coupon;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import javax.validation.constraints.NotEmpty;

import com.ly.titc.pms.member.dubbo.enums.MemberCouponStateEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @classname ListByMemberNoWithinMemberAffiliationNoReq
 * @descrition 根据会员编号和归属查询优惠券请求体
 * <AUTHOR>
 * @since 2023/8/29 18:03
 */
@Data
@Accessors(chain = true)
public class ListByMemberNoWithinMemberAffiliationNoReq extends BaseReq {

  /**
   * 会员编号
   */
  @NotEmpty(message = "会员编号不能为空")
  private String memberNo;

  /**
   * 会员归属编号(会员号-会员归属编码)
   */
  @NotEmpty(message = "会员归属编号不能为空")
  private String memberAffiliationNo;

  /**
   * 会员优惠券状态
   */
  @LegalEnum(target = MemberCouponStateEnum.class, methodName = "getState", message = "会员优惠券状态非法")
  private Integer state;
}
