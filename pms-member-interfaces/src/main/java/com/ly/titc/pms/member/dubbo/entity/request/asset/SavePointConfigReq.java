package com.ly.titc.pms.member.dubbo.entity.request.asset;

import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 保存积分配置
 *
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-24 13:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SavePointConfigReq extends BlocBaseReq {
    private Long id;

    /**
     * 积分有效期
     */
    private Integer pointLimit;


    /**
     * 积分有效期单位 年 YEAR 月:MONTH 日; DAY
     */
    private String pointLimitUnit;

    /**
     * 长期有效 0 否 1 是
     */
    @NotNull(message = "是否长期有效")
    private Integer pointLimitLong;
}
