package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 修改会员信息入参
 *
 * <AUTHOR>
 * @title: UpdateBaseInfoReq
 * @projectName pms-member
 * @description: 修改会员信息入参
 * @date 2023/10/12 11:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class UpdateBaseInfoReq extends BaseReq {

    /**
     * 会员基础信息
     */
    @NotNull(message = "会员基础信息不能为空")
    private MemberInfoReq memberInfo;

    /**
     * 会员拓展信息
     */
    private MemberExtendInfoReq memberExtendInfo;

    /**
     * 会员联系信息
     */
    private MemberContactInfoReq memberContactInfo;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;


}
