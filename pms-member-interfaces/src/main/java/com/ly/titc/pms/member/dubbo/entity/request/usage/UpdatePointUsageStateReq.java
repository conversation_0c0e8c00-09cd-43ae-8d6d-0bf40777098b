package com.ly.titc.pms.member.dubbo.entity.request.usage;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.ecrm.dubbo.enums.StateEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 更新积分规则状态
 *
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:10
 */
@Data
@Accessors(chain = true)
public class UpdatePointUsageStateReq extends BlocBaseReq {

    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空")
    private Long ruleId;

    /**
     * 状态 0 无效 1 有效
     */
    @NotNull(message = "状态不能为空")
    @LegalEnum(target = StateEnum.class,methodName = "getCode")
    private Integer state;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
