package com.ly.titc.pms.member.dubbo.entity.request.order;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-28 18:59
 */
@Data
@Accessors(chain = true)
public class GetPayStateReq extends BaseReq {

    /**
     * 集团code
     */
    @NotBlank(message = "集团不能为空")
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 会员订单号
     */
    @NotBlank(message = "会员订单号不能为空")
    private String memberOrderNo;

    /**
     * 会员支付申请单号
     */
    @NotBlank(message = "会员支付申请单号不能为空")
    private String memberOrderPayTradeNo;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
