package com.ly.titc.pms.member.dubbo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname IdTypeEnum
 * @descrition
 * @since 2021/4/20 上午11:40
 */
@Getter
@AllArgsConstructor
public enum IdTypeEnum {
	/**
	 * 0:身份证;1:护照;2:学生证;3:军官证;4:回乡证;5:台胞证;6:港澳通行证;7:国际海员;8:外国人永久居留（身份）证;9:其他证件;10:警官证;11:士兵证;12:台湾通行证;13:入台证;14:户口薄;15:出生证明;16:中国驾照;17:港澳居民居住证;18:台湾居民居住证
	 */
	IDENTITY_CARD(0, "身份证"),
	PASSPORT(1, "护照"),
	STUDENT_CARD(2,"学生证"),
	MILITARY_OFFICER(3,"军官证"),
	REENTRY_PERMIT(4,"回乡证"),
	MTP(5, "台胞证"),
	HK_MAC_PASS(6,"港澳通行证"),
	INTERNATIONAL_SEAFARER(7,"国际海员"),
	FOREIGNER_PERMIT(8,"外国人永久居留（身份）证"),
	OTHER_PERMIT(9,"其他证件"),
	POLICE_CARD(10,"警官证"),
	SOLDIERS(11,"士兵证"),
	TAIWAN_PASS(12, "台湾通行证"),
	TAIWAN_ENTRY_PASS(13,"入台证"),
	RESIDENCE(14,"户口薄"),
	BIRTH_CERT(15,"出生证明"),
	CHINESE_LICENSE(16,"中国驾照"),
	HK_MAC_PERMIT(17, "港澳居民居住证"),
	TAIWAN_PERMIT(18, "台湾居民居住证"),
	;



	private Integer type;


	private String desc;

	public static String getNameByType(Integer type) {
		for (IdTypeEnum value : IdTypeEnum.values()) {
			if (value.getType().equals(type)) {
				return value.getDesc();
			}
		}
		return null;
	}
}
