package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.data.*;
import com.ly.titc.pms.member.dubbo.entity.request.member.GetByMemberNoReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.GetCheckInStatisticsReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.GetHotelCheckInStatisticsReq;
import com.ly.titc.pms.member.dubbo.entity.request.record.PageCheckinRecordReq;
import com.ly.titc.pms.member.dubbo.entity.request.record.PageLevelChangeRecordReq;
import com.ly.titc.pms.member.dubbo.entity.request.record.PagePurchaseCardRecordReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPayRegisterStatisticsResp;

import javax.validation.Valid;

/**
 * 会员数据记录Dubbo服务
 *
 * <AUTHOR>
 * @date 2025/2/7 19:01
 */
public interface MemberDataRecordDubboService {

    /**
     * 分页查询入住记录
     *
     * @param request
     * @return
     */
    Response<Pageable<CheckinRecordResp>> pageCheckinRecord(@Valid PageCheckinRecordReq request);

    /**
     * 查询总入住统计
     *
     * @param request
     * @return
     */
    Response<CheckInStatisticsResp> getCheckInStatistics(@Valid GetCheckInStatisticsReq request);

    /**
     * 查询门店入住统计
     *
     * @param request
     * @return
     */
    Response<CheckInStatisticsResp> getHotelCheckInStatistics(@Valid GetHotelCheckInStatisticsReq request);

    /**
     * 分页查询购卡记录
     *
     * @param request
     * @return
     */
    Response<Pageable<PurchaseCardRecordResp>> pagePurchaseCardRecord(@Valid PagePurchaseCardRecordReq request);

    /**
     * 分页查询升降级记录
     *
     * @param request
     * @return
     */
    Response<Pageable<MemberLevelChangeRecordResp>> pageMemberLevelChangeRecord(@Valid PageLevelChangeRecordReq request);

    /**
     * 查询时间段内会员注册数量+充值金额统计
     */
    Response<MemberPayRegisterStatisticsResp> getMemberPayRegisterStatistics(@Valid GetMemberPayRegisterStatisticsReq request);

}
