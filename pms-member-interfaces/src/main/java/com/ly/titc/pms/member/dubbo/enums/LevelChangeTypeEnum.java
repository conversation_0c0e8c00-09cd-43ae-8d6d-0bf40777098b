package com.ly.titc.pms.member.dubbo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 等级变化类型枚举
 *
 * <AUTHOR>
 * @title: LevelChangeTypeEnum
 * @projectName pms-member
 * @description: 等级变化类型枚举
 * @date 2023/10/11 15:03
 */
@AllArgsConstructor
@Getter
public enum LevelChangeTypeEnum {

    REGISTER(1, "会员注册"),

    UPGRADE(2, "会员升级"),

    RELEGATION_SUCCESS(3, "保级成功"),

    RELEGATION_FAIL(4, "保级失败"),

    MANUAL_OPERATION(5, "手动处理"),

    TRANSFER(6, "迁移"),

    ;

    private final Integer type;

    /**
     * 描述
     */
    private final String desc;

}
