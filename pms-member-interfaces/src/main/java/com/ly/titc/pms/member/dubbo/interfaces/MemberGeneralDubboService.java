package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.general.ListMemberIdentityReq;
import com.ly.titc.pms.member.dubbo.entity.request.general.ListMemberSourceReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberGeneralCardConfigResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberGeneralSourceResp;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员公共组件相关接口
 *
 * @Author：rui
 * @name：MemberGeneralDubboService
 * @Date：2024-12-3 19:51
 * @Filename：MemberGeneralDubboService
 */
public interface MemberGeneralDubboService {

    /**
     * 查询会员来源
     *
     * @param req req
     * @return 会员来源
     */
    Response<List<MemberGeneralSourceResp>> listMemberSource(@Valid ListMemberSourceReq req);

    /**
     * 查询会员身份
     *
     * @param req req
     * @return 会员身份
     */
    Response<List<MemberGeneralCardConfigResp>> listMemberIdentity(@Valid ListMemberIdentityReq req);


}
