package com.ly.titc.pms.member.dubbo.entity.request.order;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-8-23 10:39
 */
@Data
@Accessors(chain = true)
public class PayAccountItemReq {


    @NotEmpty(message = "账户号不能为空")
    private String accountNo;
    @NotEmpty(message = "账户名称不能为空")
    private String accountName;

    /**
     * 账户大类型：CUSTOMER 客房账务；NON_CUSTOMER(非客房)
     */
    @NotEmpty(message = "账户大类型不能为空")
    private String accountType;

    /**
     * 线下支付可以自定义结算项code
     */
    private String itemCode;


    /**
     * 结算项code
     */
    private String itemName;





}
