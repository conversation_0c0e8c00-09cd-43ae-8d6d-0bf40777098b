package com.ly.titc.pms.member.dubbo.entity.request.member;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 手动回收会员卡请求
 *
 * <AUTHOR>
 * @date 2024/12/25 10:00
 */
@Data
public class RecycleMemberCardManualRequest {

    private Integer masterType;

    private String masterCode;

    /**
     * 酒店编码
     */
    @NotBlank(message = "酒店编码不能为空")
    private String hotelCode;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空")
    private String bizType;

    /**
     * 业务id(必传，用于查询之前的下发操作)
     */
    @NotBlank(message = "业务ID不能为空")
    private String bizNo;

    /**
     * 回收原因
     */
    private String reason;
}
