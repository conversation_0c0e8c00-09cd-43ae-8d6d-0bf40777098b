package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.common.annotation.LegalNormalDate;
import com.ly.titc.pms.member.dubbo.enums.HotelTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 会员卡信息
 *
 * <AUTHOR>
 * @title: MemberCardInfoReq
 * @projectName pms-member
 * @description: 会员卡信息
 * @date 2023/11/16 14:44
 */
@Data
public class MemberCardInfoReq {

    /**
     * 会员卡号
     */
    @NotBlank(message = "会员卡号不能为空")
    private String memberCardNo;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 会员卡ID
     */
    @NotNull(message = "会员卡ID不能为空")
    private Long cardId;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 会员卡有效期开始日期 yyyy-MM-dd
     */
    @LegalNormalDate(message = "会员卡等级生效日期不合法[pattern:yyyy-MM-dd]")
    private String effectBeginDate;

    /**
     * 会员卡有效期结束日期 yyyy-MM-dd
     */
    @LegalNormalDate(message = "会员卡等级失效日期不合法[pattern:yyyy-MM-dd]")
    private String effectEndDate;

    /**
     * 是否长期有效 0 否 1 是
     */
    private Integer isLongTerm;

    /**
     * 发放门店类型 集团:BLOC;门店:HOTEL
     */
    @NotBlank(message = "发放门店类型不合法")
    @LegalEnum(target = HotelTypeEnum.class, methodName = "getType", message = "发放门店类型不合法")
    private String issueHotelType;

    /**
     * 发放门店(集团编号; 酒店编号)
     */
    @NotBlank(message = "发放门店不能为空")
    private String issueHotel;

    /**
     * 排序值
     */
    private Integer sort;

}
