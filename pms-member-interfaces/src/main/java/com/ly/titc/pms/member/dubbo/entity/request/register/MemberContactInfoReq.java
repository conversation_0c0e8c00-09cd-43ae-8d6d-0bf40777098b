package com.ly.titc.pms.member.dubbo.entity.request.register;

import com.ly.titc.common.annotation.LegalEmail;
import lombok.Data;

/**
 * 会员联系方式入参
 *
 * <AUTHOR>
 * @date 2024/11/5 10:11
 */
@Data
public class MemberContactInfoReq {

    /**
     * 邮箱
     */
    @LegalEmail(message = "邮箱不合法")
    private String email;

    /**
     * QQ号
     */
    private String qq;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 国家id
     */
    private Integer countryId;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 市id
     */
    private Integer cityId;

    /**
     * 区id
     */
    private Integer districtId;

    /**
     * 住址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;
}
