package com.ly.titc.pms.member.dubbo.entity.request.member;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 会员拓展信息
 *
 * @Author：rui
 * @name：MemberExtendInfoRequest
 * @Date：2024-11-18 20:30
 * @Filename：MemberExtendInfoRequest
 */
@Data
public class MemberExtendInfoReq {
    /**
     * 会员号
     */
    @NotBlank(message = "会员号不能为空")
    private String memberNo;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 民族
     */
    private String nation;

    /**
     * 车牌号
     */
    private String numberPlate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 语言
     */
    private String language;
}
