package com.ly.titc.pms.member.dubbo.entity.request.usage;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 保存积分使用规则
 *
 * <AUTHOR>
 * @date 2025/6/12 09:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SavePointUsageConfigReq extends BaseReq {

    /**
     * 积分规则ID
     */
    private Long id;

    /**
     * 酒馆组编码（冗余存储）
     */
    private String clubCode;

    /**
     * 集团编码（冗余存储）
     */
    @NotBlank(message = "集团编号不能为空")
    private String blocCode;

    /**
     * 酒店编码（冗余存储）
     */
    private Integer hotelCode;

    /**
     * 规则名称
     */
    @NotEmpty(message = "规则名称不能为空")
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 适用渠道，逗号隔开 使用渠道 线下酒店：PMS、CRM  微订房：微订房公众号、微订房小程序
     */
    @NotEmpty(message = "适用渠道不能为空")
    private List<String> scopePlatformChannels;

    /**
     * 配置的适用来源 CLUB(集团组) BLOC (集团)，HOTEL(门店)
     */
    @NotNull(message = "适用来源不能为空")
    private List<String> scopeSources;

    /**
     * 配置的门店范围 1 全部门店 2 指定门店
     */
    @NotNull(message = "选择门店范围不能为空")
    private Integer scopeHotelRange;

    /**
     * 适用酒店codes
     * 适用来源是门店时 必填
     */
    private List<String> scopeHotelCodes;

    /**
     * 是否可用 1可使用 0不可使用
     */
    @NotNull(message = "是否可用不能为空")
    private Integer isCanUse;

    /**
     * 使用模式 1.指定门店可用，2.仅充值门店可用，3.全部门店可用
     */
    @NotNull(message = "使用模式不能为空")
    private Integer usageMode;

    /**
     * 指定可用酒店codes
     */
    private List<String> usageHotelCodes;

    /**
     * 使用是否需要密码 1：需要 0 不需要
     */
    private Integer isUsePassword;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;

}
