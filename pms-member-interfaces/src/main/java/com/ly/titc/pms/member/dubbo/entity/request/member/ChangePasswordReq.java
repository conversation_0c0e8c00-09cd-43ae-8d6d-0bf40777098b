package com.ly.titc.pms.member.dubbo.entity.request.member;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 修改密码
 *
 * <AUTHOR>
 * @date 2025/1/23 16:40
 */
@Data
public class ChangePasswordReq {

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 新密码
     */
    private String newPassword;

    /**
     * 确认新密码
     */
    private String confirmPassword;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;
}
