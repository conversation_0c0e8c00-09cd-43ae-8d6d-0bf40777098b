package com.ly.titc.pms.member.dubbo.entity.request.asset;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.AssetBusinessTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.PointActionTypeEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 调整会员积分
 *
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-20 10:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class AdjustPointReq extends BaseReq {

    /**
     * 归属主体类型
     */
    @NotNull(message = "归属主体类型不能为空")
    private Integer masterType;

    /**
     * 归属主体值
     */
    @NotEmpty(message = "归属主体值不能为空")
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 业务类型：客房单-ROOM，会员-MEMBER，商品部-SHOP ,营销活动 SPM
     */
    @NotEmpty(message = "业务类型不能为空")
    @LegalEnum(methodName = "getType",message = "业务类型不正确", target = AssetBusinessTypeEnum.class)
    private String businessType;

    /**
     * 业务订单编号
     */
    @NotEmpty(message = "业务订单编号不能为空")
    private String businessNo;

    /**
     * 操作类型
     */
    @NotEmpty(message = "操作类型不能为空")
    @LegalEnum(methodName = "getType",message = "操作类型不正确", target = PointActionTypeEnum.class)
    private String actionType;

    /**
     * 积分调整项目
     */
    @NotBlank(message = "积分调整项目不能为空")
    private String actionItem;

    /**
     * 积分项目描述
     */
    @NotBlank(message = "积分项目描述不能为空")
    private String actionItemDesc;

    /**
     * 积分数（加为正，减为负）
     */
    @NotNull(message = "积分数不能为空")
    private Integer score;

    /**
     * 会员编号
     */
    @NotBlank(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 调整原因
     */
    @NotBlank(message = "调整原因不能为空")
    private String remark;

    /**
     * 平台渠道 CMS ，PMS ，微订房小程序，微订房公众号，艺龙会小程序
     */
    @NotEmpty(message = "使用平台不能为空")
    private String platformChannel;

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 过期时间 yyyy-MM-dd
     */
    private String expireDate;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
