package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 创建订单返回
 *
 * <AUTHOR>
 * @date 2025/2/8 15:34
 */
@Data
public class CreateOrderResp {

    /**
     * 会员订单号
     */
    private String memberOrderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员业务场景
     */
    private String memberScene;

    /**
     * 会员业务场景描述
     */
    private String memberSceneDesc;

    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 售价类型
     */
    private String amountType;

}
