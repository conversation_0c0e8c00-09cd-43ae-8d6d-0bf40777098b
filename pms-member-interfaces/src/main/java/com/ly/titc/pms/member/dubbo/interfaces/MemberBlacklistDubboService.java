package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.member.BlacklistMemberReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.CancelBlacklistMemberReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.ListBlacklistParamReq;
import com.ly.titc.pms.member.dubbo.entity.response.BlacklistInfoResp;
import com.ly.titc.pms.member.dubbo.entity.response.SelectValueResp;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员黑名单dubbo服务
 *
 * <AUTHOR>
 * @date 2025/2/7 16:01
 */
public interface MemberBlacklistDubboService {

    /**
     * 获取黑名单场景
     *
     * @return
     */
    Response<SelectValueResp> listBlacklistScene();

    /**
     * 获取黑名单渠道
     *
     * @return
     */
    Response<SelectValueResp> listPlatformChannel();

    /**
     * 拉黑会员
     *
     * @param request
     * @return 黑名单编号
     */
    Response<String> blacklist(@Valid BlacklistMemberReq request);

    /**
     * 取消拉黑会员
     *
     * @param request
     * @return 黑名单编号
     */
    Response<String> cancelBlacklist(@Valid CancelBlacklistMemberReq request);

    /**
     * 分页查询拉黑信息
     *
     * @param request
     * @return
     */
    Response<List<BlacklistInfoResp>> listBlacklist(@Valid ListBlacklistParamReq request);
}
