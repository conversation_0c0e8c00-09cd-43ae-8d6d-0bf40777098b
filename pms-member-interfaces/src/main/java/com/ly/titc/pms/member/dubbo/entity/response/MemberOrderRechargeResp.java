package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-1-8 13:45
 */
@Data
@Accessors(chain = true)
public class MemberOrderRechargeResp {


    /**
     * 是否是长期有效
     */
    private boolean isPerpetualEffect;

    /**
     * 礼金有效期
     */
    private String giftExpireDate;

    /**
     * 储值总额
     */
    private BigDecimal capitalAmount;

    /**
     * 礼金总额
     */
    private BigDecimal giftAmount;


    /**
     * 充值明细--充值面额
     */
    private BigDecimal priceCapitalAmount;

    /**
     * 充值明细--赠送面额
     */
    private BigDecimal priceGiftAmount;

}
