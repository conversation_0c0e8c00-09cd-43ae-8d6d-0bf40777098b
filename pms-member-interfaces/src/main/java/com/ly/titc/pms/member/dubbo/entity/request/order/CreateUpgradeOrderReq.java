package com.ly.titc.pms.member.dubbo.entity.request.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 升级订单
 *
 * <AUTHOR>
 * @date 2024/12/10 14:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CreateUpgradeOrderReq extends CreateOrderBaseRequest {

    /**
     * 会员卡ID
     */
    @NotNull(message = "会员卡ID不能为空")
    private Long cardId;

    /**
     * 会员号
     */
    @NotBlank(message = "会员号不能为空")
    private String memberNo;

    /**
     * 会员卡号
     */
    @NotBlank(message = "会员卡号不能为空")
    private String memberCardNo;

    /**
     * 会员卡名称
     */
    @NotBlank(message = "会员卡名称不能为空")
    private String cardName;

    /**
     * 变更前等级
     */
    @NotNull(message = "变更前等级不能为空")
    private Integer preLevel;

    /**
     * 变更前等级名称
     */
    @NotNull(message = "变更前等级名称不能为空")
    private String preLevelName;

    /**
     * 变更后等级
     */
    @NotNull(message = "变更后等级不能为空")
    private Integer afterLevel;

    /**
     * 变更后等级名称
     */
    @NotNull(message = "变更后等级名称不能为空")
    private String afterLevelName;

}
