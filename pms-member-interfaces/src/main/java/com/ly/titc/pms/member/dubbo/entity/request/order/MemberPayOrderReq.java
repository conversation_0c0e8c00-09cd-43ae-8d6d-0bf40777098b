package com.ly.titc.pms.member.dubbo.entity.request.order;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 16:31
 */
@Data
@Accessors(chain = true)
public class MemberPayOrderReq extends BaseReq {
    /**
     * 会员订单号
     */
    @NotEmpty(message = "会员订单号不能为空")
    private String memberOrderNo;


    /**
     * 金额 （保留到分）
     */
    @NotNull(message = "支付金额不能为空")
    private BigDecimal amount;

    /**
     * 平台渠道
     */

    @NotEmpty(message = "平台渠道不能为空")
    private String platformChannel;

    /**
     * 归属类型
     */
    @NotNull(message = "归属类型不能为空")
    private Integer masterType;

    /**
     * 归属编码
     */
    @NotEmpty(message = "归属编码不能为空")
    private String masterCode;

    /**
     * 酒店code （Pms端必传）
     */
    private String hotelCode;

    /**
     * 集团code （Crs端必传）
     */
    private String blocCode;


    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;
}
