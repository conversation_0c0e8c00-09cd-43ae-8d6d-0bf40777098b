package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.member.ListMemberTagCountReq;
import com.ly.titc.pms.member.dubbo.entity.request.profile.*;
import com.ly.titc.pms.member.dubbo.entity.response.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员档案Dubbo服务
 *
 * <AUTHOR>
 * @date 2024/10/28 16:04
 */
public interface MemberProfileDubboService {

    /**
     * 保存会员常用地址
     *
     * @param req
     * @return
     */
    Response<String> saveCommonAddress(@Valid SaveCommonAddressReq req);

    /**
     * 删除会员常用地址
     *
     * @param req
     * @return
     */
    Response<String> deleteCommonAddress(@Valid DeleteCommonAddressReq req);

    /**
     * 查询会员常用地址
     *
     * @param req
     * @return
     */
    Response<List<MemberProfileAddressResp>> listCommonAddress(@Valid ListCommonAddressReq req);

    /**
     * 查询会员常用地址
     *
     * @param req
     * @return
     */
    Response<MemberProfileAddressResp> getCommonAddress(@Valid GetCommonAddressReq req);

    /**
     * 保存常用发票抬头
     *
     * @param req
     * @return
     */
    Response<String> saveCommonInvoiceHeader(@Valid SaveCommonInvoiceHeaderReq req);

    /**
     * 删除常用发票抬头
     *
     * @param req
     * @return
     */
    Response<String> deleteCommonInvoiceHeader(@Valid DeleteCommonInvoiceHeaderReq req);

    /**
     * 查询常用发票抬头
     *
     * @param req
     * @return
     */
    Response<List<MemberProfileInvoiceHeaderResp>> listCommonInvoiceHeader(@Valid ListCommonInvoiceHeaderReq req);

    /**
     * 查询常用发票抬头
     *
     * @param req
     * @return
     */
    Response<MemberProfileInvoiceHeaderResp> getCommonInvoiceHeader(@Valid GetCommonInvoiceHeaderReq req);

    /**
     * 保存常用入住人
     *
     * @param req
     * @return
     */
    Response<String> saveCommonOccupants(@Valid SaveCommonOccupantsReq req);

    /**
     * 删除常用入住人
     *
     * @param req
     * @return
     */
    Response<String> deleteCommonOccupants(@Valid DeleteCommonOccupantsReq req);

    /**
     * 查询常用入住人
     *
     * @param req
     * @return
     */
    Response<List<MemberProfileOccupantsResp>> listCommonOccupants(@Valid ListCommonOccupantsReq req);

    /**
     * 查询常用入住人
     *
     * @param req
     * @return
     */
    Response<MemberProfileOccupantsResp> getCommonOccupants(@Valid GetCommonOccupantsReq req);

    /**
     * 添加会员标签
     *
     * @param req
     * @return
     */
    Response<String> addMemberTag(@Valid AddMemberTagReq req);

    /**
     * 批量添加会员标签
     *
     * @param req
     * @return
     */
    Response<String> batchAddMemberTag(@Valid BatchAddMemberTagReq req);

    /**
     * 删除会员标签
     *
     * @param req
     * @return
     */
    Response<String> deleteMemberTag(@Valid DeleteMemberTagReq req);

    /**
     * 查询会员标签
     *
     * @param req
     * @return
     */
    Response<List<MemberProfileTagResp>> getMemberTag(@Valid GetMemberTagReq req);

    /**
     * 批量查询会员标签
     *
     * @param req
     * @return
     */
    Response<List<SimpleMemberProfileTagGroupResp>> listMemberTagGroup(@Valid ListMemberTagReq req);

    /**
     * 查询会员标签（分组）
     *
     * @param req
     * @return
     */
    Response<List<MemberProfileTagGroupResp>> getMemberTagGroup(@Valid GetMemberTagReq req);

    /**
     * 查询会员标签数量
     *
     * @param req
     * @return
     */
    Response<List<MemberProfileTagCountResp>> listMemberTagCount(@Valid ListMemberTagCountReq req);
}
