package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.MemberStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 根据手机号查询会员
 *
 * <AUTHOR>
 * @date 2024/10/29 20:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class ListByMobileReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 手机号列表
     */
    @NotEmpty(message = "手机号不能为空")
    @Size(max = 2000, message = "查询数量不能超过2000")
    private List<String> mobiles;

    /**
     * 会员状态
     */
    @LegalEnum(target = MemberStateEnum.class, methodName = "getState", message = "会员状态不合法")
    private Integer state = MemberStateEnum.VALID.getState();


}
