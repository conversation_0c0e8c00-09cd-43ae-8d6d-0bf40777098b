package com.ly.titc.pms.member.dubbo.entity.request.coupon;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import java.sql.Timestamp;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.ly.titc.pms.member.dubbo.enums.MemberCouponStateEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @classname IssueCouponByMemberNoReq
 * @descrition 根虎会员编号发放优惠券
 * <AUTHOR>
 * @since 2023/8/29 18:05
 */
@Data
@Accessors(chain = true)
public class IssueCouponByMemberNoReq extends BaseReq {

  /**
   * 会员编号
   */
  @NotEmpty(message = "会员编号不能为空")
  private String memberNo;

  /**
   * 会员归属编号(会员号-会员归属编码)
   * <p>传入会员归属编号则使用传入的</p>
   * <p>未传入会员归属编号则使用默认的</p>
   */
  private String memberAffiliationNo;

  /**
   * 集团编码
   */
  @NotEmpty(message = "集团编码不能为空")
  private String blocCode;

  /**
   * 优惠券编码
   */
  @NotEmpty(message = "优惠券编码不能为空")
  private String couponCode;

  /**
   * 优惠券名称
   */
  @NotEmpty(message = "优惠券名称不能为空")
  private String couponName;

  /**
   * 优惠券类型
   */
  @NotEmpty(message = "优惠券类型不能为空")
  private String couponType;

  /**
   * 券值;eg:折扣券是折扣值;抵扣券是抵扣金额
   */
  private String couponValue;

  /**
   * 优惠券提示
   */
  private String couponTips;

  /**
   * 优惠券内容
   */
  private String couponContent;

  /**
   * 有效期开始时间
   */
  @NotNull(message = "有效期开始时间不能为空")
  private Timestamp startDateTime;

  /**
   * 有效期结束时间
   */
  @NotNull(message = "有效期结束时间不能为空")
  private Timestamp endDateTime;

  /**
   * 优惠券适用规则json
   */
  private String couponApplicableRuleJson;

  /**
   * 会员优惠券状态
   */
  @NotNull(message = "会员优惠券状态不能为空")
  @LegalEnum(target = MemberCouponStateEnum.class, methodName = "getState", message = "会员优惠券状态非法")
  private Integer state = MemberCouponStateEnum.VALID.getState();

  /**
   * 优惠券编号集合
   */
  @NotEmpty(message = "优惠券编号集合不能为空")
  private List<String> couponNos;

  /**
   * 操作人
   */
  @NotEmpty(message = "操作人不能为空")
  private String operator;
}
