package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-2-20 20:27
 */
@Data
@Accessors(chain = true)
public class GetUsableMemberReq extends BaseReq {

    /**
     * 集团编码（必传）
     */
    @NotEmpty(message = "集团编码不能为空")
    private String blocCode;


    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员编号
     */
    @NotEmpty(message = "会员编号不能为空")
    private String memberNo;

    @NotEmpty(message = "会员卡号不能为空")
    private String memberCardNo;

    /**
     * 会员资产归属类型
     */
    @NotNull(message = "会员资产归属类型不能为空")
    private Integer scopeMasterType;

    /**
     * 会员资产的归属值
     */
    @NotEmpty(message = "会员资产的归属值不能为空")
    private String scopeMasterCode;

    /**
     * 平台渠道 CMS ，PMS ，微订房小程序，微订房公众号，艺龙会小程序
     */
    @NotEmpty(message = "使用平台不能为空")
    private String platformChannel;
}
