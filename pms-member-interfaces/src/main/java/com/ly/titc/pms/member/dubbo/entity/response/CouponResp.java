package com.ly.titc.pms.member.dubbo.entity.response;

import java.sql.Timestamp;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @classname MemberCouponResp
 * @descrition 会员优惠券信息返回体
 * <AUTHOR>
 * @since 2023/8/29 17:55
 */
@Data
@Accessors(chain = true)
public class CouponResp {

  /**
   * 集团编码
   */
  private String blocCode;

  /**
   * 优惠券编码
   */
  private String couponCode;

  /**
   * 优惠券名称
   */
  private String couponName;

  /**
   * 优惠券编号
   */
  private String couponNo;

  /**
   * 优惠券类型
   */
  private String couponType;

  /**
   * 券值;eg:折扣券是折扣值;抵扣券是抵扣金额
   */
  private String couponValue;

  /**
   * 优惠券提示
   */
  private String couponTips;

  /**
   * 优惠券内容
   */
  private String couponContent;

  /**
   * 有效期开始时间
   */
  private Timestamp startDateTime;

  /**
   * 有效期结束时间
   */
  private Timestamp endDateTime;

  /**
   * 优惠券适用规则json
   */
  private String couponApplicableRuleJson;

  /**
   * 优惠券状态;
   */
  private Integer state;

}
