package com.ly.titc.pms.member.dubbo.entity.request.profile;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.common.annotation.LegalPhoneNumber;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 保存常用地址
 *
 * <AUTHOR>
 * @date 2024/10/31 17:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveCommonAddressReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员编号
     */
    @NotEmpty(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 常用地址编号
     */
    private Long addressNo;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @LegalPhoneNumber(message = "手机号不合法")
    private String mobile;

    /**
     * 国家id
     */
    @NotNull(message = "国家ID不能为空")
    private Long countryId;

    /**
     * 国家
     */
    private String country;

    /**
     * 省id
     */
    @NotNull(message = "省ID不能为空")
    private Long provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市id
     */
    @NotNull(message = "城市ID不能为空")
    private Long cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域id
     */
    @NotNull(message = "区域ID不能为空")
    private Long districtId;

    /**
     * 区域
     */
    private String district;

    /**
     * 地址
     */
    @NotBlank(message = "地址不能为空")
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 标签
     */
    private String tag;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空")
    private Integer sort;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;

}
