package com.ly.titc.pms.member.dubbo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员事件
 *
 * <AUTHOR>
 * @date 2024/11/11 13:42
 */
@AllArgsConstructor
@Getter
public enum MemberEventEnum {

    REGISTER("REGISTER", "会员注册"),
    MODIFY("MODIFY", "会员信息更新"),
    CANCEL("CANCEL", "会员注销"),
    RECOVER("RECOVER", "会员恢复"),
    BLACKLIST("BLACKLIST", "会员拉黑"),
    CANCEL_BLACKLIST("CANCEL_BLACKLIST", "取消拉黑"),
    MARK("MARK", "会员打标"),
    MARK_DELETE("MARK_DELETE", "会员标签删除"),
    ISSUE_CARD("ISSUE_CARD", "会员发放会员卡"),
    MODIFY_CARD("MODIFY_CARD", "会员卡更新");

    private final String eventType;

    private final String desc;
}
