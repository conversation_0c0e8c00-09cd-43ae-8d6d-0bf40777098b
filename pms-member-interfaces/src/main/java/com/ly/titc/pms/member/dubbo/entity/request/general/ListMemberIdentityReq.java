package com.ly.titc.pms.member.dubbo.entity.request.general;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：QueryMemberIdentity
 * @Date：2024-12-3 20:26
 * @Filename：QueryMemberIdentity
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListMemberIdentityReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属不能为空")
    private Integer masterType;

    /**
     * 归属值
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 名称-模糊搜索
     */
    private String name;
}
