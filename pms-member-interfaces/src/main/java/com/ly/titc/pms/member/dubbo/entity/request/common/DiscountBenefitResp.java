package com.ly.titc.pms.member.dubbo.entity.request.common;

import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-13 11:44
 */
@Data
public class DiscountBenefitResp {

    /**
     * 优惠类型
     */
    private String discountType;


    private String gearCode;
    /**
     * 优惠奖励code/数值
     */
    private String benefitValue;

    /**
     * 奖励名
     */
    private String benefitName;

    /**
     * 奖励描述
     */
    private String benefitDesc;

    /**
     * 基准价类型
     */
    private Integer basePriceType;

    /**
     * 奖励数量
     */
    private Integer discountCount;

    /**
     * 是否默认
     * 1:默认 0:不是
     */
    private Integer isDefault;
}
