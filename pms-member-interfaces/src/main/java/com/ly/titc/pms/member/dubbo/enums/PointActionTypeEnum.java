package com.ly.titc.pms.member.dubbo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-18 19:48
 */
@AllArgsConstructor
@Getter
public enum PointActionTypeEnum {
    ADJUST("ADJUST","调整"),
    GIVE("GIVE","事件赠送"),
    RECOVERY("RECOVERY","事件赠送回收"),;
    ;

    private String type;

    private String desc;

    public static PointActionTypeEnum getByType(String tradeType) {
        for (PointActionTypeEnum typeEnum : PointActionTypeEnum.values()) {
            if (typeEnum.getType().equals(tradeType)) {
                return typeEnum;
            }
        }
        return null;
    }
}
