package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 会员和会员卡信息
 *
 * <AUTHOR>
 * @classname MemberResp
 * @descrition 订单信息返回体
 * @since 2023/6/7 13:52
 */
@Data
@Accessors(chain = true)
public class MemberWithCardInfoResp {

    /**
     * 会员基本信息
     */
    private MemberInfoResp memberInfo;

    /**
     * 会员卡信息集合
     */
    private List<MemberCardInfoResp> memberCardInfos;

}
