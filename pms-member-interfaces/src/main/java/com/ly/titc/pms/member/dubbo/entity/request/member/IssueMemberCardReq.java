package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.common.annotation.LegalNormalDate;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 发放的会员卡
 *
 * <AUTHOR>
 * @date 2024/11/19 17:14
 */
@Data
public class IssueMemberCardReq {

    /**
     * 会员卡号
     */
    @NotBlank(message = "会员卡号不能为空")
    private String memberCardNo;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 会员卡ID
     */
    @NotNull(message = "会员卡ID不能为空")
    private Long cardId;

    /**
     * 会员卡名称
     */
    @NotBlank(message = "会员卡名称不能为空")
    private String cardName;

    /**
     * 会员卡等级
     */
    @NotNull(message = "会员卡等级不能为空")
    private Integer cardLevel;

    /**
     * 会员卡等级名称
     */
    @NotBlank(message = "会员卡等级名称不能为空")
    private String cardLevelName;

    /**
     * 生效日期
     */
    @LegalNormalDate(message = "会员卡等级生效日期不合法[pattern:yyyy-MM-dd]")
    private String effectBeginDate;

    /**
     * 失效日期
     */
    @LegalNormalDate(message = "会员卡等级失效日期不合法[pattern:yyyy-MM-dd]")
    private String effectEndDate;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 会员卡费
     */
    private BigDecimal cardPrice;

}
