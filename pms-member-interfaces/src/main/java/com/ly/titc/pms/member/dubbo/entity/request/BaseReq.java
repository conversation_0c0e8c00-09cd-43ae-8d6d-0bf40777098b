package com.ly.titc.pms.member.dubbo.entity.request;

import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.UUID;

/**
 * <AUTHOR>
 * @classname BaseReq
 * @descrition
 * @since 2021/3/10 上午10:25
 */
@Data
@Accessors(chain = true)
public class BaseReq {

	/**
	 * 请求追踪id
	 */
	@NotBlank(message = "trackingId不能为空")
	private String trackingId = UUID.randomUUID().toString();
}
