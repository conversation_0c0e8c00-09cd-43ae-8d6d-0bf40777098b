package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.cc.entity.request.BaseReq;
import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 会员卡信息更新
 *
 * <AUTHOR>
 * @date 2024/10/31 10:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors
public class UpdateCardInfoReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员号
     */
    @NotBlank(message = "会员号不能为空")
    private String memberNo;

    /**
     * 会员卡号
     */
    @NotBlank(message = "会员卡号不能为空")
    private String memberCardNo;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 会员卡生效时间
     */
    private String effectBeginDate;

    /**
     * 会员卡失效时间
     */
    private String effectEndDate;

    /**
     * 状态 0 无效 1 有效 2 停用
     */
    private Integer state;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
