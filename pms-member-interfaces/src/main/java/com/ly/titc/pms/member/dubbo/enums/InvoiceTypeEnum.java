package com.ly.titc.pms.member.dubbo.enums;

/**
 * @Author：rui
 * @name：invoiceTypeEnum
 * @Date：2024-11-19 22:50
 * @Filename：invoiceTypeEnum
 */
public enum InvoiceTypeEnum {
    // 1 普通发票 2 专用发票

    NORMAL(1, "普通发票"),
    SPECIAL(2, "专用发票");
    private Integer type;
    private String name;

    InvoiceTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getNameByType(Integer type) {
        for (InvoiceTypeEnum invoiceTypeEnum : InvoiceTypeEnum.values()) {
            if (invoiceTypeEnum.getType().equals(type)) {
                return invoiceTypeEnum.getName();
            }
        }
        return null;
    }

    public static Integer getTypeByName(String name) {
        for (InvoiceTypeEnum invoiceTypeEnum : InvoiceTypeEnum.values()) {
            if (invoiceTypeEnum.getName().equals(name)) {
                return invoiceTypeEnum.getType();
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
