package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 卡号校验入参
 *
 * <AUTHOR>
 * @title: MemberCardInfoReq
 * @projectName pms-member
 * @description: 会员卡信息
 * @date 2023/11/16 14:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CheckHasCardReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员卡ID
     */
    @NotBlank(message = "会员ID不能为空")
    private Long cardId;

    /**
     * 会员卡登记
     */
    private Integer cardLevel;

}
