package com.ly.titc.pms.member.dubbo.enums;

/**
 * @Author：rui
 * @name：ApplicationScopeTypeEnum
 * @Date：2024-11-19 19:59
 * @Filename：ApplicationScopeTypeEnum
 */
public enum ApplicationScopeTypeEnum {

    ALL(1, "全部"),

    PART(0, "部分"),
    ;

    private Integer type;
    private String name;

    ApplicationScopeTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static ApplicationScopeTypeEnum getByType(Integer type) {
        for (ApplicationScopeTypeEnum value : ApplicationScopeTypeEnum.values()) {
           if (value.getType().equals(type)) {
               return value;
           }
        }
        return null;
    }

    public static String getNameByType(Integer type) {
        for (ApplicationScopeTypeEnum value : ApplicationScopeTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getName();
            }
        }
        return "";
    }

    public String getName() {
        return name;
    }

    public Integer getType() {
        return type;
    }
}
