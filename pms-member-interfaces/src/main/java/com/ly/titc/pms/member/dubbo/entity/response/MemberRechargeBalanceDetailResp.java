package com.ly.titc.pms.member.dubbo.entity.response;

import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @classname MemberRechargeBalanceDetailResp
 * @descrition 会员收支明细返回体
 * <AUTHOR>
 * @since 2023/8/16 10:20
 */
@Data
@Accessors(chain = true)
public class MemberRechargeBalanceDetailResp {

  /**
   * 会员编号
   */
  private String memberNo;

  /**
   * 会员归属编码(会员号-会员归属类别-会员归属值)
   */
  private String memberAffiliationCode;

  /**
   * 会员等级
   */
  private Integer memberLevel;

  /**
   * 储值行为：0.充值 1.赠送 2.消费 3:冻结
   */
  private Integer action;

  /**
   * 场景
   */
  private String scene;

  /**
   * 金额
   */
  private BigDecimal amount;

  /**
   * 业务号;流水号/订单号
   */
  private String bizNo;
}
