package com.ly.titc.pms.member.dubbo.entity.request.record;

import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import lombok.Data;

/**
 * @Author：rui
 * @name：PageCheckinRecordRequest
 * @Date：2024-12-11 21:32
 * @Filename：PageCheckinRecordRequest
 */
@Data
public class PagePurchaseCardRecordReq extends BasePageReq {

    /**
     * 来源门店类型 1 集团  2 门店
     */
    private Integer masterType;

    /**
     * 具体的值 集团编码 、 门店编码
     */
    private String masterCode;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 开始日期
     */
    private String beginTime;

    /**
     * 结束日期
     */
    private String endTime;


}
