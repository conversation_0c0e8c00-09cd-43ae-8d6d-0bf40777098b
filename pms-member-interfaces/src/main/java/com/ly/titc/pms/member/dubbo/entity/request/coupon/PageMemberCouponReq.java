package com.ly.titc.pms.member.dubbo.entity.request.coupon;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import javax.validation.constraints.NotEmpty;

import com.ly.titc.pms.member.dubbo.enums.MemberCouponStateEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 分页查询会员优惠券请求体
 *
 * @classname PageMemberCouponReq
 * @descrition 分页查询会员优惠券请求体
 * <AUTHOR>
 * @since 2023/8/31 11:08
 */
@Data
@Accessors(chain = true)
public class PageMemberCouponReq extends BasePageReq {

  /**
   * 会员编号
   */
  @NotEmpty(message = "会员编号不能为空")
  private String memberNo;

  /**
   * 会员归属编号(会员号-会员归属编码)
   */
  private String memberAffiliationNo;

  /**
   * 会员优惠券状态
   */
  @LegalEnum(target = MemberCouponStateEnum.class, methodName = "getState", message = "会员优惠券状态非法")
  private Integer state;
}
