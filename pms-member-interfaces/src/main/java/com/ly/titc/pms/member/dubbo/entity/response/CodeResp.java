package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 13:49
 */
@Data
@Accessors(chain = true)
public class CodeResp {

    /**
     * 父级code
     */
    private String parentCode;

    /**
     * 父级名称
     */
    private String parentName;

    /**
     * code
     */
    private String code;

    /**
     * name
     */
    private String name;
}
