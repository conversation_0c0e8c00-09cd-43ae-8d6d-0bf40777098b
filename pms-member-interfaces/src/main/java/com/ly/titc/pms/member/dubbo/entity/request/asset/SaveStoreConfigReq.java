package com.ly.titc.pms.member.dubbo.entity.request.asset;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 会员资产系统配置请求参数
 *
 * <AUTHOR>
 * @date 2025/3/31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SaveStoreConfigReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 储值支付是否需要验证 (0-否, 1-是)
     */
    @NotNull(message =  "储值支付是否需要验证不能为空")
    private Integer isPayVerifyRequired;

    /**
     * 是否允许酒店修改验证设置 (仅集团层级有效)
     */
    @NotNull(message =  "是否允许酒店修改验证设置不能为空")
    private Integer allowPayVerifyModify;

    /**
     * 是否支持他卡付款 (0-否, 1-是)
     */
    @NotNull(message =  "是否支持他卡付款不能为空")
    private Integer isSupportOtherCard;

    /**
     * 是否允许酒店修改他卡付款设置 (仅集团层级有效)
     */
    @NotNull(message =  "是否允许酒店修改他卡付款设置不能为空")
    private Integer allowOtherCardModify;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
