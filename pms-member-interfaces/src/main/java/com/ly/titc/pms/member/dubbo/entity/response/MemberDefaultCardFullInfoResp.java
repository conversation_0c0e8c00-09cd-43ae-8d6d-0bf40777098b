package com.ly.titc.pms.member.dubbo.entity.response;

import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardPrivilegeConfigResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreAccountResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.CouponStatisticsResp;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 会员默认卡全量信息
 * 包含资产和权益信息
 */
@Data
@Accessors(chain = true)
public class MemberDefaultCardFullInfoResp extends MemberIdentityBaseInfoResp {

    /**
     * 会员权益信息
     */
    private List<MemberCardPrivilegeConfigResp> privileges;

    /**
     * 默认卡对应的权益信息(订单下单快照权益)
     */
    private List<MemberCardPrivilegeConfigResp> orderPrivileges;

    /**
     * 会员资产信息
     */
    private MemberStoreAccountResp storeAccount;

    /**
     * 会员积分信息
     */
    private MemberPointAccountResp pointAccount;

    /**
     * 会员优惠券信息
     */
    private CouponStatisticsResp couponStatisticsResp;



}
