package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 会员资产系统配置请求参数
 *
 * <AUTHOR>
 * @date 2025/3/31
 */
@Data
@Accessors(chain = true)
public class MemberStoreConfigResp {

    /**
     * 储值配置ID
     */
    private Long id;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 集团组code  集团code 门店code
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 储值支付是否需要验证 (0-否, 1-是)
     */
    private Integer isPayVerifyRequired;

    /**
     * 是否允许酒店修改验证设置 (仅集团层级有效)
     */
    private Integer allowPayVerifyModify;

    /**
     * 是否支持他卡付款 (0-否, 1-是)
     */
    private Integer isSupportOtherCard;

    /**
     * 是否允许酒店修改他卡付款设置 (仅集团层级有效)
     */
    private Integer allowOtherCardModify;
}
