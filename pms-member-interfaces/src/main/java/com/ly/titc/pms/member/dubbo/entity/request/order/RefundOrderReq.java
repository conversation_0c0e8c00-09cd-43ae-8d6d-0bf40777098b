package com.ly.titc.pms.member.dubbo.entity.request.order;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 订单退款
 *
 * <AUTHOR>
 * @date 2024/12/16 16:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RefundOrderReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员号
     */
    @NotBlank(message = "会员号不能为空")
    private String memberNo;

    /**
     * 会员订单号
     */
    @NotBlank(message = "会员订单号不能为空")
    private String memberOrderNo;

    /**
     * 退款金额（到分）
     */
    @NotNull(message = "退款金额不能为空")
    private BigDecimal refundAmount;

//    /**
//     * 退款原因
//     */
//    @NotBlank(message = "退款原因不能为空")
//    private String reason;

    /**
     * 平台渠道
     */
    @NotBlank(message = "平台渠道不能为空")
    @LegalEnum(target = PlatformChannelEnum.class, methodName = "getPlatformChannel", message = "平台渠道不合法")
    private String platformChannel;

    /**
     * 终端ID
     */
    private String termId;

    /**
     * 操作人
     */
    private String operator;

}
