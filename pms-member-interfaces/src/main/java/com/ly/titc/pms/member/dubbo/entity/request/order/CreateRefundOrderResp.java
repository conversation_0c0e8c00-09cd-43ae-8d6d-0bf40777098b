package com.ly.titc.pms.member.dubbo.entity.request.order;

import lombok.Data;

/**
 * 订单退款结果
 *
 * <AUTHOR>
 * @date 2024/12/16 16:54
 */
@Data
public class CreateRefundOrderResp {

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 退款单号
     */
    private String memberOrderPayTradeNo;

    /**
     * 收银台退款交易号
     */
    private String refundPayNo;

    /**
     * 支付产品
     */
    private String payProduct;

    /**
     * 渠道交易号
     */
    private String refundTransactionId;

    /**
     * 退款状态 1 退款中、2 退款成功、3 退款失败
     */
    private Integer tradeState;

    /**
     * 备注
     */
    private String failReason;

}
