package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 会员标签
 *
 * <AUTHOR>
 * @date 2024/11/6 10:47
 */
@Data
public class MemberProfileTagResp {

    /**
     * 会员标签编号
     */
    private Long tagNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 标签分类
     */
    private Integer tagType;

    /**
     * 标签分类
     */
    private String tagTypeDesc;

    /**
     * 打标分类: 1:手动标记 2:系统标记
     */
    private Integer markType;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;


}
