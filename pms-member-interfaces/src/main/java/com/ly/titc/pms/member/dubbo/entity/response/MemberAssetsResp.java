package com.ly.titc.pms.member.dubbo.entity.response;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname MemberAssetsResp
 * @descrition 会员资产返回体
 * @since 2023/8/16 10:16
 */
@Data
@Accessors(chain = true)
public class MemberAssetsResp {

  /**
   * 会员编号
   */
  private String memberNo;

  /**
   * 总金额
   */
  private BigDecimal totalAmount;

  /**
   * 当前可用金额
   */
  private BigDecimal availableAmount;

  /**
   * 充值金额
   */
  private BigDecimal rechargeAmount;

  /**
   * 赠送金额
   */
  private BigDecimal presentAmount;

  /**
   * 过期金额
   */
  private BigDecimal expiredAmount;

  /**
   * 冻结金额
   */
  private BigDecimal frozenPoint;

  /**
   * 会员充值信息集合
   */
  private List<MemberRechargeResp> memberRecharges;
}
