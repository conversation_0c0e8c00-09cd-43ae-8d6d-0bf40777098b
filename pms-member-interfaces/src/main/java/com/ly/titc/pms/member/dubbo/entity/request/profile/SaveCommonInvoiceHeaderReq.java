package com.ly.titc.pms.member.dubbo.entity.request.profile;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 保存常用发票抬头
 *
 * <AUTHOR>
 * @date 2024/11/6 11:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveCommonInvoiceHeaderReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员编号
     */
    @NotEmpty(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 发票抬头编号
     */
    private Long invoiceHeaderNo;

    /**
     * 发票类型；1:个人;2:企业;3:非企业性单位
     */
    @NotNull(message = "发票类型不能为空")
    private Long invoiceType;

    /**
     * 发票抬头
     */
    @NotBlank(message = "发票抬头不能为空")
    private String headerName;

    /**
     * 税号
     */
    @NotBlank(message = "税号不能为空")
    private String taxCode;

    /**
     * 是否需要增值税专用发票
     */
    private Integer needSpecialInvoice;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司电话
     */
    private String companyTel;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空")
    private Integer sort;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;
}
