package com.ly.titc.pms.member.dubbo.entity.request.record;

import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import lombok.Data;

/**
 * @Author：rui
 * @name：PageCheckinRecordRequest
 * @Date：2024-12-11 21:32
 * @Filename：PageCheckinRecordRequest
 */
@Data
public class PageCheckinRecordReq extends BasePageReq {

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 入住时间--开始
     */
    private String checkInBeginTime;

    /**
     * 入住时间-- 结束
     */
    private String checkInEndTime;

    /**
     * 离店时间-开始
     */
    private String checkOutBeginTime;

    /**
     * 离店时间--结束
     */
    private String checkOutEndTime;

}
