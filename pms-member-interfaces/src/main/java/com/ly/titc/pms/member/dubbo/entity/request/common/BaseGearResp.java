package com.ly.titc.pms.member.dubbo.entity.request.common;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-13 11:42
 */
@Data
public class BaseGearResp implements Serializable {

    private static final long serialVersionUID = 134245234521L;
    /**
     * 档位类型
     */
    private String gearType;


    private String gearCode;
    /**
     * 优惠list
     */
    private List<DiscountBenefitResp> benefitDtoList;

    /**
     * 档位描述
     */
    private String gearDesc;

    /**
     * 子档位
     */
    private List<BaseGearResp> childGears;
}
