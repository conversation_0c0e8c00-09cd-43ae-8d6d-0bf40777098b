package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 拉黑会员
 *
 * <AUTHOR>
 * @date 2025/2/7 16:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BlacklistMemberReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员编号
     */
    @NotBlank(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 场景适用范围 0 部分 1 全部
     */
    private Integer sceneScope;

    /**
     * 适用场景列表
     */
    private List<String> scenes;

    /**
     * 渠道适用范围 0 部分 1 全部
     */
    private Integer platformChannelScope;

    /**
     * 适用渠道列表
     */
    private List<String> platformChannels;

    /**
     * 来源类型 1、集团 2、门店
     */
    private Integer sourceType;

    /**
     * 来源（集团/门店）
     */
    private String source;

    /**
     * 原因
     */
    @NotBlank(message = "原因不能为空")
    private String reason;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;
}
