package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 16:30
 */
@Data
@Accessors(chain = true)
public class MemberPayOrderResp {
    /**
     * 会员订单号
     */
    private String memberOrderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员支付申请单号
     */
    private String memberOrderPayTradeNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员业务场景
     */
    private String memberScene;

    /**
     * 会员业务场景描述
     */
    private String memberSceneDesc;

    /**
     * 支付状态 1 处理中 2 成功，3失败，4 超时关闭
     */
    private Integer tradeState;

    /**
     * 入账状态  0. 未入账 1.无需入账  2.成功，3.失败
     */
    private Integer accountState;

    /**
     * 交易失败原因
     */
    private String tradeFailReason;

    /**
     * 入账状态描述
     */
    private String accountFailReason;

    /**
     * 支付厂商 FUIOU 富友 WX 微信 ZFB 支付宝 TALN 通联 UNP 银联
     */
    private String payVendor;

    /**
     /**
     * 支付渠道（对应页面显示字段为付款方式）
     * 示例：支付宝，微信，银行卡，信用授权，储值卡
     *WECHAT :微信 ALIPAY:支付宝 PosCARD：POS银行卡 UNIONPAY：银联二维码
     *CASH 现金  OfflineCreditAuthor 信用授权  OfflineBankCard 线下银行卡
     *fliggy 飞猪 MemberCard 会员储值卡
     * @see com.ly.titc.pms.account.dubbo.enums.pay.PayChannelEnum
     */
    private String payChannel;

    /**
     * 支付方式
     * 示例：wxPay 微信支付； aliPay 支付宝支付； unionpay
     * 银联支付； posSlotCard 国内卡刷卡； posWildcard 境外卡刷卡；
     * posWxPay pos微信支付； posAliPay pos支付宝支付； posUnionPay pos银联
     * fliggy 飞猪
     */
    private String payType;

    /**
     * 是否是线上支付 0 否 1 是
     */
    private Integer isOnlinePay;

    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 渠道交易号
     */
    private String transactionId;
    /**
     * 支付完成时间
     */
    private String payedTime;

    /**
     * 结算项code
     */
    private String itemCode;

    /**
     * 结算项名称
     */
    private String itemName;

    /**
     * 账单明细NO
     */
    private String accountItemNo;
    /**
     * 收银台交易号
     */
    private String onlinePayNo;
}
