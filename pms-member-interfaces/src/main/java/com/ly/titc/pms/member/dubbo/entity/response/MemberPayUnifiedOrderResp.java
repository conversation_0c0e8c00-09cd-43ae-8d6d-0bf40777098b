package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 16:30
 */
@Data
@Accessors(chain = true)
public class MemberPayUnifiedOrderResp {
    /**
     * 会员订单号
     */
    private String memberOrderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员支付申请单号
     */
    private String memberOrderPayTradeNo;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     *
     * 来源系统：会员-MEMBER
     */

    private String sourceSystem;

    /**
     * 业务类型：会员-MEMBER
     * （账单明细需要）
     */
    private String bizType;

    /**
     * 会员业务场景描述 （对应收银台的商品描述）
     */
    private String goodsDes;

    /**
     * 收银场景
     * MEMBER_REGISTER 会员注册 MEMBER_PURCHASE_CARD 购卡升级
     * MEMBER_RECHARGE 储值充值
     */
    private String cashierScene;



    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;



}
