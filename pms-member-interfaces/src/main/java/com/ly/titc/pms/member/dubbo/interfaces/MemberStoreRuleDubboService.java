package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.GetStoreConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.SaveStoreConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.DeleteStoreUsageReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.PageStoreUsageRuleReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.SaveStoreUsageConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.UpdateStoreUsageStateReq;
import com.ly.titc.pms.member.dubbo.entity.response.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员储值配置
 *
 * <AUTHOR>
 * @date 2025/1/23 16:50
 */
public interface MemberStoreRuleDubboService {

    /**
     * 保存会员储值配置
     *
     * @param request
     * @return
     */
    Response<Boolean> saveConfig(@Valid SaveStoreConfigReq request);

    /**
     * 查询会员储值配置
     *
     * @param request
     * @return
     */
    Response<MemberStoreConfigResp> getConfig(@Valid GetStoreConfigReq request);

    /**
     * 分页查询储值可用规则
     *
     * @param request
     * @return
     */
    Response<Pageable<MemberStoreUsageRuleResp>> pageRule(@Valid PageStoreUsageRuleReq request);

    /**
     * 保存储值可用规则
     *
     * @param request
     * @return
     */
    Response<List<SaveStoreUsageRuleResultResp>> saveRule(@Valid SaveStoreUsageConfigReq request);

    /**
     * 更新状态
     *
     * @param request
     * @return
     */
    Response<Boolean> updateState(@Valid UpdateStoreUsageStateReq request);

    /**
     * 删除可用规则
     *
     * @param request
     * @return
     */
    Response<Boolean> deleteRule(@Valid DeleteStoreUsageReq request);

    /**
     * 设置提醒
     *
     * @param request
     * @return
     */
    Response<List<SaveStoreUsageRuleResultResp>> remind(@Valid BlocBaseReq request);

    /**
     * 查询可用的储值规则
     *
     * @param request
     * @return
     */
    Response<List<MemberStoreAvailableUsageRuleResp>> listAvailableRule(@Valid BlocBaseReq request);

}
