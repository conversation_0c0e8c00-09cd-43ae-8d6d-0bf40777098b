package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-2-20 14:30
 */
@Data
@Accessors(chain = true)
public class FuzzyMatchMemberReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员名称或手机号
     */
    private String memberFuzzy;

    /**
     * 条目数
     */
    @Max(value = 2000, message = "最多只返回2000条")
    private int size =20;

}
