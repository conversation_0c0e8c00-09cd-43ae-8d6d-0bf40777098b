package com.ly.titc.pms.member.dubbo.enums;
/**
 * @classname MemberCouponStateEnum
 * @descrition 会员优惠券状态
 * <AUTHOR>
 * @since 2023/8/29 18:09
 */
public enum MemberCouponStateEnum {

  /**
   * 会员状态；0 已无效 1 正常 2 已使用
   */
  INVALID(0,"已无效"),
  VALID(1,"正常"),
  USED(2,"已使用"),
  ;

  private Integer state;
  private String desc;

  MemberCouponStateEnum(Integer state, String desc) {
    this.state = state;
    this.desc = desc;
  }

  public Integer getState() {
    return state;
  }

  public String getDesc() {
    return desc;
  }
}
