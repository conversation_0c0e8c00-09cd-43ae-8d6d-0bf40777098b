package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.card.*;
import com.ly.titc.pms.member.dubbo.entity.request.member.IssueMemberCardManualRequest;
import com.ly.titc.pms.member.dubbo.entity.request.member.RecycleMemberCardManualRequest;
import com.ly.titc.pms.member.dubbo.entity.response.IssueMemberCardManualResponse;
import com.ly.titc.pms.member.dubbo.entity.response.RecycleMemberCardManualResponse;
import com.ly.titc.pms.member.dubbo.entity.response.MemberDetailInfoResp;

import javax.validation.Valid;

/**
 * 会员卡dubbo服务
 *
 * <AUTHOR>
 * @date 2024/10/25 14:00
 */
public interface MemberCardDubboService {

    /**
     * 根据会员卡号查询会员号
     *
     * @param request
     * @return
     */
    Response<String> getMemberNo(@Valid GetByCardNoReq request);

    /**
     * 根据会员卡号查询会员信息
     *
     * @param request
     * @return
     */
    Response<MemberDetailInfoResp> getDetailByCardNo(@Valid GetByCardNoReq request);

    /**
     * 发放会员卡
     *
     * @param request
     * @return
     */
    Response<String> issueCard(@Valid IssueCardReq request);

    /**
     * 会员卡信息更新
     *
     * @param request
     * @return
     */
    Response<String> updateCardInfo(@Valid UpdateCardInfoReq request);

    /**
     * 会员卡等级变更
     *
     * @param request
     * @return
     */
    Response<String> updateCardLevel(@Valid UpdateCardLevelReq request);

    /**
     * 生成卡号
     *
     * @param request
     * @return
     */
    Response<String> generateCardNo(@Valid GenerateCardNoReq request);

    /**
     * 校验会员卡号是否存在
     *
     * @param request
     * @return
     */
    Response<Boolean> checkCardNoExist(@Valid CheckCardNoReq request);

    /**
     * 校验会员是否拥有会员卡
     *
     * @param request
     * @return
     */
    Response<Boolean> checkHasCard(@Valid CheckHasCardReq request);

    /**
     * 发放会员卡
     * @param request
     * @return
     */
    Response<IssueMemberCardManualResponse> issueMemberCard(@Valid IssueMemberCardManualRequest request);

    /**
     * 回收会员卡
     * @param request
     * @return
     */
    Response<RecycleMemberCardManualResponse> recycleMemberCard(@Valid RecycleMemberCardManualRequest request);
}
