package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：CalculateTypeEnum
 * @Date：2024-11-19 21:20
 * @Filename：CalculateTypeEnum
 */
public enum CalculateTypeEnum {
    // 计算方式计算方式 1 大于等于 2 大于 3 小于等于 4 小于
    GT_EQ(1, "大于等于"),
    GT(2, "大于"),
    LT_EQ(3, "小于等于"),
    LT(4, "小于")
    ;

    private Integer type;

    private String name;

    public String getName() {
        return name;
    }

    public Integer getType() {
        return type;
    }

    public static String getNameByType(Integer type) {
        for (CalculateTypeEnum value : CalculateTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getName();
            }
        }
        return null;
    }

    public static Integer getTypeByName(String name) {
        for (CalculateTypeEnum value : CalculateTypeEnum.values()) {
            if (value.getName().equals(name)) {
                return value.getType();
            }
        }
        return null;
    }

    CalculateTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static CalculateTypeEnum getByType(Integer type) {
        for (CalculateTypeEnum value : CalculateTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
