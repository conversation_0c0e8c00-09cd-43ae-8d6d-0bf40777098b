package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：CycleTypeEnum
 * @Date：2024-11-19 21:10
 * @Filename：CycleTypeEnum
 */
public enum CycleTypeEnum {
        SINCE_REGISTER(1, "会员注册起"),
        SINCE_UPGRADE_OR_DOWN(2, "上次升降级起"),
    ;

    private Integer type;

    private String name;


    CycleTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getNameByType(Integer type) {
        for (CycleTypeEnum cycleTypeEnum : CycleTypeEnum.values()) {
            if (cycleTypeEnum.getType().equals(type)) {
                return cycleTypeEnum.getName();
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

}
