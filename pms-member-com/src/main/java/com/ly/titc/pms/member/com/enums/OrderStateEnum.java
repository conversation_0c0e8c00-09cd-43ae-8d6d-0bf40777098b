package com.ly.titc.pms.member.com.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员业务场景
 * @since 2024-11-26 11:56
 */
@AllArgsConstructor
@Getter
public enum OrderStateEnum {
    PAYING(1, "支付中"),
    PAID(2, "支付成功业务处理成功"),
    FAIL(3, "支付失败"),

    CLOSE(4, "交易关闭"),

    PAID_BUSINESS_FAIL(5, "支付成功业务处理失败"),

    SOME_REFUND(6, "部分退款"),
    REFUND(7, "全额退款"),
    ;

    private final Integer state;

    private final String desc;
}
