package com.ly.titc.pms.member.com.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName: CookieUtil
 * @Description:
 * @since 2022/11/17 20:24
 */
@Slf4j
public class CookieUtil {
    private static final int COOKIE_MAX_AGE = 7*24*60*60;

    /**
     * 根据cookie名称获取cookie值
     * @param cookieName
     * @param servletRequest
     * @return
     */
    public static String getCookie(String cookieName, HttpServletRequest servletRequest){
        Cookie[] cookies = servletRequest.getCookies();
        if(null != cookies){
            Cookie cookie = Arrays.stream(cookies).filter(e -> e.getName().equalsIgnoreCase(cookieName)).findFirst().orElse(null);
            return null == cookie ? "" : cookie.getValue();
        }
        return "";
    }

    /**
     * 设置cookie
     * @param cookieName
     * @param cookieValue
     * @param servletResponse
     * @param specialDomain
     */
    public static void setCookie(String cookieName, String cookieValue, HttpServletResponse servletResponse, String specialDomain){
        Cookie cookie = new Cookie(cookieName, cookieValue);
        cookie.setPath("/");
        if(!StringUtils.isEmpty(specialDomain)){
            cookie.setDomain(specialDomain);
        }
        servletResponse.addCookie(cookie);
    }

    /**
     * 删除cookie
     * @param cookieName
     * @param servletResponse
     */
    public static void delCookie(String cookieName, HttpServletResponse servletResponse, String specialDomain){
        Cookie cookie = new Cookie(cookieName, null);
        cookie.setPath("/");
        if(!StringUtils.isEmpty(specialDomain)){
            cookie.setDomain(specialDomain);
        }
        cookie.setMaxAge(0);
        servletResponse.addCookie(cookie);
    }

    /**
     * 删除cookie
     * @param cookieName
     * @param servletResponse
     */
    public static void delCookie(String cookieName, HttpServletResponse servletResponse){
        delCookie(cookieName, servletResponse, null);
    }

    /**
     * 设置cookie
     *  @param cookieName
     * @param cookieValue
     * @param servletResponse
     * @param specialDomain
     * @param trackingId
     */
    public static void setHeaderCookie(String cookieName, String cookieValue, HttpServletResponse servletResponse, String specialDomain, String trackingId) {
        log.info("设置cookie, cookieName:{}, cookieValue:{}, domain:{}, trackingId:{}", cookieName, cookieValue, specialDomain, trackingId);
//        specialDomain = null == specialDomain ? "" : specialDomain;
//        Boolean secure = true;
//        String env = AppProfile.getEnvironment();
////        // 非线上环境不使用secure
////        if(StringUtils.isNotBlank(env) && !Constant.ENV_PRODUCT.equalsIgnoreCase(env)) {
////            secure = false;
////        }
//
//        ResponseCookie cookie = ResponseCookie.from(cookieName, cookieValue) // key & value
////        .httpOnly(true)		// 禁止js读取
//                .secure(secure)		    // 在http下也传输
//                .domain(specialDomain)// 域名
//                .path("/")			// path
//                .sameSite("None")
//                .build()
//                ;
//
//        servletResponse.addHeader(HttpHeaders.SET_COOKIE, cookie.toString());
    }
}
