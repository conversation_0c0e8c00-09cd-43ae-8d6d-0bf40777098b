package com.ly.titc.pms.member.com.annotation;


import com.ly.titc.pms.member.com.enums.ActionEnum;
import com.ly.titc.pms.member.com.enums.ModuleEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Author：rui
 * @name：OperationLog
 * @Date：2023-11-22 17:42
 * @Filename：OperationLog
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface OperationLog {
    /**
     * 日志操作模块：
     */
    ModuleEnum module() default ModuleEnum.MEMBER_MANAGE;


    /**
     * 日志操作行为：
     */
    ActionEnum action() default ActionEnum.ADD;

    /**
     * 操作内容
     *
     * @return
     */
    String operateRecord() default "";

}
