package com.ly.titc.pms.member.com.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.com.function.PageFunction;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/6 14:42
 */
public class PageUtil {

    /**
     * 分页遍历的方式获取所有数据
     *
     * @param queryListFunc
     * @param <T>
     * @return
     */
    public static <T> List<T> queryAll(PageFunction<T> queryListFunc, Integer limit) {
        List<T> list = new ArrayList<>();
        int pageIndex = 1;
        int pageLimit = limit != null ? limit : Constant.TEN_THOUSAND;
        while (true) {
            IPage<T> page = queryListFunc.pageData(pageIndex++, pageLimit);
            if (CollectionUtils.isEmpty(page.getRecords())) {
                break;
            }
            list.addAll(page.getRecords());
            if (page.getRecords().size() < pageLimit){
                break;
            }
        }
        return list;
    }

}
