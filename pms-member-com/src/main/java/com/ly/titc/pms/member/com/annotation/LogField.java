package com.ly.titc.pms.member.com.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface LogField {

    /**
     * 名称
     *
     * @return
     */
    String name() default "";

    /**
     * 字段枚举值
     */
    String fieldKeyValue() default "";

}
