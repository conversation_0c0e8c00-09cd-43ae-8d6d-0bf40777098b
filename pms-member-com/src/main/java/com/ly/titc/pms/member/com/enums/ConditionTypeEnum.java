package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：ConditionTypeEnum
 * @Date：2024-11-19 21:18
 * @Filename：ConditionTypeEnum
 */
public enum ConditionTypeEnum {
     // 条件类型： 0-入住次数 1-入住房晚 2-充值金额（不含赠送) 3-消费金额 4-积分 5-平均房费 6-未入住天数 7-注册天数
    IN_COUNT(0, "入住次数"),
    IN_NIGHT(1, "入住房晚"),
    RECHARGE_AMOUNT(2, "充值金额（不含赠送）"),
    CONSUME_AMOUNT(3, "消费金额"),
    POINT(4, "积分"),
    AVERAGE_ROOM_FEE(5, "平均房费"),
    UNCHECKED_DAYS(6, "未入住天数"),
    REGISTER_DAYS(7, "注册天数")
    ;

    private Integer type;

    private String name;

    public String getName() {
        return name;
    }

    public Integer getType() {
        return type;
    }

    public static String getNameByType(Integer type) {
        for (ConditionTypeEnum item : ConditionTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return item.getName();
            }
        }
        return null;
    }

    public static Integer getTypeByName(String name) {
        for (ConditionTypeEnum item : ConditionTypeEnum.values()) {
            if (item.getName().equals(name)) {
                return item.getType();
            }
        }
        return null;
    }

    public static ConditionTypeEnum getByType(Integer type) {
        for (ConditionTypeEnum item : ConditionTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return item;
            }
        }
        return null;
    }


    ConditionTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

}
