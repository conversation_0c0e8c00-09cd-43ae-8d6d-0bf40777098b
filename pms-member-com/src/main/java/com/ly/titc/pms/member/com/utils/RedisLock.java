package com.ly.titc.pms.member.com.utils;

import com.ly.titc.pms.member.com.spring.SpringContextHolder;
import com.ly.titc.common.factory.RedisFactory;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2023-5-11 14:07
 */
@Slf4j
public class RedisLock {

    private static final RedisFactory redisFactory = SpringContextHolder.getBean(RedisFactory.class);

    public static boolean retryLock(String lockKey, long seconds, Integer retryTime, long sleepTime) {
        Boolean flag = false;
        for (int i = 0; i < retryTime; i++) {
            flag = redisFactory.setNx(lockKey, seconds, Thread.currentThread().getId() + "value");
            if (!flag) {
                if (i == retryTime - 1) {
                    log.info("抢锁失败，本次操作失败");
                    return false;
                }
                log.info("有任务正在持有锁，lockKey:{} 休眠后：{}ms后，开始底【{}】次尝试", lockKey, i * sleepTime, i);
                try {
                    Thread.sleep(i * sleepTime);
                } catch (InterruptedException e) {
                    //
                }
                continue;
            }
            break;
        }
        return flag;
    }

    /**
     * 默认的加锁方法
     *
     * @param lockKey
     * @return
     */
    public static boolean retryLock(String lockKey) {
        Boolean flag = false;
        // 重试次数默认10次
        int retryTime = 10;
        // 默认10s
        int seconds = 10;
        for (int i = 0; i < retryTime; i++) {
            flag = redisFactory.setNx(lockKey, seconds, "1");
            if (!flag) {
                if (i == retryTime - 1) {
                    log.info("抢锁失败，本次操作失败");
                    return false;
                }
                log.info("有任务正在持有锁，lockKey:{} 休眠后：{}ms后，开始底【{}】次尝试", lockKey, 10, i);
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    //
                }
                continue;
            }
            break;
        }
        return flag;
    }


    public static void unLock(String lockKey) {
        redisFactory.del(lockKey);
    }


}
