package com.ly.titc.pms.member.com.utils;

import cn.hutool.core.date.format.FastDateFormat;

import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 会员号管理类
 *
 * <AUTHOR>
 * @classname GeneratorMemberNoManager
 * @descrition 会员号管理类
 * @since 2023/5/17 17:27
 */
public class GeneratorMemberNoUtil {

    private static final FastDateFormat pattern = FastDateFormat.getInstance("yyMMdd");

    /**
     * 会员号生成策略
     *
     * @return
     */
    public static String generatorMemberNo() {

        StringBuilder builder = new StringBuilder();
        // 当前时间
        builder.append(pattern.format(Instant.now().toEpochMilli()));
        Integer random = ThreadLocalRandom.current().nextInt(0, 9);
        builder.append(random);
        int hashCode = Math.abs(UUID.randomUUID().toString().replace("-", "").hashCode());
        // hashcode
        builder.append(String.format("%010d", hashCode));
        return builder.toString();
    }
}
