package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：MemberRegisterCheckEnum
 * @Date：2024-11-25 16:32
 * @Filename：MemberRegisterCheckEnum
 */
public enum MemberRegisterCheckEnum {
    // 1 验证码 2 卡费 3 手机号 4 证件号 5 黑名单 6 密码
    VERIFY_CODE(1),
    CARD_FEE(2),
    MOBILE(3),
    ID_NO(4),
    BLACK_LIST(5),
    PASSWORD(6),
    CUSTOMER(7),
    MEMBER_CARD(8),
    ;

    private Integer action;

    MemberRegisterCheckEnum(Integer action) {
        this.action = action;
    }

    public Integer getAction() {
        return action;
    }

    public static MemberRegisterCheckEnum getEnum(Integer action) {
        for (MemberRegisterCheckEnum value : MemberRegisterCheckEnum.values()) {
            if (value.getAction().equals(action)) {
                return value;
            }
        }
        return null;
    }

    public static Integer getAction(MemberRegisterCheckEnum actionEnum) {
        return actionEnum.getAction();
    }
}
