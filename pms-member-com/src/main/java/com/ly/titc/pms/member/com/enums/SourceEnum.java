package com.ly.titc.pms.member.com.enums;

/**
 * 来源
 * @Author：rui
 * @name：SourceEnum
 * @Date：2024-12-4 17:24
 * @Filename：SourceEnum
 */
public enum SourceEnum {

    PUB("PUB","酒馆组"),
    BLOC("BLOC","集团"),
    HOTEL("HOTEL","门店"),

        ;

    SourceEnum (String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    private String code;

    private String desc;

    public static SourceEnum getByCode(String code) {
        for (SourceEnum sourceEnum : SourceEnum.values()) {
            if (sourceEnum.getCode().equals(code)) {
                return sourceEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
