package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：SuccessfulPerformTypeEnum
 * @Date：2024-11-19 21:08
 * @Filename：SuccessfulPerformTypeEnum
 */
public enum SuccessfulPerformTypeEnum {

    ALL("ALL", "全部规则满足"),
    ANY("ANY", "任一规则满足"),
    ;

    private String type;

    private String desc;

    SuccessfulPerformTypeEnum(String code, String desc) {
        this.type = code;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByType(String type) {
        for (SuccessfulPerformTypeEnum value : SuccessfulPerformTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
