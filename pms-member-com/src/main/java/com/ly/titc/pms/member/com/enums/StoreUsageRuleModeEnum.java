package com.ly.titc.pms.member.com.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-14 10:20
 */
@Getter
@AllArgsConstructor
public enum StoreUsageRuleModeEnum {

    SINGLE_STORE("SINGLE", "唯一"),
    MULTI_STORE("MULTI", "混合"),;

    private String code;

    private String desc;

    public static StoreUsageRuleModeEnum getByCode(String code) {
        for (StoreUsageRuleModeEnum storeUsageRuleModeEnum : StoreUsageRuleModeEnum.values()) {
            if (storeUsageRuleModeEnum.getCode().equals(code)) {
                return storeUsageRuleModeEnum;
            }
        }
        return null;
    }
}
