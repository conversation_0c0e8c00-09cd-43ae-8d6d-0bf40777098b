package com.ly.titc.pms.member.com.converter;

import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.constants.DateConstant;
import com.ly.titc.common.util.LocalDateTimeUtil;
import com.ly.titc.common.util.LocalDateUtil;
import org.mapstruct.Named;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @classname BaseConvert
 * @descrition
 * @since 2020/5/6 15:41
 */
public interface BaseConverter {

    /**
     * format timestamp to string
     *
     * @param timestamp
     * @return
     */
    @Named("formatTimestamp")
    default String formatTimestamp(Timestamp timestamp) {
        if (timestamp == null) {
            return null;
        }
        return LocalDateTimeUtil.formatByNormalDateTime(timestamp.toLocalDateTime());
    }


    @Named("string2Timestamp")
    default Timestamp string2Timestamp(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }
        return new Timestamp(LocalDateTimeUtil.parseByNormalDateTimeToMilli(dateStr));
    }

    @Named("localDatetime2Timestamp")
    default Timestamp localDatetime2Timestamp(LocalDateTime localDateTime) {

        if (Objects.isNull(localDateTime)) {
            return null;
        }
        return new Timestamp(localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
    }


    default String localDatetime2String(LocalDateTime localDateTime) {

        if (Objects.isNull(localDateTime)) {
            return null;
        }
        return LocalDateTimeUtil.formatByNormalDateTime(localDateTime);
    }


    default String localDate2String(LocalDate localDate) {

        if (Objects.isNull(localDate)) {
            return null;
        }
        return LocalDateUtil.formatByNormalDate(localDate);
    }

    /**
     * @param str
     * @return
     */
    @Named("defaultString")
    default String defaultString(String str) {
        return StringUtils.isEmpty(str) ? Constant.STRING_EMPTY : str;
    }

    /**
     * 单个变list
     *
     * @param str
     * @return
     */
    @Named("defaultString")
    default List<String> singleToList(String str) {
        return Collections.singletonList(str);
    }
    @Named("localDatetime2Str")
    default String localDatetime2Str(LocalDateTime str) {
        return LocalDateTimeUtil.formatByNormalDateTime(str);
    }


    @Named("dateTimeStr2DateStr")
    default String dateTimeStr2DateStr(String dateTimeStr) {
        if (StringUtils.isEmpty(dateTimeStr)) {
            return null;
        }
        LocalDateTime localDateTime = LocalDateTime.parse(dateTimeStr, DateConstant.NORM_DATE_FORMATTER);
        return localDateTime.format(DateConstant.NORM_DATETIME_FORMATTER);
    }

    @Named("timestampToString")
    default String timestampToString(Timestamp timestamp) {
        if (Objects.isNull(timestamp)) {
            return "";
        }
        // 将Timestamp转换为Instant
        LocalDateTime localDateTime = timestamp.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        return LocalDateTimeUtil.formatByNormalDateTime(localDateTime);
    }
}
