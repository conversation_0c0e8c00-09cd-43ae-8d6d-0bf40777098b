package com.ly.titc.pms.member.com.utils;

import cn.hutool.core.util.IdUtil;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-7-10 12:53
 */
public class GenerateUtils {

    public  static  String generateOrderNO(String scene) {
        String orderNo = "";
        if(scene.equals(MemberSceneEnum.REGISTER.getScene())){
            orderNo=  generate("MRG");
        } else if(scene.equals(MemberSceneEnum.RECHARGE.getScene())){
            orderNo =   generate("MRC");
        } else if(scene.equals(MemberSceneEnum.PURCHASECARD.getScene())){
            orderNo=  generate("MPU");
        }else{
            orderNo = generate("MO");
        }
        return orderNo;
    }

    public  static String generateRefundOrderNo() {
        return generate("M");
    }


    public static String generate() {
       return generate(null);
    }

    public static String generate(String prefix) {
        long id = IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId();
        String no = String.valueOf(id);
        if (StringUtils.isNotBlank(prefix)) {
            return prefix + no;
        }
        return no;
    }

    public static String generatePayNo(String memberOrderNo,Integer size){
        return  memberOrderNo+"_"+size+1;
    }
}
