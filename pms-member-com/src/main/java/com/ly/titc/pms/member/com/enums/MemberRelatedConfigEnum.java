package com.ly.titc.pms.member.com.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：rui
 * @name：MemberRelatedConfigEnum
 * @Date：2024-12-11 13:52
 * @Filename：MemberRelatedConfigEnum
 */
@Getter
@AllArgsConstructor
public enum MemberRelatedConfigEnum {

    // 0 注册 1 手机验证设置 2 列表显示 3 列表快捷操作

    REGISTER(0, "注册"),
    PHONE_VERIFY(1, "手机验证设置"),
    LIST_SHOW(2, "列表显示"),
    LIST_QUICK_OPERATION(3, "列表快捷操作");

    private final Integer code;
    private final String desc;

    public static String getDesc(Integer code) {
        for (MemberRelatedConfigEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }
}
