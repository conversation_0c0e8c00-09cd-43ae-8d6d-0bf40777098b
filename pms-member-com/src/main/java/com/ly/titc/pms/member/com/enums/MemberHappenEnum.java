package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：MemberEventHappenEnum
 * @Date：2024-12-14 23:11
 * @Filename：MemberEventHappenEnum
 */
public enum MemberHappenEnum {
    // 0 积分 1 消费 2 充值 3 入住
    INTEGRAL(0, "积分"),
    CONSUME(1, "消费金额"),
    RECHARGE(2, "充值金额"),
    CHECK_IN(3, "入住")

    ;

    private Integer code;

    private String desc;

    MemberHappenEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
