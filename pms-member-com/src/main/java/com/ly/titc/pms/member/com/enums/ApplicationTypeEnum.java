package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：ApplicationTypeEnum
 * @Date：2024-11-28 11:07
 * @Filename：ApplicationTypeEnum
 */
public enum ApplicationTypeEnum {

    //适用类型 1 集团 2 品牌 3 门店
    GROUP(1, "集团"),
    BRAND(2, "品牌"),
    HOTEL(3, "门店");

    private Integer type;
    private String name;

    ApplicationTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static ApplicationTypeEnum getByType(Integer type) {
        for (ApplicationTypeEnum value : ApplicationTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public Integer getType() {
        return type;
    }
}
