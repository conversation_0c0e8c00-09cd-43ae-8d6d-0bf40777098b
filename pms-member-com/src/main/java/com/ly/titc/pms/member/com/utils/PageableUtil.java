package com.ly.titc.pms.member.com.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.entity.Pageable;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/26 14:37
 */
public class PageableUtil {
    public static <V> Pageable<V> convert(Pageable<?> pageable, List<V> list) {
        return new Pageable(pageable.getCurrPage(), pageable.getTotalPage(), pageable.getTotal(), list);
    }

    public static <V> Pageable<V> convert(Page<?> page, List<V> list) {
        return new Pageable((int)page.getCurrent(), (int)page.getPages(), (int)page.getTotal(), list);
    }


    /**
     * 将ipage转换成Pageable
     * @param iPage
     * @param converter
     * @return
     * @param <T>
     * @param <R>
     */
    public static <T,R> Pageable<R> convert(IPage<T> iPage , Function<T, R> converter) {
        Pageable<R> page = new Pageable<>();
        page.setTotalPage((int) iPage.getPages())
                .setTotal((int) iPage.getTotal())
                .setCurrPage((int) iPage.getCurrent());
        if (CollectionUtils.isEmpty(iPage.getRecords())) {
            page.setDatas(Collections.emptyList());
        } else {
            page.setDatas(iPage.getRecords().stream().map(converter).collect(Collectors.toList()));
        }
        return page;
    }

    /**
     * 将ipage转换成Pageable
     * @param iPage
     * @param converter
     * @return
     * @param <T>
     * @param <R>
     */
    public static <T,R> Pageable<R> convert(IPage<T> iPage , List<R> list) {
        Pageable<R> page = new Pageable<>();
        page.setTotalPage((int) iPage.getPages())
                .setTotal((int) iPage.getTotal())
                .setCurrPage((int) iPage.getCurrent());
        if (CollectionUtils.isEmpty(iPage.getRecords())) {
            page.setDatas(Collections.emptyList());
        } else {
            page.setDatas(list);
        }
        return page;
    }

    /**
     * 将pageable转换成另一个pageable
     * @param pageable
     * @param converter
     * @return
     * @param <T>
     * @param <R>
     */
    public static <T,R> Pageable<R> convert(Pageable<T> pageable , Function<T, R> converter) {
        Pageable<R> page = new Pageable<>();
        page.setTotalPage(pageable.getTotalPage())
                .setTotal(pageable.getTotal())
                .setCurrPage(pageable.getCurrPage());
        if (CollectionUtils.isEmpty(pageable.getDatas())) {
            page.setDatas(Collections.emptyList());
        } else {
            page.setDatas(pageable.getDatas().stream().map(converter).collect(Collectors.toList()));
        }
        return page;
    }


    /**
     * 手动分页
     * @param list
     * @param pageSize
     * @param currPage
     * @return
     * @param <T>
     */
    public static <T> Pageable<T> toPage(List<T> list, Integer pageSize, Integer currPage) {
        if (currPage == 0) {
            currPage = 1;
        }

        int startItem = (currPage - 1) * pageSize;

        if (CollectionUtils.isEmpty(list)) {
            return Pageable.empty();
        }

        if (list.size() < startItem) {
            return Pageable.empty();
        }

        int toIndex = Math.min(startItem + pageSize, list.size());
        List<T> paginatedList = list.subList(startItem, toIndex);
        Integer totalPage = Pageable.getTotalPage(pageSize, list.size());
        Pageable<T> pageable = new Pageable<>();
        pageable.setDatas(paginatedList);
        pageable.setTotal(list.size());
        pageable.setTotalPage(totalPage);
        pageable.setCurrPage(currPage);
        return pageable;
    }


}
