package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：MemberTagEnum
 * @Date：2024-11-18 16:09
 * @Filename：MemberTagEnum
 */
public enum MemberTagEnum {
    CUSTOMER(0, "客户标签"),
    ROOM_PREFERENCE(1, "客房偏好"),
    INTERESTS(2, "兴趣爱好"),
    DINING_PREFERENCE(3, "餐饮喜好"),
    OTHERS(4, "其他标签");


    ;

    private String desc;

    private Integer type;

    public String getDesc() {
        return desc;
    }

    public Integer getType() {
        return type;
    }

    private MemberTagEnum(Integer type, String desc) {
        this.desc = desc;
        this.type = type;
    }

    public static  String getDescByType(Integer type) {
        for (MemberTagEnum memberTagEnum : MemberTagEnum.values()) {
            if (memberTagEnum.getType().equals(type)) {
                return memberTagEnum.getDesc();
            }
        }
        return null;
    }

}
