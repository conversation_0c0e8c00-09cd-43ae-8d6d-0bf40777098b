package com.ly.titc.pms.member.com.utils;

import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2024/5/28 19:29
 */
public class WorkerUtil {

    /**
     * 获取数据中心ID（机器ID）
     *
     * @return 采用本地机器名称取32的模（降低分布式部署工作ID相同）
     */
    public static long getDataCenterId() {
        ThreadLocalRandom localRandom = ThreadLocalRandom.current();
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            String hostName = localHost.getHostName();
            List<Integer> tenNums = string2Integer(hostName);
            if (CollectionUtils.isEmpty(tenNums)) {
                return localRandom.nextInt(0,31);
            }
            return num2Model(tenNums);
        } catch (UnknownHostException e) {
            return localRandom.nextInt(0, 31);
        }
    }


    /**
     * 获取工作工厂ID
     *
     * @return 采用本地IP地址取32的模（降低分布式部署工作ID相同）
     */
    public static long getWorkId() {
        ThreadLocalRandom localRandom = ThreadLocalRandom.current();
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            String ip = localHost.getHostAddress();
            List<Integer> tenNums = string2Integer(ip);
            if (CollectionUtils.isEmpty(tenNums)) {
                // IP为空，则采用随机数指定工作ID
                return localRandom.nextInt(0,31);
            }
            return num2Model(tenNums);
        } catch (UnknownHostException e) {
            // ip 获取失败，则采用随机数指定工作ID
            return localRandom.nextInt(0,31);
        }
    }

    /**
     * 字符串转成10进制的数字集合
     *
     * @param str 字符串
     * @return 10进制的数字集合（这里的是将字符转成对应的ASICC码）
     */
    private static List<Integer> string2Integer(String str) {
        if (StringUtils.isEmpty(str)) {
            return Collections.emptyList();
        }
        List<Integer> integers = Lists.newArrayListWithCapacity(str.length());
        for (int i = 0; i < str.length(); i++) {
            integers.add(Integer.valueOf(Integer.toString(str.charAt(i), 10)));
        }
        return integers;
    }

    /**
     * 求和取模32
     *
     * @param nums 数字集合
     * @return long类型
     */
    private static long num2Model(List<Integer> nums) {
        ThreadLocalRandom localRandom = ThreadLocalRandom.current();
        if (CollectionUtils.isEmpty(nums)) {
            return localRandom.nextInt(0,31);
        }
        int sums = 0;
        for (Integer num : nums) {
            sums += num;
        }
        return sums % 32;
    }

}
