package com.ly.titc.pms.member.com.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * @Description:
 * @Author: xueli.han
 * @Date: 2024/7/23
 */

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface OperationBatchRecordsLog {
    /**
     * 操作内容
     *
     * @return
     */
    String operateRecord() default "";

}
