package com.ly.titc.pms.member.com.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：rui
 * @name：BlacklistSceneEnum
 * @Date：2024-12-10 17:19
 * @Filename：BlacklistSceneEnum
 */
@Getter
@AllArgsConstructor
public enum BlacklistSceneEnum {
    // 0 禁止预订入住  1 禁止会员操作  2 禁止参与营销
    FORBID_BOOKING("0", "禁止预订入住"),
    FORBID_OPERATION("1", "禁止会员操作"),
    FORBID_MARKETING("2", "禁止参与营销"),
    ;
    private String type;

    private String desc;

    public static BlacklistSceneEnum getByType(String type) {
        for (BlacklistSceneEnum value : BlacklistSceneEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    public static String getDescByType(String type) {
        for (BlacklistSceneEnum value : BlacklistSceneEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
