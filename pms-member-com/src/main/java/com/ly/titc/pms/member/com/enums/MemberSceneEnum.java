package com.ly.titc.pms.member.com.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员业务场景
 * @since 2024-11-26 11:56
 */
@AllArgsConstructor
@Getter
public enum MemberSceneEnum {
    REGISTER("MEMBER_REGISTER", "会员注册","MEMBER_PURCHASE_CARD","购卡升级"),
    PURCHASECARD("MEMBER_PURCHASE_CARD", "会员购卡","MEMBER_PURCHASE_CARD","购卡升级"),
    RECHARGE("MEMBER_RECHARGE", "会员充值","MEMBER_RECHARGE","储值充值"),
    UPGRADE("MEMBER_UPGRADE", "会员升级","MEMBER_PURCHASE_CARD","购卡升级"),;


    private final String scene;

    private final String desc;

    private final String cashierScene;

    private final String cashierSceneDesc;

    public static  String getCashierScene(String scene){
        for(MemberSceneEnum memberSceneEnum: MemberSceneEnum.values()){
            if(memberSceneEnum.getScene().equals(scene)){
                return memberSceneEnum.getCashierScene();
            }
        }
        return null;
    }

    public static  String getDesc(String scene){
        for(MemberSceneEnum memberSceneEnum: MemberSceneEnum.values()){
            if(memberSceneEnum.getScene().equals(scene)){
                return memberSceneEnum.getDesc();
            }
        }
        return null;
    }
}
