package com.ly.titc.pms.member.com.utils;

import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

public class TokenUtils {

    public static String ACCESS_TOKEN = "x-access-titc-c-token";

    /**
     *  获取新服务token
     * @param request
     * @return
     */
    public static String getNewAccessToken(HttpServletRequest request) {
        String accessToken = request.getHeader(ACCESS_TOKEN);
        if (StringUtils.isEmpty(accessToken)){
            accessToken = CookieUtil.getCookie(ACCESS_TOKEN, request);
        }
        return accessToken;
    }
}
