package com.ly.titc.pms.member.com.enums;

import java.util.Arrays;

/**
 * @Author：rui
 * @name：ModuleEnum
 * @Date：2024-11-12 17:03
 * @Filename：ModuleEnum
 */
public enum ModuleEnum {
    MEMBER_MANAGE(1,1,"会员管理"),
    MEMBER_LEVEL_MANEGE(2,2,"会员等级管理"),
    MEMBER_RULE_MANAGE(3,3,"自定升降级规则"),
    MEMBER_CARD_MANAGE(4,4,"会员卡管理"),
    MEMBER_PRIVILEGE_MANAGE(5,5,"权益列表"),
    TAG_MANAGE(6,6,"标签管理")

    ;

    private int code;
    private String desc;
    private int sort;

    ModuleEnum(int code, int sort, String desc) {
        this.code = code;
        this.sort = sort;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public static ModuleEnum getByCode(Integer code) {
        return null == code ? null : Arrays.stream(ModuleEnum.values()).filter(e -> e.getCode() == code)
                .findFirst().orElse(null);
    }
}
