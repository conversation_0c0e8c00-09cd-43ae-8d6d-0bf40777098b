package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：TagEnum
 * @Date：2024-11-19 22:47
 * @Filename：TagEnum
 */
public enum TagEnum {
    // 0 家、1公司、2 学校、3 亲人、4 朋友、5客户、 6同事、7 同学

    HOME(0, "家"),
    COMPANY(1, "公司"),
    SCHOOL(2, "学校"),
    RELATIVE(3, "亲人"),
    FRIEND(4, "朋友"),
    CUSTOMER(5, "客户"),
    COLLEAGUE(6, "同事"),
    STUDENT(7, "同学");

    private Integer type;

    private String name;

    TagEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(Integer type) {
        for (TagEnum tagEnum : TagEnum.values()) {
            if (tagEnum.getType().equals(type)) {
                return tagEnum.getName();
            }
        }
        return null;
    }

    public static Integer getTypeByName(String name) {
        for (TagEnum tagEnum : TagEnum.values()) {
            if (tagEnum.getName().equals(name)) {
                return tagEnum.getType();
            }
        }
        return null;
    }

}
