package com.ly.titc.pms.member.com.utils;

import com.ly.titc.common.config.ConfigCenter;
import com.ly.titc.common.enums.MasterTypeEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * 配置中心
 *
 * <AUTHOR>
 * @date 2024/12/9 15:04
 */
public class ConfigCenterUtil {

    /**
     * 通过集团获取归属
     *
     * @param blocCode
     * @return
     */
    public static TwoTuple<Integer, String> getMasterByBloc(String blocCode){
        try {
            String value = ConfigCenter.get("titc.java.pms.ecrm.job", "E_LONG_BLOC");
            if (StringUtils.isEmpty(value)) {
                return new TwoTuple<>(MasterTypeEnum.BLOC.getType(), blocCode);
            }
            Optional<String> optional = Arrays.stream(value.split(",")).filter(code -> code.equals(blocCode)).findFirst();

            return optional.isPresent() ? new TwoTuple<>(MasterTypeEnum.CLUB.getType(), "CLUB")
                    : new TwoTuple<>(MasterTypeEnum.BLOC.getType(), blocCode);
        } catch (Exception e) {
            throw new ServiceException(RespCodeEnum.CODE_3000);
        }
    }

}
