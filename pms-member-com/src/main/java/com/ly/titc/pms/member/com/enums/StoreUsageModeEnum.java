package com.ly.titc.pms.member.com.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-14 10:20
 */
@Getter
@AllArgsConstructor
public enum StoreUsageModeEnum {
    ALLOCATE(1, "指定可用"),
    ALONE(2, "仅充值方可用"),
    ALL(3, "全部可用"),

    ;

    private Integer value;

    private String desc;

    public static StoreUsageModeEnum getByValue(Integer value) {
        for (StoreUsageModeEnum storeUsageModeEnum : StoreUsageModeEnum.values()) {
            if (storeUsageModeEnum.getValue().equals(value)) {
                return storeUsageModeEnum;
            }
        }
        return null;
    }
}
