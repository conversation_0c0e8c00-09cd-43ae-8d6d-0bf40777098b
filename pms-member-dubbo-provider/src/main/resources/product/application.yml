###属性的值命名格式 是 ${一站式应用标识(appUk)}
spring:
  application:
    name: titc.java.dubbo.pms.member

dubbo:
  application:
    name: dsf.dubbo.pms.member
    ###表示使用应用级服务注册
    register-mode: instance
    service-discovery:
      migration: FORCE_APPLICATION
  scan:
    ###Base packages to scan Dubbo Component: @com.alibaba.dubbo.config.annotation.Service
    basePackages: com.ly.titc.pms.member
  consumer:
    protocol: dsf
    check: false
    retries: 0
    timeout: 6000
  protocol:
    port: 20880
    ###必须使用dsf协议
    name: dsf
  registry:
    ###naming字段根据具体环境调整
    ###qa:tcbase_java_dsf_backend.sz.qa_default,
    ###stage: tcbase_java_dsf_backend.sz.stage_default,
    ###pro: tcbase_java_dsf_backend.sz.product_default
    address: dsf://sz.name.vip.17usoft.com?report-metadata=true&path=/naming/getNaming&naming=tcbase_java_dsf_backend.sz.product_default


#### retrofit 全局配置
retrofit:
  # 连接池配置
  pool:
    # cc连接池配置
    pms-member:
      # 最大空闲连接数
      max-idle-connections: 10
      # 连接保活时间(秒)
      keep-alive-second: 300
  # 全局连接超时时间
  global-connect-timeout-ms: 60_000
  # 全局读取超时时间
  global-read-timeout-ms: 15_000
  # 全局写入超时时间
  global-write-timeout-ms: 60_000
  # 全局完整调用超时时间
  global-call-timeout-ms: 0


####dal 自定义配置
dal:
  #dbName
  dbName: TETitcPmsMemberDCDB
  #初始化连接
  initialSize: 5
  #最大连接数量
  maxActive: 300
  #最小空闲连接
  minIdle: 5
  #超时等待时间以毫秒为单位
  maxWait: 5000
  #指明是否在从池中取出连接前进行检验,如果检验失败
  testOnBorrow: false
  #指明是否在归还到池中前进行检验
  testOnReturn: false
  #指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
  testWhileIdle: true
  #在空闲连接回收器线程运行期间休眠的时间值,以毫秒为单位
  #timeBetweenEvictionRunsMillis: 60000
  #连接在池中保持空闲而不被空闲连接回收器线程 1000 * 60 * 3
  #minEvictableIdleTimeMillis: 180000
  #连接的最大存活时间，如果连接的最大时间大于maxEvictableIdleTimeMillis，则无视最小连接数强制回收
  #maxEvictableIdleTimeMillis: 360000
  #当建立新连接时被发送给JDBC驱动的连接参数
  connectionProperties[allowMultiQueries]: true
  phyTimeoutMillis: 150000
  validationQuery: SELECT 1

###mybatis-plus 配置
mybatis-plus:
  global-config:
    db-config:
      id-type: auto
      field-strategy: not_empty
      table-underline: true
      db-type: mysql
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  mapper-locations: classpath:mapper/*Mapper.xml
  type-aliases-package: com.ly.titc.pms.member.dal.entity

###turbomq config
turbomq:
  name-srv-addr: mqnameserver.17usoft.com:9876
  producer-group: titc_pms_member_bps_producer_group
  ##生产组
  producer:
    nameserver: mqnameserver.17usoft.com:9876
    group: titc_pms_member_bps_producer_group

###旅智框架配置
titc:
  ###dal mapper scan
  dal-scan-package: com.ly.titc.pms.member.dal.dao*
  dal-entity-packages: com.ly.titc.pms.member.dal.entity.po
  dal-sql-log:
    enable: true
  ###Redisson分布式锁配置
  cache:
    project-name: titc.java.dubbo.pms.member
    cache-items:
      - cache-name: dubbo.pms.member.redis.instance
        isDefault: true
  ###敏感信息加密算法
  encrypt:
    algorithm: BASE64
    ###AES需要
    key:
    iv: