package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.order.*;
import com.ly.titc.pms.member.dubbo.entity.response.CreateOrderResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPayOrderResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPayUnifiedOrderResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberOrderDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberOrderConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpgradeMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.*;
import com.ly.titc.pms.member.mediator.entity.dto.recharge.MemberStoreRechargeDto;
import com.ly.titc.pms.member.mediator.service.MemberOrderMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/8 18:03
 */
@Slf4j
@Validated
@DubboService
public class MemberOrderDubboServiceImpl implements MemberOrderDubboService {

    @Resource(type = MemberOrderMedService.class)
    private MemberOrderMedService orderMedService;

    @Resource
    private MemberOrderConverter orderConverter;

    @Override
    public Response<CreateOrderResp> createRegisterOrder(CreateRegisterOrderReq request) {
        CreateOrderDto<RegisterMemberDto> orderDto = orderConverter.convert(request);
        CreateOrderResultDto resultDto = orderMedService.createOrder(orderDto);
        return Response.success(orderConverter.convert(resultDto));
    }

    @Override
    public Response<CreateOrderResp> createPurchaseCardOrder(CreatePurchaseCardOrderReq request) {
        CreateOrderDto<PurchaseCardDto> orderDto = orderConverter.convert(request);
        CreateOrderResultDto resultDto = orderMedService.createOrder(orderDto);
        return Response.success(orderConverter.convert(resultDto));
    }

    @Override
    public Response<CreateOrderResp> createUpgradeOrder(CreateUpgradeOrderReq request) {
        CreateOrderDto<UpgradeMemberDto> orderDto = orderConverter.convert(request);
        CreateOrderResultDto resultDto = orderMedService.createOrder(orderDto);
        return Response.success(orderConverter.convert(resultDto));
    }

    @Override
    public Response<CreateOrderResp> createStoreOrder(CreateStoreOrderReq request) {
        CreateOrderDto<MemberStoreRechargeDto> orderDto = orderConverter.convert(request);
        CreateOrderResultDto resultDto = orderMedService.createOrder(orderDto);
        return Response.success(orderConverter.convert(resultDto));
    }

    @Override
    public Response<MemberPayUnifiedOrderResp> payUnifiedOrder(MemberPayOrderReq request) {
        MemberPayUnifiedOrderDto resultDto = orderMedService.payUnifiedOrder(orderConverter.convert(request));
        return Response.success(orderConverter.convertUnified(resultDto));
    }

    @Override
    public Response<MemberPayOrderResp> getPayState(GetPayStateReq request) {
        PayOrderResultDto resultDto = orderMedService.getPayState(orderConverter.convert(request));
        return Response.success(orderConverter.convert(resultDto));
    }

    @Override
    public Response<CreateRefundOrderResp> refundOrder(RefundOrderReq request) {
        RefundOrderDto refundOrderDto = orderConverter.convert(request, request.getMasterType(), request.getMasterCode());
        refundOrderDto.setReason("退款");
        RefundResultDto refundResultDto = orderMedService.refundOrder(refundOrderDto);
        return Response.success(orderConverter.convert(refundResultDto));
    }

    @Override
    public Response<CreateRefundOrderResp> getRefundState(GetRefundStateReq request) {
        GetRefundStateDto getRefundStateDto = orderConverter.convert(request);
        RefundResultDto refundResultDto = orderMedService.getRefundState(getRefundStateDto);
        return Response.success(orderConverter.convert(refundResultDto));
    }
}
