package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.dubbo.entity.request.member.BlacklistMemberReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.CancelBlacklistMemberReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.ListBlacklistParamReq;
import com.ly.titc.pms.member.dubbo.entity.response.BlacklistInfoResp;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.BlacklistInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.BlacklistMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.CancelBlacklistMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.ListBlacklistParamDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7 16:20
 */
@Mapper(componentModel = "spring")
public interface MemberBlacklistConverter extends BaseConverter {
    BlacklistMemberDto convertRequestToDto(BlacklistMemberReq request);

    CancelBlacklistMemberDto convertRequestToDto(CancelBlacklistMemberReq request);

    ListBlacklistParamDto convertRequestToDto(ListBlacklistParamReq request);

    List<BlacklistInfoResp> convertDtoToResp(List<BlacklistInfoDto> list);

    @Mappings({
            @Mapping(target = "gmtCreate", source = "gmtCreate", qualifiedByName = "localDatetime2Str"),
            @Mapping(target = "gmtModified", source = "gmtModified", qualifiedByName = "localDatetime2Str")
    })
    BlacklistInfoResp convertDtoToResp(BlacklistInfoDto list);

}
