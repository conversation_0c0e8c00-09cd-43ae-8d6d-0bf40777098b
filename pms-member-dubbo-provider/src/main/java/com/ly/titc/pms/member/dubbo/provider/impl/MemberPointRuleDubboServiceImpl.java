package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.BaseMasterReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberPointSysConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.usageRule.QueryMemberMasterUsageReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MemberPointSysConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberPointUsageRuleResp;
import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.SavePointConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.DeletePointUsageReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.PagePointUsageRuleReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.SavePointUsageConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.UpdatePointUsageStateReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPointConfigResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPointUsageRuleInfoResp;
import com.ly.titc.pms.member.dubbo.entity.response.SavePointUsageRuleResultResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberPointRuleDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberPointRuleConverter;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.DeleteMemberUsageDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.MemberPointUsageRuleConfigSaveDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.MemberUsageRuleSaveResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.UpdateMemberUsageStateDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberPointUsageDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberSysConfigDecorator;
import com.ly.titc.pms.member.mediator.service.MemberPointUsageMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 积分可用规则
 *
 * <AUTHOR>
 * @date 2025/6/12 10:57
 */
@Slf4j
@Validated
@DubboService
public class MemberPointRuleDubboServiceImpl implements MemberPointRuleDubboService {

    @Resource
    private MemberPointRuleConverter memberPointRuleConverter;

    @Resource
    private MemberPointUsageDecorator memberPointUsageDecorator;

    @Resource
    private HotelDecorator hotelDecorator;

    @Resource
    private MemberPointUsageMedService memberPointUsageMedService;

    @Resource
    private MemberSysConfigDecorator memberSysConfigDecorator;


    @Override
    public Response<Boolean> setConfig(SavePointConfigReq request) {
        MemberPointSysConfigReq configReq = memberPointRuleConverter.convertRequestToReq(request);
        memberSysConfigDecorator.savePointConfig(configReq);
        return Response.success(true);
    }

    @Override
    public Response<MemberPointConfigResp> getConfig(BlocBaseReq request) {
        BaseMasterReq req = memberPointRuleConverter.convertRequestToReq(request);
        MemberPointSysConfigResp resp = memberSysConfigDecorator.getPointConfig(req);
        MemberPointConfigResp response = memberPointRuleConverter.convertRespToResponse(resp);
        return Response.success(response);
    }

    @Override
    public Response<Pageable<MemberPointUsageRuleInfoResp>> pageRule(PagePointUsageRuleReq request) {
        QueryMemberMasterUsageReq req = memberPointRuleConverter.convertRequestToReq(request);
        Pageable<MemberPointUsageRuleResp> resp = memberPointUsageDecorator.page(req);
        List<HotelBaseInfoResp> respList = hotelDecorator.listHotelBaseInfos(request.getBlocCode(), null);
        Map<Long, HotelBaseInfoResp> hotelRespMap = respList.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelVid, Function.identity()));
        return Response.success(PageableUtil.convert(resp, d -> memberPointRuleConverter.convertRespToResponse(d, hotelRespMap)));
    }

    @Override
    public Response<List<SavePointUsageRuleResultResp>> saveRule(SavePointUsageConfigReq request) {
        MemberPointUsageRuleConfigSaveDto dto = memberPointRuleConverter.convertRequestToDto(request);
        List<MemberUsageRuleSaveResultDto> results =  memberPointUsageMedService.save(dto);
        return Response.success(results.stream().map(memberPointRuleConverter::convertDtoToResponse).collect(Collectors.toList()));
    }

    @Override
    public Response<Boolean> updateState(UpdatePointUsageStateReq request) {
        UpdateMemberUsageStateDto dto = memberPointRuleConverter.convertRequestToDto(request);
        Boolean result= memberPointUsageMedService.updateState(dto);
        return Response.success(result);
    }

    @Override
    public Response<Boolean> deleteRule(DeletePointUsageReq request) {
        DeleteMemberUsageDto dto = memberPointRuleConverter.convertRequestToDto(request);
        Boolean result= memberPointUsageMedService.delete(dto);
        return Response.success(result);
    }

    @Override
    public Response<List<SavePointUsageRuleResultResp>> remind(BlocBaseReq request) {
        BaseDto dto = memberPointRuleConverter.convertRequestToDto(request);
        List<MemberUsageRuleSaveResultDto> results= memberPointUsageMedService.remind(dto);
        return Response.success(results.stream().map(memberPointRuleConverter::convertDtoToResponse).collect(Collectors.toList()));
    }
}
