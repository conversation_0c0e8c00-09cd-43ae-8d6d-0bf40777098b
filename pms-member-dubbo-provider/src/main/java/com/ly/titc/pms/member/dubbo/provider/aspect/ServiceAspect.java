package com.ly.titc.pms.member.dubbo.provider.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.BaseRespCodeEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.TraceNoUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;


/**
 * @ClassName: ServiceAspect
 * @Description:
 * <AUTHOR>
 * @Date 2019/5/27 15:37
 * @Version 1.0
 */
@Component
@Aspect
@Slf4j
@Order(1)
public class ServiceAspect {

    @Pointcut("within(@org.apache.dubbo.config.annotation.DubboService com.ly.titc.pms.member.dubbo.provider..*)")
    public void requestLog() {

    }

    @Around("requestLog()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {

        TraceNoUtil.initTraceNo();
        Map<String, Object> args = getNameAndValue(pjp);
        Signature signature = pjp.getSignature();
        String methodName = signature.getName();
        String className = pjp.getTarget().getClass().getName();
        log.info("[{}] [{}] request start, args:{}", className, methodName, JSON.toJSONString(args, SerializerFeature.WriteMapNullValue));
        long start = System.currentTimeMillis();
        Object obj;
        try {
            obj = pjp.proceed();
            log.info("[{}] [{}]，request end, cost：{}", className ,methodName, System.currentTimeMillis() - start);
        }  catch (ConstraintViolationException e) {
            log.error(e.getMessage(), e);
            Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
            if (CollectionUtils.isNotEmpty(violations)) {
                Object ebj = e.getConstraintViolations().toArray()[0];
                if (ebj instanceof ConstraintViolation) {
                    return Response.set(BaseRespCodeEnum.CODE_400.getCode(), ((ConstraintViolation) ebj).getMessage(), null);
                }
            }
            return Response.set(BaseRespCodeEnum.CODE_400.getCode(), e.getMessage(), null);
        } catch (ServiceException e) {
            log.warn(e.getMessage(), e);
            return Response.set(e.getErrorCode(), e.getMessage(), null);
        } finally {
            TraceNoUtil.clearTraceNo();
        }
        return obj;
    }

    @AfterThrowing(value = "requestLog()", throwing = "e")
    public void afterThrowing(Throwable e) {
        log.error(e.getMessage(), e);
    }

    /**
     * assemble map
     *
     * @param joinPoint
     * @return
     */
    private Map<String, Object> getNameAndValue(ProceedingJoinPoint joinPoint) {

        Map<String, Object> param = new HashMap<>();
        Object[] paramValues = joinPoint.getArgs();
        String[] paramNames = ((CodeSignature) joinPoint.getSignature()).getParameterNames();
        for (int i = 0; i < paramNames.length; i++) {
            param.put(paramNames[i], paramValues[i]);
        }
        return param;
    }

}
