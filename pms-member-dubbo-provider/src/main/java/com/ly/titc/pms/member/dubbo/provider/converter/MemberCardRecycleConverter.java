package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.dubbo.entity.request.member.RecycleMemberCardManualRequest;
import com.ly.titc.pms.member.dubbo.entity.response.RecycleMemberCardManualResponse;
import com.ly.titc.pms.member.mediator.entity.dto.member.RecycleMemberCardRequestDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.RecycleMemberCardResultDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 会员卡回收转换器
 *
 * <AUTHOR>
 * @date 2024/12/25 10:00
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MemberCardRecycleConverter {

    /**
     * 请求转换为DTO
     *
     * @param request 请求
     * @return DTO
     */
    RecycleMemberCardRequestDto convertToDto(RecycleMemberCardManualRequest request);

    /**
     * 结果DTO转换为响应
     *
     * @param result 结果DTO
     * @return 响应
     */
    RecycleMemberCardManualResponse convertToResponse(RecycleMemberCardResultDto result);
}
