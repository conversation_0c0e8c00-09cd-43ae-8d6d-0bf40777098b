package com.ly.titc.pms.member.dubbo.provider.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.ecrm.dubbo.entity.BaseMasterReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberRecordOPResultResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberTotalPointResp;
import com.ly.titc.pms.member.asset.dubbo.enums.PointActionItemEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.dubbo.entity.request.SelectBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.AdjustPointReq;
import com.ly.titc.pms.member.dubbo.entity.response.SelectValueResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberPointAssetDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberPointConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.AssetDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.MemberPointOpDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberSysConfigDecorator;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-2-5 10:12
 */
@Slf4j
@Validated
@DubboService
public class MemberPointAssetDubboServiceImpl implements MemberPointAssetDubboService {
    @Resource(type = AssetDecorator.class)
    private AssetDecorator assetDecorator;

    @Resource(type = MemberPointOpDecorator.class)
    private MemberPointOpDecorator memberPointOpDecorator;

    @Resource
    private MemberSysConfigDecorator memberSysConfigDecorator;

    @Resource
    private MemberMedService memberMedService;

    @Resource
    private MemberPointConverter memberPointConverter;

    @Override
    public Response<List<SelectValueResp>> selectPointItem(SelectBaseReq request) {
        String text = request.getText();
        List<SelectValueResp> list = new ArrayList<>();
        for (PointActionItemEnum itemEnum : PointActionItemEnum.values()) {
            SelectValueResp selectValueResp = new SelectValueResp();
            String actionItem = itemEnum.getActionItem();
            String actionItemDesc = itemEnum.getActionItemDesc();
            selectValueResp.setText(actionItemDesc).setValue(actionItem);
            if (org.springframework.util.StringUtils.isEmpty(text)) {
                list.add(selectValueResp);
            } else if (actionItemDesc.contains(text)) {
                list.add(selectValueResp);
            }
        }
        return Response.success(list);
    }

    @Override
    public Response<String> adjustPoint(AdjustPointReq request) {
        if (request.getScore() == 0) {
            throw new ServiceException("积分数不能为0", RespCodeEnum.CODE_400.getCode());
        }
        String recordeNo = "";
        if (request.getScore() > 0) {
            ReceiveMemberPointReq pointReq = memberPointConverter.convertReqToAddReq(request);
            if (StringUtils.isBlank(request.getExpireDate())) {
                String pointExpireDate = getPointExpireDate(request.getMemberNo());
                pointReq.setExpireDate(pointExpireDate);
            }
            MemberRecordOPResultResp resp = memberPointOpDecorator.receive(pointReq);
            recordeNo = resp.getRecordNo();
        } else {
            ConsumeMemberPointReq pointReq = memberPointConverter.convertReqToConsumeReq(request);
            MemberRecordOPResultResp resp = memberPointOpDecorator.consume(pointReq);
            recordeNo = resp.getRecordNo();
        }
        return Response.success(recordeNo);
    }

    @Override
    public Response<MemberRecordOPResultResp> receiveRollback(ReceiveRollBackMemberPointReq req) {
        MemberRecordOPResultResp resp = memberPointOpDecorator.receiveRollback(req);
        return Response.success(resp);
    }

    @Override
    public Response<List<MemberPointsFlowInfoResp>> listConsumeRecords(ListMemberPointConsumeForBusinessReq req) {
        List<MemberPointsFlowInfoResp> resps = memberPointOpDecorator.listConsumeRecords(req);
        return Response.success(resps);
    }

    @Override
    public Response<MemberTotalPointResp> getTotalAccountPoints(BaseMemberReq req) {
        MemberTotalPointResp usableMasterAccount = assetDecorator.getTotalAccountPoints(req);
        return Response.success(usableMasterAccount);
    }

    @Override
    public Response<MemberPointAccountResp> getUsableMasterAccount(GetMemberUsableReq req) {
        MemberPointAccountResp usableMasterAccount = assetDecorator.getPointUsableMasterAccount(req);
        return Response.success(usableMasterAccount);
    }

    @Override
    public Response<Pageable<MemberPointsFlowInfoResp>> pageMemberPointsFlow(PageMemberPointsFlowMemberReq req) {
        Pageable<MemberPointsFlowInfoResp> pageable = assetDecorator.pagePointRecord(req);
        return Response.success(pageable);
    }

    private String getPointExpireDate(String memberNo) {
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(memberNo);
        BaseMasterReq baseMasterReq = memberPointConverter.convertBaseMasterReq(memberInfo);
        return memberSysConfigDecorator.getPointExpireDate(baseMasterReq);
    }
}
