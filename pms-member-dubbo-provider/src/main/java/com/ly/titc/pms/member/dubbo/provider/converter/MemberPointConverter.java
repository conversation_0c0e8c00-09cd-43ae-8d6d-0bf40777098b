package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.ecrm.dubbo.entity.BaseMasterReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.ConsumeMemberPointReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.ReceiveMemberPointReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.AdjustPointReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.AddMemberPointReq;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberInfoDto;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2025/2/14 10:48
 */
@Mapper(componentModel = "spring")
public interface MemberPointConverter {
    ReceiveMemberPointReq convertReqToReq(AddMemberPointReq req);

    BaseMasterReq convertBaseMasterReq(MemberInfoDto memberInfo);

    AddMemberPointReq convertRequestToReq(AdjustPointReq request);

    ReceiveMemberPointReq convertReqToAddReq(AdjustPointReq request);

    ConsumeMemberPointReq convertReqToConsumeReq(AdjustPointReq request);
}
