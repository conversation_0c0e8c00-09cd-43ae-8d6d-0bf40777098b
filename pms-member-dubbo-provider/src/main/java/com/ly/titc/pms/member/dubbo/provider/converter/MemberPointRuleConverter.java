package com.ly.titc.pms.member.dubbo.provider.converter;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.BaseMasterReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberPointSysConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.usageRule.QueryMemberMasterUsageReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MemberPointSysConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberPointUsageRuleResp;
import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.SavePointConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.DeletePointUsageReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.PagePointUsageRuleReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.SavePointUsageConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.UpdatePointUsageStateReq;
import com.ly.titc.pms.member.dubbo.entity.response.*;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.DeleteMemberUsageDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.MemberPointUsageRuleConfigSaveDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.MemberUsageRuleSaveResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.UpdateMemberUsageStateDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/12 11:24
 */
@Mapper(componentModel = "spring")
public interface MemberPointRuleConverter {

    @Mappings({
            @Mapping(target = "masterType", constant = "1"),
            @Mapping(target = "masterCode", source = "blocCode")
    })
    QueryMemberMasterUsageReq convertRequestToReq(PagePointUsageRuleReq request);

    @Mappings({
            @Mapping(target = "scopePlatformChannels", source = "scopePlatformChannels", ignore = true),
            @Mapping(target = "scopeHotelCodes", source = "scopeHotelCodes", ignore = true),
            @Mapping(target = "usageHotelCodes", source = "usageHotelCodes", ignore = true),

    })
    MemberPointUsageRuleInfoResp convertBase(MemberPointUsageRuleResp resp);


    default MemberPointUsageRuleInfoResp convertRespToResponse(MemberPointUsageRuleResp resp, Map<Long, HotelBaseInfoResp> hotelRespMap) {
        MemberPointUsageRuleInfoResp ruleDto = convertBase(resp);
        List<CodeResp> scopePlatformChannels = new ArrayList<>();
        List<CodeResp> scopeHotelCodes = new ArrayList<>();
        List<CodeResp> usageHotelCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(resp.getScopePlatformChannels())) {
            resp.getScopePlatformChannels().forEach(s -> {
                PlatformChannelEnum channelEnum = PlatformChannelEnum.getByPlatformChannel(s);
                if (channelEnum != null) {
                    CodeResp codeDto = new CodeResp();
                    codeDto.setCode(s);
                    codeDto.setName(channelEnum.getPlatformChannelDesc());
                    codeDto.setParentCode(channelEnum.getPlatform());
                    codeDto.setParentName(channelEnum.getPlatformDesc());
                    scopePlatformChannels.add(codeDto);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(resp.getScopeHotelCodes())) {
            resp.getScopeHotelCodes().forEach(s -> {
                CodeResp codeDto = new CodeResp();
                codeDto.setCode(s);
                codeDto.setName(hotelRespMap.get(Long.valueOf(s)).getHotelName());
                scopeHotelCodes.add(codeDto);
            });
        }
        if (CollectionUtils.isNotEmpty(resp.getUsageHotelCodes())) {
            resp.getUsageHotelCodes().forEach(s -> {
                CodeResp codeDto = new CodeResp();
                codeDto.setCode(s);
                codeDto.setName(hotelRespMap.get(Long.valueOf(s)).getHotelName());
                usageHotelCodes.add(codeDto);
            });
        }
        ruleDto.setScopePlatformChannels(scopePlatformChannels);
        ruleDto.setScopeHotelCodes(scopeHotelCodes);
        ruleDto.setUsageHotelCodes(usageHotelCodes);
        return ruleDto;
    }

    MemberPointUsageRuleConfigSaveDto convertRequestToDto(SavePointUsageConfigReq request);

    SavePointUsageRuleResultResp convertDtoToResponse(MemberUsageRuleSaveResultDto resp);

    UpdateMemberUsageStateDto convertRequestToDto(UpdatePointUsageStateReq request);

    DeleteMemberUsageDto convertRequestToDto(DeletePointUsageReq request);

    BaseDto convertRequestToDto(BlocBaseReq request);

    @Mappings({
            @Mapping(target = "masterType", constant = "1"),
            @Mapping(target = "masterCode", source = "blocCode")
    })
    MemberPointSysConfigReq convertRequestToReq(SavePointConfigReq request);

    @Mappings({
            @Mapping(target = "masterType", constant = "1"),
            @Mapping(target = "masterCode", source = "blocCode")
    })
    BaseMasterReq convertRequestToReq(BlocBaseReq request);

    MemberPointConfigResp convertRespToResponse(MemberPointSysConfigResp resp);
}
