package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.data.*;
import com.ly.titc.pms.member.dubbo.entity.request.member.GetCheckInStatisticsReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.GetHotelCheckInStatisticsReq;
import com.ly.titc.pms.member.dubbo.entity.request.record.PageCheckinRecordReq;
import com.ly.titc.pms.member.dubbo.entity.request.record.PageLevelChangeRecordReq;
import com.ly.titc.pms.member.dubbo.entity.request.record.PagePurchaseCardRecordReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPayRegisterStatisticsResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberDataRecordDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberDataRecordConverter;
import com.ly.titc.pms.member.mediator.entity.dto.data.*;
import com.ly.titc.pms.member.mediator.service.MemberDataRecordMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/7 19:13
 */
@Slf4j
@Validated
@DubboService
public class MemberDataRecordDubboServiceImpl implements MemberDataRecordDubboService {

    @Resource
    private MemberDataRecordMedService memberDataRecordMedService;

    @Resource
    private MemberDataRecordConverter memberDataRecordConverter;

    @Override
    public Response<Pageable<CheckinRecordResp>> pageCheckinRecord(PageCheckinRecordReq request) {
        PageCheckinRecordDto dto = memberDataRecordConverter.convertRequestToDto(request);
        Pageable<CheckInRecordDto> pageable = memberDataRecordMedService.pageCheckinRecord(dto);
        return Response.success(Pageable.convert(pageable, pageable.getDatas().stream()
                .map(memberDataRecordConverter::convertDtoToResp).collect(Collectors.toList())));
    }

    @Override
    public Response<CheckInStatisticsResp> getCheckInStatistics(GetCheckInStatisticsReq request) {
        CheckInStatisticsDto dto = memberDataRecordMedService.getCheckInStatistics(request.getMemberNo());
        return Response.success(memberDataRecordConverter.convertDtoToResp(dto));
    }

    @Override
    public Response<CheckInStatisticsResp> getHotelCheckInStatistics(GetHotelCheckInStatisticsReq request) {
        CheckInStatisticsDto dto = memberDataRecordMedService.getHotelCheckInStatistics(request.getMemberNo(), request.getHotelCode());
        return Response.success(memberDataRecordConverter.convertDtoToResp(dto));
    }

    @Override
    public Response<Pageable<PurchaseCardRecordResp>> pagePurchaseCardRecord(PagePurchaseCardRecordReq request) {
        PagePurchaseCardRecordDto dto = memberDataRecordConverter.convertRequestToDto(request);
        Pageable<PurchaseCardRecordDto> pageable = memberDataRecordMedService.pagePurchaseCardRecord(dto);
        return Response.success(Pageable.convert(pageable, pageable.getDatas().stream()
                .map(memberDataRecordConverter::convertDtoToResp).collect(Collectors.toList())));
    }

    @Override
    public Response<Pageable<MemberLevelChangeRecordResp>> pageMemberLevelChangeRecord(PageLevelChangeRecordReq request) {
        PageLevelChangeRecordDto dto = memberDataRecordConverter.convertRequestToDto(request);
        Pageable<MemberLevelChangeRecordDto> pageable = memberDataRecordMedService.pageMemberLevelChangeRecord(dto);
        return Response.success(Pageable.convert(pageable, pageable.getDatas().stream()
                .map(memberDataRecordConverter::convertDtoToResp).collect(Collectors.toList())));
    }

    @Override
    public Response<MemberPayRegisterStatisticsResp> getMemberPayRegisterStatistics(GetMemberPayRegisterStatisticsReq request) {
        return Response.success(memberDataRecordMedService.getMemberPayStatistics(request));
    }
}
