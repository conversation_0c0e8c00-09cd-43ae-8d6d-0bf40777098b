package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.dubbo.entity.request.profile.*;
import com.ly.titc.pms.member.dubbo.entity.response.*;
import com.ly.titc.pms.member.mediator.entity.dto.profile.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @date 2024/11/6 10:52
 */
@Mapper(componentModel = "spring")
public interface MemberProfileConverter {

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    SaveMemberProfileAddressDto convertReqToDto(SaveCommonAddressReq req);

    MemberProfileAddressResp convertPoToResp(MemberProfileAddressInfoDto memberProfileAddressInfo);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    SaveMemberProfileInvoiceHeaderDto convertReqToDto(SaveCommonInvoiceHeaderReq req);

    MemberProfileInvoiceHeaderResp convertPoToResp(MemberProfileInvoiceHeaderInfoDto memberProfileInvoiceHeaderInfo);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    SaveMemberProfileOccupantsDto convertReqToDto(SaveCommonOccupantsReq req);

    MemberProfileOccupantsResp convertPoToResp(MemberProfileOccupantsInfoDto memberProfileOccupantsInfo);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    AddMemberProfileTagDto convertReqToDto(AddMemberTagReq req);

    MemberProfileTagResp convertPoToResp(MemberProfileTagInfoDto memberProfileTagInfo);

    MemberProfileTagGroupResp convertPoToResp(MemberProfileTagInfoGroupDto memberProfileTagInfo);

    BatchAddSingleMemberTagDto convertReqToDto(BatchAddMemberTagReq req);

    TagBaseDto convertReqToDto(MemberTagReq req);

    SimpleMemberProfileTagGroupResp convertPoToResp(SimpleMemberProfileTagInfoGroupDto memberProfileTagInfo);

    MemberProfileTagCountResp convertDtoToResp(MemberProfileTagCountDto memberProfileTagCountDto);
}
