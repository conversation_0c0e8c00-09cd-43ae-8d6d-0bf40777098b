package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.general.ListMemberIdentityReq;
import com.ly.titc.pms.member.dubbo.entity.request.general.ListMemberSourceReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberGeneralCardConfigResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberGeneralSourceResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberGeneralDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberGeneralConverter;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberGeneralCardConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberSourceDto;
import com.ly.titc.pms.member.mediator.service.MemberGeneralMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberGeneralDubboServiceImpl
 * @Date：2024-12-3 20:30
 * @Filename：MemberGeneralDubboServiceImpl
 */
@Slf4j
@Validated
@DubboService
public class MemberGeneralDubboServiceImpl implements MemberGeneralDubboService {

    @Resource
    private MemberGeneralMedService memberGeneralMedService;

    @Resource
    private MemberGeneralConverter memberGeneralConverter;

    @Override
    public Response<List<MemberGeneralSourceResp>> listMemberSource(ListMemberSourceReq req) {
        List<MemberSourceDto> memberSources = memberGeneralMedService.queryMemberSource(req.getMasterType(), req.getMasterCode(), req.getName());
        return Response.success(memberSources.stream().map(memberGeneralConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<List<MemberGeneralCardConfigResp>> listMemberIdentity(ListMemberIdentityReq req) {
        List<MemberGeneralCardConfigDto> memberConfigs = memberGeneralMedService.queryMemberIdentity(req.getMasterType(), req.getMasterCode(), req.getName());
        return Response.success(memberConfigs.stream().map(memberGeneralConverter::convertDtoToResp).collect(Collectors.toList()));
    }
}
