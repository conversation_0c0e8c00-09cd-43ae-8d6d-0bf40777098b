package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.dubbo.entity.request.member.IssueMemberCardManualRequest;
import com.ly.titc.pms.member.dubbo.entity.response.IssueMemberCardManualResponse;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardRequestDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardResultDto;
import org.mapstruct.Mapper;

/**
 * 会员卡手动发放转换器
 *
 * <AUTHOR>
 * @date 2025年06月26日
 */
@Mapper(componentModel = "spring")
public interface MemberCardManualConverter {

    /**
     * 将手动发放会员卡请求转换为DTO
     *
     * @param request 手动发放会员卡请求
     * @return 发放会员卡请求DTO
     */
    IssueMemberCardRequestDto convertToDto(IssueMemberCardManualRequest request);

    /**
     * 将发放会员卡结果DTO转换为手动发放会员卡响应
     *
     * @param result 发放会员卡结果DTO
     * @return 手动发放会员卡响应
     */
    IssueMemberCardManualResponse convertToResponse(IssueMemberCardResultDto result);
} 