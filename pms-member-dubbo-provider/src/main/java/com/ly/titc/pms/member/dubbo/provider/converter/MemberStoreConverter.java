package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.cashier.dubbo.entity.request.SelectReq;
import com.ly.titc.pms.member.dubbo.entity.request.SelectBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.order.PageMemberStoreRecordReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRechargeDetailResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRechargeRecordResp;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PageOrderQueryDto;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2025/2/8 11:22
 */
@Mapper(componentModel = "spring")
public interface MemberStoreConverter {
    PageOrderQueryDto convertRequestToDto(PageMemberStoreRecordReq request);

    MemberRechargeRecordResp convertDtoToResp(MemberRechargeOrderDto memberRechargeOrderDto);

    MemberRechargeDetailResp convertDtoToResp(MemberRechargeOrderDetailDto dto);

    SelectReq convertDtoToResp(SelectBaseReq request);
}
