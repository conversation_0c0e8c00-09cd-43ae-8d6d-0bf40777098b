package com.ly.titc.pms.member.dubbo.provider.config;

import com.ly.titc.springboot.mq.producer.TurboMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: shawn
 * @email: <EMAIL>
 * @date: 2024/3/11 18:58
 * @description:
 */
@Slf4j
@Configuration
public class TurboMQProducerConfig {


    /**
     * 生产组
     */
    @Value("${turbomq.producer.group}")
    private String producerGroup;
    /**
     * NameServer的地址
     */
    @Value("${turbomq.producer.nameserver}")
    private String nameServer;


    /**
     * 生产者
     * @return
     */
    @Bean(name = "producer",initMethod = "start",destroyMethod = "shutdown")
    public TurboMQProducer getTurboMQProducerBean() {

        TurboMQProducer producer = new TurboMQProducer();
        producer.setGroup(producerGroup);
        producer.setNameserver(nameServer);
        return producer;
    }
}
