package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.common.convert.BaseConverter;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.dubbo.entity.request.card.MemberCardInfoReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.GetUsableMemberReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.PageMemberReq;
import com.ly.titc.pms.member.dubbo.entity.request.member.UpdateBaseInfoReq;
import com.ly.titc.pms.member.dubbo.entity.request.register.MemberContactInfoReq;
import com.ly.titc.pms.member.dubbo.entity.request.register.MemberRegisterReq;
import com.ly.titc.pms.member.dubbo.entity.response.*;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * 会员转换
 *
 * <AUTHOR>
 * @date 2024/10/29 14:16
 */
@Mapper(componentModel = "spring")
public interface MemberConverter extends BaseConverter {

    @Mappings({
            @Mapping(target = "issueUser", source = "operator")
    })
    IssueMemberCardDto convertReqToDto(MemberCardInfoReq memberCardInfo, String operator);

    MemberRegisterResp convertDtoToResp(RegisterMemberResultDto registerResult);

    MemberInfoResp convertDtoToResp(MemberInfoDto memberInfoDto);

    MemberDetailInfoResp convertDtoToResp(MemberDetailDto memberDetailDto);

    MemberCardInfoResp convertPoToResp(MemberCardInfoDto cardInfo);

    @Mappings({
            @Mapping(target = "modifyUser", source = "operator")
    })
    void convertReqToPo(UpdateBaseInfoReq request, @MappingTarget MemberDetailDto memberInfo);

    MemberExtendInfo convertDtoToExtendPo(MemberDetailDto memberDetailDto);

    MemberInfo convertDtoToPo(MemberDetailDto memberDetailDto);

    PageMemberParamDto convertReqToBo(PageMemberReq request);

    MemberContactInfo convertDtoToPo(MemberContactInfoDto memberContactInfoDto);

    UpdateMemberInfoDto convertPoToDto(UpdateBaseInfoReq req);

    @Mappings({
            @Mapping(target = "memberContactInfo", source = "request.memberContactInfo"),
            @Mapping(target = "memberCardInfo", source = "request.memberCardInfo")
    })
    RegisterMemberDto convertReqToDto(MemberRegisterReq request);

    MemberContactInfoDto convertReqToDto(MemberContactInfoReq request);

    IssueMemberCardDto convertReqToDto(MemberCardInfoReq request);

    List<MemberInfoResp> convert(List<MemberInfoDto> dtos);

    List<MemberIdentityBaseInfoResp> convertBaseInfo(List<MemberIdentityBaseInfoDto> baseInfoDtos);

    GetUsableMemberDto convert(GetUsableMemberReq req);

    MemberDefaultCardFullInfoResp convertDtoToResp(MemberDefaultCardFullInfoDto dto);

}
