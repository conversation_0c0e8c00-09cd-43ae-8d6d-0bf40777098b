package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.dubbo.entity.request.member.*;
import com.ly.titc.pms.member.dubbo.entity.request.register.MemberRegisterReq;
import com.ly.titc.pms.member.dubbo.entity.response.*;
import com.ly.titc.pms.member.dubbo.enums.ConsistencyEnum;
import com.ly.titc.pms.member.dubbo.interfaces.MemberDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员Dubbo服务实现
 *
 * <AUTHOR>
 * @date 2024/10/29 13:47
 */
@Slf4j
@Validated
@DubboService
public class MemberDubboServiceImpl implements MemberDubboService {

    @Resource(type = MemberConverter.class)
    private MemberConverter memberConverter;

    @Resource(type = MemberMedService.class)
    private MemberMedService memberMedService;

    @Override
    public Response<MemberRegisterResp> register(MemberRegisterReq request) {
        RegisterMemberDto dto = memberConverter.convertReqToDto(request);
        RegisterMemberResultDto registerResult = memberMedService.register(dto);
        return Response.success(memberConverter.convertDtoToResp(registerResult));
    }

    @Override
    public Response<MemberInfoResp> getByMemberNo(GetByMemberNoReq request) {
        MemberInfoDto memberDetailDto = memberMedService.getByMemberNo(request.getMasterType(), request.getMasterCode(), request.getMemberNo());
        return Response.success(memberConverter.convertDtoToResp(memberDetailDto));
    }

    @Override
    public Response<MemberDetailInfoResp> getDetailByMemberNo(GetByMemberNoReq request) {
        Integer masterType = request.getMasterType();
        String masterCode = request.getMasterCode(), memberNo = request.getMemberNo();

        MemberDetailDto memberDetailDto = memberMedService.getDetailByMemberNo(masterType, masterCode, memberNo);
        if (memberDetailDto == null) {
            return Response.success(null);
        }

        return Response.success(memberConverter.convertDtoToResp(memberDetailDto));
    }

    @Override
    public Response<MemberDefaultCardFullInfoResp> getUsableMemberInfo(GetUsableMemberReq req) {
        MemberDefaultCardFullInfoDto dto= memberMedService.getUsableMemberInfo(memberConverter.convert(req));
        return Response.success(memberConverter.convertDtoToResp(dto));
    }

    @Override
    public Response<Pageable<MemberDetailInfoResp>> pageMember(PageMemberReq request) {
        PageMemberParamDto pageMemberParamBo = memberConverter.convertReqToBo(request);
        Pageable<MemberDetailDto> pageable = null;
        if (request.getQueryMode().equals(ConsistencyEnum.FINAL)) {
            pageable = memberMedService.pageMemberByFinalMode(pageMemberParamBo);
        } else {
            pageable = memberMedService.pageMemberByStrongMode(pageMemberParamBo);
        }
        return Response.success(PageableUtil.convert(pageable, memberConverter::convertDtoToResp));
    }

    @Override
    public Response<List<MemberDetailInfoResp>> listByMobiles(ListByMobileReq request) {
        Integer masterType = request.getMasterType(), state = request.getState();
        String masterCode = request.getMasterCode();
        List<String> mobiles = request.getMobiles();

        List<MemberDetailDto> memberInfos = memberMedService.listByMobiles(masterType, masterCode, mobiles, state);
        return Response.success(memberInfos.stream().map(memberConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<List<MemberDetailInfoResp>> listByIdNos(ListByIdNoReq request) {
        Integer masterType = request.getMasterType(), state = request.getState();
        String masterCode = request.getMasterCode();
        Integer idType = request.getIdType();
        List<String> idNos = request.getIdNos();

        List<MemberDetailDto> memberInfos = memberMedService.listByIdNos(masterType, masterCode, idType, idNos, state);
        return Response.success(memberInfos.stream().map(memberConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<List<MemberDetailInfoResp>> listByCardNos(ListByCardNoReq request) {
        Integer masterType = request.getMasterType(), state = request.getState();
        String masterCode = request.getMasterCode();
        List<String> cardNos = request.getCardNos();

        List<MemberDetailDto> memberInfos = memberMedService.listByCardNos(masterType, masterCode, cardNos, state);
        return Response.success(memberInfos.stream().map(memberConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<List<MemberDetailInfoResp>> listByMemberNos(ListByMemberNoReq request) {
        Integer masterType = request.getMasterType();
        String masterCode = request.getMasterCode();
        List<String> memberNos = request.getMemberNos();

        List<MemberDetailDto> memberInfos = memberMedService.listByMemberNos(masterType, masterCode, memberNos);
        return Response.success(memberInfos.stream().map(memberConverter::convertDtoToResp).collect(Collectors.toList()));
    }

    @Override
    public Response<String> updateBaseInfo(UpdateBaseInfoReq request) {

        UpdateMemberInfoDto dto = memberConverter.convertPoToDto(request);
        memberMedService.updateMember(dto);
        return Response.success(null);
    }

    @Override
    public Response<String> cancel(DeleteMemberReq request) {
        Integer masterType = request.getMasterType();
        String masterCode = request.getMasterCode(),
                memberNo = request.getMemberNo(),
                operator = request.getOperator();

        CancelMemberDto cancelMemberDto = new CancelMemberDto();
        cancelMemberDto.setMasterType(masterType);
        cancelMemberDto.setMasterCode(masterCode);
        cancelMemberDto.setMemberNo(memberNo);
        cancelMemberDto.setOperator(operator);
        memberMedService.cancelMember(cancelMemberDto);
        return Response.success(memberNo);
    }

    @Override
    public Response<String> recover(RecoverMemberReq request) {
        memberMedService.recoverMember(request.getMasterType(), request.getMasterCode(), request.getMemberNo(), request.getOperator());
        return Response.success(request.getMemberNo());
    }

    @Override
    public Response<String> changePassword(ChangePasswordReq request) {
        memberMedService.changePassword(request.getMemberNo(), request.getNewPassword(), request.getConfirmPassword(), request.getOperator());
        return Response.success(null);
    }

    @Override
    public Response<List<MemberIdentityBaseInfoResp>> fuzzyMatchMember(FuzzyMatchMemberReq req) {
        String mobile = "";
        String realName = "";
        //判断是否是全是数字如果都是数字只查询手机号
        if( NumberUtils.isDigits(req.getMemberFuzzy())){
            mobile = req.getMemberFuzzy();
        }else{
            realName = req.getMemberFuzzy();
        }
        List<MemberIdentityBaseInfoDto> baseInfoDtos = memberMedService.fuzzyMatchMember(req.getMasterType(), req.getMasterCode(), realName, mobile,req.getSize());
        return Response.success(memberConverter.convertBaseInfo(baseInfoDtos));
    }
}
