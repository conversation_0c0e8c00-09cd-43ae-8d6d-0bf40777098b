package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.dubbo.entity.response.MemberGeneralSourceResp;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberGeneralCardConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberSourceDto;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6 17:41
 */
@Mapper(componentModel = "spring")
public interface MemberGeneralConverter {

    MemberGeneralSourceResp convertDtoToResp(MemberSourceDto memberSource);

    MemberGeneralSourceResp convertDtoToResp(MemberGeneralCardConfigDto memberConfigs);

}
