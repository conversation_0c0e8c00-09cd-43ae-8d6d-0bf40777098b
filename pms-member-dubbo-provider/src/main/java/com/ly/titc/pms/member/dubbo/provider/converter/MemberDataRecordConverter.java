package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.dubbo.entity.request.common.BaseGearResp;
import com.ly.titc.pms.member.dubbo.entity.request.data.CheckInStatisticsResp;
import com.ly.titc.pms.member.dubbo.entity.request.data.CheckinRecordResp;
import com.ly.titc.pms.member.dubbo.entity.request.data.MemberLevelChangeRecordResp;
import com.ly.titc.pms.member.dubbo.entity.request.data.PurchaseCardRecordResp;
import com.ly.titc.pms.member.dubbo.entity.request.record.PageCheckinRecordReq;
import com.ly.titc.pms.member.dubbo.entity.request.record.PageLevelChangeRecordReq;
import com.ly.titc.pms.member.dubbo.entity.request.record.PagePurchaseCardRecordReq;
import com.ly.titc.pms.member.mediator.entity.dto.data.*;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.BaseGearDto;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7 19:26
 */
@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        builder = @Builder(disableBuilder = true))
public interface MemberDataRecordConverter {
    PageCheckinRecordDto convertRequestToDto(PageCheckinRecordReq request);

    CheckinRecordResp convertDtoToResp(CheckInRecordDto checkInRecordDto);

    CheckInStatisticsResp convertDtoToResp(CheckInStatisticsDto dto);

    PagePurchaseCardRecordDto convertRequestToDto(PagePurchaseCardRecordReq request);

    PurchaseCardRecordResp convertDtoToResp(PurchaseCardRecordDto purchaseCardRecordDto);

    PageLevelChangeRecordDto convertRequestToDto(PageLevelChangeRecordReq request);

    MemberLevelChangeRecordResp convertDtoToResp(MemberLevelChangeRecordDto memberLevelChangeRecordDto);

    default List<BaseGearResp> baseGearDtoListToBaseGearRespList(List<? extends BaseGearDto> list) {
        if ( list == null ) {
            return null;
        }

        List<BaseGearResp> list1 = new ArrayList<BaseGearResp>( list.size() );
        for ( BaseGearDto baseGearDto : list ) {
            list1.add( baseGearDtoToBaseGearResp( baseGearDto ) );
        }

        return list1;
    }

    BaseGearResp baseGearDtoToBaseGearResp(BaseGearDto baseGearDto);


}
